/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.device.entity.DeviceCurrentStatusEntity;
import org.skyworth.ess.device.vo.DeviceCurrentStatusVO;
import org.skyworth.ess.device.excel.DeviceCurrentStatusExcel;
import org.skyworth.ess.device.wrapper.DeviceCurrentStatusWrapper;
import org.skyworth.ess.device.service.IDeviceCurrentStatusService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import springfox.documentation.annotations.ApiIgnore;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * 设备/逆变器当前状态 控制器
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("skyworth-DeviceCurrentStatus/DeviceCurrentStatus")
@Api(value = "设备/逆变器当前状态", tags = "设备/逆变器当前状态接口")
public class DeviceCurrentStatusController extends BladeController {

	private final IDeviceCurrentStatusService DeviceCurrentStatusService;

	/**
	 * 设备/逆变器当前状态 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入DeviceCurrentStatus")
	public R<DeviceCurrentStatusVO> detail(DeviceCurrentStatusEntity DeviceCurrentStatus) {
		DeviceCurrentStatusEntity detail = DeviceCurrentStatusService.getOne(Condition.getQueryWrapper(DeviceCurrentStatus));
		return R.data(DeviceCurrentStatusWrapper.build().entityVO(detail));
	}
	/**
	 * 设备/逆变器当前状态 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入DeviceCurrentStatus")
	public R<IPage<DeviceCurrentStatusVO>> list(@ApiIgnore @RequestParam Map<String, Object> DeviceCurrentStatus, Query query) {
		IPage<DeviceCurrentStatusEntity> pages = DeviceCurrentStatusService.page(Condition.getPage(query), Condition.getQueryWrapper(DeviceCurrentStatus, DeviceCurrentStatusEntity.class));
		return R.data(DeviceCurrentStatusWrapper.build().pageVO(pages));
	}

	/**
	 * 设备/逆变器当前状态 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入DeviceCurrentStatus")
	public R<IPage<DeviceCurrentStatusVO>> page(DeviceCurrentStatusVO DeviceCurrentStatus, Query query) {
		IPage<DeviceCurrentStatusVO> pages = DeviceCurrentStatusService.selectDeviceCurrentStatusPage(Condition.getPage(query), DeviceCurrentStatus);
		return R.data(pages);
	}

	/**
	 * 设备/逆变器当前状态 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入DeviceCurrentStatus")
	public R save(@Valid @RequestBody DeviceCurrentStatusEntity DeviceCurrentStatus) {
		return R.status(DeviceCurrentStatusService.save(DeviceCurrentStatus));
	}

	/**
	 * 设备/逆变器当前状态 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入DeviceCurrentStatus")
	public R update(@Valid @RequestBody DeviceCurrentStatusEntity DeviceCurrentStatus) {
		return R.status(DeviceCurrentStatusService.updateById(DeviceCurrentStatus));
	}

	/**
	 * 设备/逆变器当前状态 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入DeviceCurrentStatus")
	public R submit(@Valid @RequestBody DeviceCurrentStatusEntity DeviceCurrentStatus) {
		return R.status(DeviceCurrentStatusService.saveOrUpdate(DeviceCurrentStatus));
	}

	/**
	 * 设备/逆变器当前状态 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(DeviceCurrentStatusService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-DeviceCurrentStatus")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入DeviceCurrentStatus")
	public void exportDeviceCurrentStatus(@ApiIgnore @RequestParam Map<String, Object> DeviceCurrentStatus, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DeviceCurrentStatusEntity> queryWrapper = Condition.getQueryWrapper(DeviceCurrentStatus, DeviceCurrentStatusEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(DeviceCurrentStatus::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(DeviceCurrentStatusEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<DeviceCurrentStatusExcel> list = DeviceCurrentStatusService.exportDeviceCurrentStatus(queryWrapper);
		ExcelUtil.export(response, "设备/逆变器当前状态数据" + DateUtil.time(), "设备/逆变器当前状态数据表", list, DeviceCurrentStatusExcel.class);
	}

}
