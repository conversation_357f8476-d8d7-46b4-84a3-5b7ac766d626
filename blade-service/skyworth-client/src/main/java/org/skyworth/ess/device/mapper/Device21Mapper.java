/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.app.vo.AppDeviceInfo;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.excel.Device21Excel;
import org.skyworth.ess.device.vo.Device21VO;

import java.util.List;
import java.util.Map;

/**
 * 设备/逆变器表，记录2.1数据 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public interface Device21Mapper extends BaseMapper<Device21Entity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param Device21
	 * @return
	 */
	List<Device21VO> selectDevice21Page(IPage page, Device21VO Device21);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<Device21Excel> exportDevice21(@Param("ew") Wrapper<Device21Entity> queryWrapper);


	List<Device21Entity> getEntity(@Param("ew") Wrapper<Device21Entity> queryWrapper);

	List<Device21Entity> getEntityIsDelete(@Param("plantId") Long plantId, @Param("deviceSerialNumber") String deviceSerialNumber);

	List<AppDeviceInfo> queryAppInverterInfo(@Param("plantId") Long plantId, @Param("deviceSerialNumber") String deviceSerialNumber);

	List<AppDeviceInfo> queryAppInverterInfoV2(@Param("plantId") Long plantId, @Param("deviceSerialNumber") String deviceSerialNumber);

	int updateByDeviceId(@Param("deviceSn") String deviceSn, @Param("status") String status);

	Device21Entity getCreateTime(@Param("de") Device21Entity device21Entity);

	int updateSetup(@Param("device21Map") Map<String, Object> device21, @Param("plantId") Long plantId, @Param("deviceSerialNumber") String deviceSerialNumber);

	List<Device21Entity> queryDevice21InfoBySerialNumber(@Param("serialNumbers") List<String> serialNumbers);

	List<Device21Entity> getListByDeviceSerialNumberCollect(@Param("list")List<String> deviceSerialNumberCollect);
}
