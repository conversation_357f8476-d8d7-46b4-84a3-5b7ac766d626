package org.skyworth.ess.battery.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum BatteryExitFactoryExcelColumnEnum {
	batterySerialNumber("Batterie SN", "电池SN"),
	batteryType("Gerätemodell", "电池型号"),
	company("Hersteller", "公司"),
	qualityGuaranteeYear("Garantiezeit", "质保年限"),

	exitFactoryDate("Fabrikdatum", "出厂日期"),
	ratedBatteryEnergy("Nennenergie(kWh)", "额定电池能量"),
	ratedBatteryCapacity("Nennleistung(Ah)", "额定电池容量"),
	ratedBatteryVoltage("Nennspannung(V)", "额定电池电压"),


	singleSeriesParallelingNumber("Enthält die Anzahl der Einzelzellreihen und Parallelverbindungen", "内含单体串并联数"),
	singleCapacity("Enthält Einzelzellenkapazität(Ah)", "单体容量");

	private String columnEn;
	private String columnCn;

	BatteryExitFactoryExcelColumnEnum(String columnEn, String columnCn) {
		this.columnEn = columnEn;
		this.columnCn = columnCn;
	}

	public static Set<String> getColumn(String language) {
		Set<String> result = new HashSet<>();
		for (BatteryExitFactoryExcelColumnEnum item : BatteryExitFactoryExcelColumnEnum.values()) {
			if (CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
				result.add(item.columnCn);
			} else {
				result.add(item.columnEn);
			}
		}
		return result;
	}
}
