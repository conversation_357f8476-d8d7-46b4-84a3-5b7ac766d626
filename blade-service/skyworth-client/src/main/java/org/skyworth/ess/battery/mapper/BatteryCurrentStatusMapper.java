/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.app.vo.AppBatteryCurrentStatusInfo;
import org.skyworth.ess.battery.entity.BatteryCurrentStatusEntity;
import org.skyworth.ess.battery.entity.QueryCondition;

import java.util.List;

/**
 * 电池当前状态 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
public interface BatteryCurrentStatusMapper extends BaseMapper<BatteryCurrentStatusEntity> {
	/**
	 * 根据plantId查询当前电池状态
	 *
	 * @param queryCondition 入参
	 * @return BatteryCurrentStatusEntity
	 * <AUTHOR>
	 * @since 2023/9/16 11:00
	 **/
	BatteryCurrentStatusEntity view(@Param("queryCondition") QueryCondition queryCondition);

	BatteryCurrentStatusEntity viewIsDelete(@Param("queryCondition") QueryCondition queryCondition);

	/**
	 * 查询电池信息
	 *
	 * @param plantId
	 * @return
	 */
	List<AppBatteryCurrentStatusInfo> queryAppBatteryInfo(@Param("plantId") Long plantId, @Param("deviceSerialNumber") String deviceSerialNumber);

	List<AppBatteryCurrentStatusInfo> queryAppBatteryInfoV2(@Param("plantId") Long plantId, @Param("deviceSerialNumber") String deviceSerialNumber);

	List<BatteryCurrentStatusEntity> batchQueryAppBatteryCurrentStatus(@Param("list") List<QueryCondition> list);

    int batchDeleteLogicByPlantId(@Param("list") List<Long> plantIdList, @Param("updateUserAccount") String updateUserAccount);

	int batchDeleteLogicByPlantIdAndSn(@Param("plantId")Long plantId, @Param("deviceSerialNumber") String deviceSerialNumber,@Param("updateUserAccount") String updateUserAccount);
}
