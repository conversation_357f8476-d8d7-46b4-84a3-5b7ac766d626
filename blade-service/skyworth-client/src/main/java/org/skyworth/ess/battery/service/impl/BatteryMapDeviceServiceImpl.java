package org.skyworth.ess.battery.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.app.service.impl.AppServiceImpl;
import org.skyworth.ess.battery.entity.BatteryDeviceInstallVO;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.mapper.BatteryMapDeviceMapper;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.battery.vo.BatteryCapacityVo;
import org.skyworth.ess.battery.vo.BatteryExitFactoryInfoVO;
import org.skyworth.ess.battery.vo.BatteryMapDeviceVO;
import org.skyworth.ess.battery.vo.BatteryPageResultVO;
import org.skyworth.ess.company.fegin.AgentClientBiz;
import org.skyworth.ess.event.entity.ImportantEventEntity;
import org.skyworth.ess.event.service.IImportantEventService;
import org.skyworth.ess.exception.service.IExceptionLogService;
import org.skyworth.ess.exception.vo.ExceptionLogVO;
import org.skyworth.ess.timeshift.ITimeShiftService;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.StatusDisplayUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 电池映射设备表(BatteryMapDeviceT)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-13 17:22:57
 */
@Service
@AllArgsConstructor
public class BatteryMapDeviceServiceImpl extends BaseServiceImpl<BatteryMapDeviceMapper, BatteryMapDeviceEntity> implements IBatteryMapDeviceService {

	private final IBatteryExitFactoryInfoService batteryExitFactoryInfoService;
	private final IImportantEventService importantEventService;
	private final IExceptionLogService exceptionLogService;
	private final AgentClientBiz agentClient;
	private final IUserSearchClient userSearchClient;
	private final ITimeShiftService timeShiftService;

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public BatteryMapDeviceVO queryById(Integer id) {
		return baseMapper.queryById(id);
	}

	/**
	 * 分页查询电池列表信息
	 *
	 * @param queryPageCondition 查询条件
	 * @param page               入参
	 * @return IPage<BatteryPageResult>
	 * <AUTHOR>
	 * @since 2023/9/15 9:25
	 **/
	@Override
	public IPage<BatteryPageResultVO> queryPage(QueryCondition queryPageCondition, IPage<BatteryPageResultVO> page) {
		//String companyName=queryPageCondition.getOperationCompanyName();
		/*List<AgentCompanyVO> agentCompanyVOList=new ArrayList<>();*/

		//搜索运维团队
		/*if(ValidationUtil.isNotEmpty(companyName)){
			agentCompanyVOList=agentClient.agentCompany(companyName).getData();
			if(ValidationUtil.isNotEmpty(agentCompanyVOList)&&!agentCompanyVOList.isEmpty()){
				String companyIds=agentCompanyVOList.stream().map(companyVO -> Func.toStr(companyVO.getDeptId())).distinct().collect(Collectors.joining(","));
				queryPageCondition.setOperationCompanyIds(Func.toLongList(companyIds));
			}else {
				return page.setRecords(new ArrayList<>());
			}
		}*/
		Map<Long,User> createUserMap = new HashMap<>();
		List<Long> createUserIdList = new ArrayList<>();
		//搜索用户名
		String realName = queryPageCondition.getCreatedByName();
		if (ValidationUtil.isNotEmpty(realName)) {
			List<User> userListByName = userSearchClient.listByRealName(realName).getData();
			if (!userListByName.isEmpty()) {
				List<Long> userIds =
						userListByName.stream().map(BaseEntity::getId).distinct().collect(Collectors.toList());
				createUserIdList.addAll(userIds);
				createUserMap.putAll(userListByName.stream().collect(Collectors.toMap(User::getId,
						Function.identity(), (a, b) -> a)));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}
		if(!createUserIdList.isEmpty()){
			queryPageCondition.setCreateUserIdList(createUserIdList);
		}
		//搜索运维人员
		List<User> userList=new ArrayList<>();
		String userName=queryPageCondition.getOperationUserName();
		if(ValidationUtil.isNotEmpty(userName)){
			userList=userSearchClient.listByRealName(userName).getData();
			if(ValidationUtil.isNotEmpty(userList)&&!userList.isEmpty()){
				String userIds=userList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				queryPageCondition.setOperationUserIds(Func.toLongList(userIds));
			}else {
				return page.setRecords(new ArrayList<>());
			}
		}


		//搜索手机号
		List<User> phoneList = new ArrayList<>();
		String phone=queryPageCondition.getPhone();
		if(ValidationUtil.isNotEmpty(phone)){
			phoneList=userSearchClient.listByPhone(phone).getData();
			if(ValidationUtil.isNotEmpty(phoneList)&&!phoneList.isEmpty()){
				String userIds=phoneList.stream().map(userVo -> Func.toStr(userVo.getId())).distinct().collect(Collectors.joining(","));
				queryPageCondition.setUserAndPhoneIds(Func.toLongList(userIds));
			}else {
				return page.setRecords(new ArrayList<>());
			}
		}

		BladeUser userAuth = AuthUtil.getUser();
		String deptId = AppServiceImpl.inspectInnerRole(userAuth);
		queryPageCondition.setCreateUser(userAuth.getUserId());
		queryPageCondition.setDeptId(deptId);
		// queryPageCondition新增删除标识
		// 获取用户类型，展示站点状态
		String userType = StatusDisplayUtil.getRoleType(userAuth.getRoleName(),userAuth.getDeptId());
		// queryPageCondition新增删除标识
		List<BatteryPageResultVO> batteryPageResultVOList = baseMapper.queryPage(queryPageCondition, page, userType);
		if(batteryPageResultVOList.isEmpty()){
			return page.setRecords(new ArrayList<>());
		}
		//电池额定总容量
		List<String> deviceSns=batteryPageResultVOList.stream().map(BatteryPageResultVO::getDeviceSerialNumber).collect(Collectors.toList());
		Map<String,BatteryCapacityVo> deviceSnMap;
		if(!CollectionUtils.isNullOrEmpty(deviceSns)){
			List<BatteryCapacityVo> devicesnList=baseMapper.queryBatteryCapacity(deviceSns);
			deviceSnMap = devicesnList.stream().collect(Collectors.toMap(BatteryCapacityVo::getDeviceSn, Function.identity(), (a, b) -> a));
		} else {
            deviceSnMap = new HashMap<>();
        }

        //用户信息
		Map<Long,User> createUserResultMap;
		if(org.springblade.common.utils.CollectionUtils.isNullOrEmpty(phoneList)){
			List<Long> createUserIds=batteryPageResultVOList.stream().map(BatteryPageResultVO::getCreateUser).collect(Collectors.toList());
			List<User> createUserRes=userSearchClient.listByUserIds(createUserIds).getData();
			if(!CollectionUtils.isNullOrEmpty(createUserRes)){
				createUserResultMap = createUserRes.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
			} else {
                createUserResultMap = new HashMap<>();
            }
        }else {
			createUserResultMap = phoneList.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
		}

		//运维团队信息
//		Map<Long, AgentCompanyVO> agentCompanyMap = new HashMap<>();
//		List<Long> companyIds =
//			batteryPageResultVOList.stream().map(BatteryPageResultVO::getOperationCompanyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//		if (!companyIds.isEmpty()){
//			List<AgentCompanyVO> agentCompanyList = agentClient.agentCompanyInfoByIds(companyIds).getData();
//			if (ObjectUtil.isNotNull(agentCompanyList)){
//				agentCompanyMap = agentCompanyList.stream().collect(Collectors.toMap(AgentCompanyVO::getDeptId, Function.identity(), (a, b) -> a));
//			}
//		}

		//运维人员信息
        Map<Long, User> userMap = new HashMap<>();
		if(org.springblade.common.utils.CollectionUtils.isNullOrEmpty(userList)){
			String userIds=batteryPageResultVOList.stream().map(UserVo -> Func.toStr(UserVo.getOperationUserId())).distinct().collect(Collectors.joining(","));
			List<User> userResult=userSearchClient.listByUserIds(Func.toLongList(userIds)).getData();
			if (!CollectionUtils.isNullOrEmpty(userResult)){
				userMap = userResult.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
			}
		} else {
			userMap = userList.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
        }



		Map<Long, User> finalUserMap = userMap;
//		Map<Long, AgentCompanyVO> finalAgentCompanyMap = agentCompanyMap;
		batteryPageResultVOList.parallelStream().forEach(v->{
//			if(finalAgentCompanyMap.containsKey(v.getOperationCompanyId())){
//				AgentCompanyVO agentCompanyVO = finalAgentCompanyMap.get(v.getOperationCompanyId());
//				if(ValidationUtil.isNotEmpty(agentCompanyVO)) {
//					v.setOperationCompanyName(agentCompanyVO.getCompanyName());
//				}
//			}
			if(finalUserMap.containsKey(v.getOperationUserId())){
				User user= finalUserMap.get(v.getOperationUserId());
				if(ValidationUtil.isNotEmpty(user)){
					v.setOperationUserName(user.getRealName());
				}
			}
			User user=createUserResultMap.get(v.getCreateUser());
			if(ValidationUtil.isNotEmpty(user)){
				v.setPhone(user.getPhone());
				v.setPhoneDiallingCode(user.getPhoneDiallingCode());
				v.setRealName(user.getRealName());
			}
			User userByName = createUserMap.get(v.getCreateUser());
			if(ValidationUtil.isNotEmpty(userByName)){
				v.setPhone(user.getPhone());
				v.setPhoneDiallingCode(user.getPhoneDiallingCode());
				v.setRealName(user.getRealName());
			}
			BatteryCapacityVo batteryCapacityVo=deviceSnMap.get(v.getDeviceSerialNumber());
			if(ValidationUtil.isNotEmpty(batteryCapacityVo)){
				v.setRatedBatteryCapacity(ValidationUtil.isEmpty(batteryCapacityVo.getCapacity())?"":batteryCapacityVo.getCapacity()+"");
			}else {
				v.setRatedBatteryCapacity("");
			}
			v.setStatus(StatusDisplayUtil.batteryStatusConvert(v.getStatus(), userType, v.getExistUserTypeAlarm(),
				v.getExistAgentTypeAlarm(),v.getBatteryPower()));
		});

//		if (ValidationUtil.isNotEmpty(queryPageCondition.getRealName())){
//			batteryPageResultVOList = batteryPageResultVOList.stream().filter(x -> x.getRealName().contains(queryPageCondition.getRealName())).collect(Collectors.toList());
//		}


		return page.setRecords(batteryPageResultVOList);
	}

	/**
	 * 查询储能电池列表查询汇总项
	 *
	 * @param queryPageCondition 入参
	 * @return Map<String, Object>
	 * <AUTHOR>
	 * @since 2023/9/15 14:30
	 **/
	@Override
	public Map<String, Object> listSummary(QueryCondition queryPageCondition) {
		//查询电池数量以及额定总能量
		Query query = new Query();
		query.setSize(100000);
		List<BatteryPageResultVO> records = this.queryPage(queryPageCondition,Condition.getPage(query)).getRecords();
		List<Long> collect = records.stream().map(BatteryPageResultVO::getPlantId).collect(Collectors.toList());
		if (!collect.isEmpty()){
			return baseMapper.listSummary(collect, queryPageCondition.getDeleteFlag());
		}else {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("totalCount",0);
			jsonObject.put("totalBatteryEnergy",0);
			return jsonObject;
		}
	}

	/**
	 * 安装信息
	 *
	 * @param queryPageCondition 入参
	 * @return BatteryDeviceInstallVO
	 * <AUTHOR>
	 * @since 2023/10/7 18:18
	 **/
	@Override
	public List<BatteryDeviceInstallVO> installation(QueryCondition queryPageCondition) {
		if (queryPageCondition == null) {
			throw new BusinessException("client.parameter.error.empty");
		}
		List<BatteryDeviceInstallVO> batteryDeviceInstallVOList=baseMapper.installation(queryPageCondition);
		if (CollectionUtils.isNullOrEmpty(batteryDeviceInstallVOList)) {
			batteryDeviceInstallVOList = baseMapper.installationIsDelete(queryPageCondition);
		}
		return batteryDeviceInstallVOList;
	}

	@Override
	public int batchDeleteLogicByPlantId(List<Long> plantIdList, String updateUserAccount) {
		return baseMapper.batchDeleteLogicByPlantId(plantIdList, updateUserAccount);
	}

	@Override
	public List<BatteryMapDeviceEntity> queryOwnerData(Long createUser) {
		return baseMapper.queryOwnerData(createUser);
	}

	@Override
	public int updateDataByCondition(BatteryMapDeviceEntity updateOwner) {
		return baseMapper.updateDataByCondition(updateOwner);
	}

	@Override
	public List<BatteryMapDeviceEntity> queryListByPlantId(List<Long> list) {
		return baseMapper.queryListByPlantId(list);
	}

	@Override
	public List<BatteryMapDeviceVO> queryBatteryDeviceInfo(BatteryMapDeviceEntity batteryMapDeviceEntity) {
		return baseMapper.queryBatteryDeviceInfo(batteryMapDeviceEntity);
	}

	@Override
	public List<BatteryExitFactoryInfoVO> deviceInformation(QueryCondition queryPageCondition) {
		// 通过逆变器SN和站点id获取电池列表
		List<BatteryExitFactoryInfoVO> batteryMapDeviceEntityList = batteryExitFactoryInfoService.deviceInformation(queryPageCondition);
		if (CollectionUtils.isNullOrEmpty(batteryMapDeviceEntityList) && "1".equals(queryPageCondition.getDeleteFlag())) {
			batteryMapDeviceEntityList = batteryExitFactoryInfoService.deviceInformationIsDelete(queryPageCondition);
		}
		Optional.ofNullable(batteryMapDeviceEntityList).orElse(new ArrayList<>()).forEach(a -> {
			a.setRunningDay(DateUtil.getDayBetweenTwoDate(a.getCreateTime(), new Date()));
		});
		return batteryMapDeviceEntityList;
	}

	@Override
	public IPage<ImportantEventEntity> importantEvents(QueryCondition queryCondition, IPage<ImportantEventEntity> page) {
		Wrapper<ImportantEventEntity> queryWrapper = Wrappers.<ImportantEventEntity>lambdaQuery().eq(ImportantEventEntity::getEventType, queryCondition.getEventType())
				.eq(ImportantEventEntity::getPlantId, queryCondition.getPlantId())
				.eq(ImportantEventEntity::getSerialNumber, queryCondition.getDeviceSerialNumber())
				.orderByDesc(ImportantEventEntity::getCreateTime);
		IPage<ImportantEventEntity> importantEventEntityIPage = importantEventService.page(page, queryWrapper);
		if (ObjectUtil.isNotNull(importantEventEntityIPage) && !CollectionUtils.isNullOrEmpty(importantEventEntityIPage.getRecords())){
			List<ImportantEventEntity> records = importantEventEntityIPage.getRecords();
			List<Long> plantIdList = records.stream().map(ImportantEventEntity::getPlantId).distinct().collect(Collectors.toList());
			timeShiftService.getAndReturnList(records,"eventDate",plantIdList);
			importantEventEntityIPage.setRecords(records);
		}

		return importantEventEntityIPage;
	}

	@Override
	public IPage<ExceptionLogVO> exceptionLog(QueryCondition queryCondition, IPage<ExceptionLogVO> page) {
        return exceptionLogService.selectExceptionLogPageByCondition(page, queryCondition);
	}
	@Override
	public List<BatteryMapDeviceEntity> queryListByPlantIdAndSnAndBattery(Long plantId, String deviceSerialNumber) {
		return baseMapper.queryListByPlantIdAndSnAndBattery(plantId,deviceSerialNumber);
	}

	@Override
	public List<BatteryMapDeviceEntity> queryListByPlantIdAndSn(Long plantId, String deviceSerialNumber) {
		return baseMapper.queryListByPlantIdAndSn(plantId,deviceSerialNumber);
	}

	@Override
	public void batchDeleteLogicByPlantIdAndSn(Long plantId, String deviceSerialNumber, String account) {
		baseMapper.batchDeleteLogicByPlantIdAndSn(plantId, deviceSerialNumber,account);
	}
}
