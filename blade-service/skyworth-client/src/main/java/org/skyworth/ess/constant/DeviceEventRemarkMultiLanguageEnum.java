package org.skyworth.ess.constant;


import java.util.Arrays;
import java.util.List;

public enum DeviceEventRemarkMultiLanguageEnum {
	PLANT_EVENT_CREATE(
		"",
		"",
		""),
	PLANT_EVENT_DELETE(
		"",
		"",
		""),
	INVERTER_EVENT_BIND_PLANT(
		"Benutzer erstellen:%s\n" +
		"Name der Energiestation:%s\n" +
		"Adresse der Energiestation:%s\n" +
		"Wechselrichter SN:%s",
		"Create User : %s\n" +
			"Energy Station Name : %s,\n" +
			"Energy Station Address : %s\n" +
			"Inverter SN : %s",
		""),
	INVERTER_EVENT_INFO_IMPORT(
		"Hersteller:%s\n" +
		"Garantiezeit:%s Jahre.",
		"Manufacturer : %s\n" +
			"Warranty period : %s years",
		""),
	INVERTER_EVENT_BIND_BATTERY(
		"die Batterie SN:%s\n" +
		"aktuelle Batteriezahl:%s\n" +
		"Gesamtenergie der aktuellen Batterie:%s kWh",
		"The Battery SN : %s\n" +
			"Current Battery Number : %s\n" +
			"Current Battery Rated Total Energy : %s kWh",
		""),
	INVERTER_EVENT_UNBIND_BATTERY(
		"die Batterie SN:%s\n" +
		"aktuelle Batteriezahl:%s\n" +
		"Gesamtenergie der aktuellen Batterie:%s kWh",
		"The Battery SN : %s\n" +
			"Current Battery Number : %s\n" +
			"Current Battery Rated Total Energy : %s kWh",
		""),
	BATTERY_EVENT_BIND_BATTERY(
		"die Batterie SN:%s",
		"The Battery SN : %s",
		""),
	BATTERY_EVENT_UNBIND(
		"die Batterie SN:%s",
		"the battery SN : %s",
		"");




	final String deEvent;
	final String enEvent;
	final String zhEvent;
	final static List<String> LANGUAGE_LIST = Arrays.asList("en","de","cn","jp");

	DeviceEventRemarkMultiLanguageEnum(String deEvent, String enEvent, String zhEvent) {
		this.deEvent = deEvent;
		this.enEvent = enEvent;
		this.zhEvent = zhEvent;
	}

	public String getLocalizedText(String language) {
		if (LANGUAGE_LIST.contains(language)){
			switch (language){
				case "en":
				case "jp":
					return enEvent;
				case "de":
					return deEvent;
				case "cn":
					return zhEvent;
			}
		}
		return enEvent;
	}




}

