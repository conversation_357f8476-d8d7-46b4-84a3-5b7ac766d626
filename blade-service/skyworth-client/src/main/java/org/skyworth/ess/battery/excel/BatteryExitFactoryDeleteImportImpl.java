package org.skyworth.ess.battery.excel;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.battery.entity.BatteryExitFactoryInfoEntity;
import org.skyworth.ess.battery.service.IBatteryExitFactoryInfoService;
import org.skyworth.ess.i18n.util.I18nUtil;
import org.springblade.common.excel.ExcelImportServiceAbstract;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class BatteryExitFactoryDeleteImportImpl extends ExcelImportServiceAbstract<BatteryExitFactoryInfoDeleteImportExcel> {
	private final IBatteryExitFactoryInfoService batteryExitFactoryInfoService;

	@Override
	public String validateDataEffective(List<BatteryExitFactoryInfoDeleteImportExcel> dataList) {
		if (dataList.isEmpty()){
			return I18nUtil.getString("client.battery.sn.empty");
		}
		List<String> batterySerialNumbers =
			dataList.stream().map(BatteryExitFactoryInfoDeleteImportExcel::getBatterySerialNumber).distinct().collect(Collectors.toList());
		List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntityLis =
			batteryExitFactoryInfoService.list(Wrappers.lambdaQuery(BatteryExitFactoryInfoEntity.class)
				.select(BatteryExitFactoryInfoEntity::getBatterySerialNumber, BatteryExitFactoryInfoEntity::getId,
					BatteryExitFactoryInfoEntity::getStatus)
				.in(BatteryExitFactoryInfoEntity::getBatterySerialNumber, batterySerialNumbers).eq(BatteryExitFactoryInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		StringBuilder errorReturn = new StringBuilder();
		// 检查设备出厂信息是否存在
		if (!batteryExitFactoryInfoEntityLis.isEmpty()) {
			// 如果入参中个数 和 DB中个数不相等，则标识录入SN不准确，则返回错误信息
			if (batterySerialNumbers.size() != batteryExitFactoryInfoEntityLis.size()) {
				validBatterySerialNumberIsExists(batteryExitFactoryInfoEntityLis, batterySerialNumbers, errorReturn);
			}
			// 检查设备出厂信息是否被使用，被使用不能删除
			List<String> usedBatterySerialNumbers =
				batteryExitFactoryInfoEntityLis.stream().filter(p -> p.getStatus() == BladeConstant.DB_STATUS_NORMAL).map(BatteryExitFactoryInfoEntity::getBatterySerialNumber).collect(Collectors.toList());
			if (!usedBatterySerialNumbers.isEmpty()) {
				String errorMsg = I18nUtil.getString("client.battery.sn.has.used");
				errorReturn.append(String.format(errorMsg, String.join(",", usedBatterySerialNumbers))).append(";");
			}
		} else {
			String errorMsg = I18nUtil.getString("client.battery.sn.not.exists");
			errorReturn.append(String.format(errorMsg, String.join(",", batterySerialNumbers))).append(";");
		}
		return errorReturn.toString();
	}

	/**
	 * 检查设备出厂信息是否存在
	 *
	 * @param batteryExitFactoryInfoEntityList 设备出厂信息列表
	 * @param batterySerialNumbers             设备出厂信息列表
	 * @param errorReturn                      错误信息
	 */
	private void validBatterySerialNumberIsExists(List<BatteryExitFactoryInfoEntity> batteryExitFactoryInfoEntityList,
												  List<String> batterySerialNumbers, StringBuilder errorReturn) {
		List<String> dbBatterySerialNumbers =
			batteryExitFactoryInfoEntityList.stream().map(BatteryExitFactoryInfoEntity::getBatterySerialNumber).collect(Collectors.toList());
		StringBuilder notExistError = new StringBuilder();
		batterySerialNumbers.forEach(p -> {
			if (!dbBatterySerialNumbers.contains(p)) {
				notExistError.append(p).append(",");
			}
		});
		// 检查末尾是否有逗号，有则删除
		if (notExistError.length() > 0 && notExistError.charAt(notExistError.length() - 1) == ',') {
			// 删除最后一个字符
			notExistError.deleteCharAt(notExistError.length() - 1);
		}
		String errorMsg = I18nUtil.getString("client.battery.sn.not.exists");
		errorReturn.append(String.format(errorMsg, notExistError)).append(";");
	}
}
