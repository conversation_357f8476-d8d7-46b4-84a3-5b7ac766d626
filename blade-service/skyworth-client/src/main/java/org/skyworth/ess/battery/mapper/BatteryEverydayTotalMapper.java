/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.battery.entity.BatteryEverydayTotalEntity;
import org.skyworth.ess.battery.entity.QueryCondition;
import org.skyworth.ess.battery.excel.BatteryEverydayTotalExcel;
import org.skyworth.ess.battery.vo.BatteryEverydayTotalVO;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.vo.DeviceLog22VO;

import java.util.List;

/**
 * 电池每日统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
public interface BatteryEverydayTotalMapper extends BaseMapper<BatteryEverydayTotalEntity> {
	/**
	 * 电池每日统计
	 *
	 * @param queryCondition 入参
	 * @return List<BatteryEverydayTotalEntity>
	 * <AUTHOR>
	 * @since 2023/9/16 15:30
	 **/
	List<JSONObject> dailyEstimate(@Param("queryCondition") QueryCondition queryCondition);

	/**
	 * 电池每日统计
	 *
	 * @param queryCondition 入参
	 * @return List<BatteryEverydayTotalEntity>
	 * <AUTHOR>
	 * @since 2023/9/16 15:30
	 **/
	List<BatteryEverydayTotalEntity> queryEverydayTotalByDates(@Param("queryCondition") QueryCondition queryCondition);

	/**
	 * 自定义分页
	 *
	 * @param page                 分页
	 * @param batteryEverydayTotal
	 * @return List<BatteryEverydayTotalVO>
	 */
	List<BatteryEverydayTotalVO> selectBatteryEverydayTotalPage(IPage page, BatteryEverydayTotalVO batteryEverydayTotal);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return List<BatteryEverydayTotalExcel>
	 */
	List<BatteryEverydayTotalExcel> exportBatteryEverydayTotal(@Param("ew") Wrapper<BatteryEverydayTotalEntity> queryWrapper);

	/**
	 * 电池每月统计
	 *
	 * @param queryCondition 入参
	 * @return List<BatteryEverydayTotalEntity>
	 * <AUTHOR>
	 * @since 2023/9/16 15:30
	 **/
	List<BatteryEverydayTotalVO> monthEstimate(@Param("queryCondition") QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> monthEstimateV2(@Param("queryCondition") QueryCondition queryCondition);

	/**
	 * 电池每月统计
	 *
	 * @param queryCondition 入参
	 * @return List<BatteryEverydayTotalEntity>
	 * <AUTHOR>
	 * @since 2023/9/16 15:30
	 **/
	List<BatteryEverydayTotalVO> annualEstimate(@Param("queryCondition") QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> annualEstimateV2(@Param("queryCondition") QueryCondition queryCondition);

	List<DeviceLog22VO> appReportEstimate(@Param("queryCondition") QueryDeviceLog22Condition queryCondition);

	List<BatteryEverydayTotalVO> weekEstimate(@Param("queryCondition") QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> weekEstimateV2(@Param("queryCondition") QueryCondition queryCondition);

	/**
	 * 电池每日统计  按月统计
	 *
	 * @param queryCondition 入参
	 * @return List<BatteryEverydayTotalEntity>
	 * <AUTHOR>
	 * @since 2023/9/16 15:30
	 **/
	List<JSONObject> dailyEstimateByMonth(@Param("queryCondition") QueryCondition queryCondition);

	BatteryEverydayTotalEntity pieReport(@Param("queryCondition") QueryCondition queryCondition);

	BatteryEverydayTotalEntity parallelPieReport(@Param("queryCondition") QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> parallelWeekEstimateV2(@Param("queryCondition") QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> parallelMonthEstimateV2(@Param("queryCondition") QueryCondition queryCondition);

	List<BatteryEverydayTotalVO> parallelAnnualEstimateV2(@Param("queryCondition") QueryCondition queryCondition);
}
