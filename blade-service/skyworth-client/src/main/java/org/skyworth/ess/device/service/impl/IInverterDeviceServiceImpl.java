package org.skyworth.ess.device.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.app.service.impl.AppServiceImpl;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;
import org.skyworth.ess.battery.service.IBatteryMapDeviceService;
import org.skyworth.ess.company.fegin.AgentClientBiz;
import org.skyworth.ess.dailyStatistics.entity.QueryDeviceLog22Condition;
import org.skyworth.ess.dailyStatistics.service.DeviceLog22ByDorisService;
import org.skyworth.ess.device.entity.*;
import org.skyworth.ess.device.mapper.InverterDeviceMapper;
import org.skyworth.ess.device.service.*;
import org.skyworth.ess.device.vo.*;
import org.skyworth.ess.plant.entity.PhotovoltaicPlantDeviceEntity;
import org.skyworth.ess.plant.service.IPhotovoltaicPlantDeviceService;
import org.skyworth.ess.plant.service.IPlantService;
import org.skyworth.ess.util.EveryDayPowerAndEnergyEnum;
import org.skyworth.ess.util.TimeZoneUtil;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DatabaseFieldConstant;
import org.springblade.common.constant.EntityFieldConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DataUnitConversionUtil;
import org.springblade.common.utils.StatusDisplayUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.cache.DictBizCache;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.Region;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/9/21 10:21:19
 */
@Service
@Slf4j
public class IInverterDeviceServiceImpl extends BaseServiceImpl<InverterDeviceMapper, InverterDevicePageVO> implements IInverterDeviceService {

	@Resource
	private IPhotovoltaicPlantDeviceService photovoltaicPlantDeviceService;
	@Resource
	private IBatteryMapDeviceService batteryMapDeviceService;
	@Resource
	private DeviceLog22ByDorisService deviceLog22ByDorisService;
	@Resource
	private IDeviceEverydayTotalService deviceEverydayTotalService;
	@Resource
	private IDeviceCurrentStatusService deviceCurrentStatusService;
	@Resource
	private IDevice23Service device23Service;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private IDeviceExitFactoryInfoService deviceExitFactoryInfoService;
	@Resource
	private IDevice21Service device21Service;
	@Resource
	private IUserSearchClient userSearchClient;
	@Resource
	private IUserClient userClient;
	@Resource
	private ISysClient sysClient;
	@Resource
	private AgentClientBiz agentClient;
	@Resource
	private IPlantService plantService;
	@Resource
	private TimeZoneUtil timeZoneUtil;

	public static final DateTimeFormatter ymFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
	public static final DateTimeFormatter ymdhmFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
	public static final DateTimeFormatter ymdFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
	public static final DateTimeFormatter hmFormatter = DateTimeFormatter.ofPattern("HH:mm");

	@Override
	public IPage<InverterDevicePageVO> selectInverterDevicePageByCountry(IPage<InverterDevicePageVO> page, InverterDevicePageVO inverterDevicePageVO) {
		//搜索运维人员条件
		List<Long> createUserIdList = new ArrayList<>();
		List<Long> operateUserIdList = new ArrayList<>();
		Map<Long,User> createUserMap = new HashMap<>();
		Map<Long,User> operateUserMap = new HashMap<>();
		//搜索用户名
		String createdByName = inverterDevicePageVO.getCreatedByName();
		if (ValidationUtil.isNotEmpty(createdByName)) {
			List<User> userListByName = userSearchClient.listByRealName(createdByName).getData();
			if (!userListByName.isEmpty()) {
				List<Long> userIds =
					userListByName.stream().map(BaseEntity::getId).distinct().collect(Collectors.toList());
				createUserIdList.addAll(userIds);
				createUserMap.putAll(userListByName.stream().collect(Collectors.toMap(User::getId,
					Function.identity(), (a, b) -> a)));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}
		//搜索用户手机号
		String phone = inverterDevicePageVO.getPhone();
		if (ValidationUtil.isNotEmpty(phone)) {
			List<User> userListByPhone = userSearchClient.listByPhone(phone).getData();
			if (!userListByPhone.isEmpty()) {
				List<Long> userIds =
					userListByPhone.stream().map(BaseEntity::getId).distinct().collect(Collectors.toList());
				createUserIdList.addAll(userIds);
				createUserMap.putAll(userListByPhone.stream().collect(Collectors.toMap(User::getId,
					Function.identity(), (a, b) -> a)));
			} else {
				return page.setRecords(new ArrayList<>());
			}
		}
		if(!createUserIdList.isEmpty()){
			inverterDevicePageVO.setCreateUserIdList(createUserIdList);
		}
		// 搜索运维人员
		String operationUserName=inverterDevicePageVO.getOperationUserName();
		if(ValidationUtil.isNotEmpty(operationUserName)){
			List<User> userListByOperation = userSearchClient.listByRealName(operationUserName).getData();
			if(!userListByOperation.isEmpty()){
				List<Long> userIds =
					userListByOperation.stream().map(BaseEntity::getId).distinct().collect(Collectors.toList());
				operateUserIdList.addAll(userIds);
				operateUserMap = userListByOperation.stream().collect(Collectors.toMap(User::getId,
					Function.identity(), (a, b) -> a));
			}else {
				return page.setRecords(new ArrayList<>());
			}
		}
		if(!operateUserIdList.isEmpty()){
			inverterDevicePageVO.setOperateUserIdList(operateUserIdList);
		}
		// 搜索
		BladeUser userAuth = AuthUtil.getUser();
		String deptId = AppServiceImpl.inspectInnerRole(userAuth);
		inverterDevicePageVO.setCreateUser(userAuth.getUserId());
		inverterDevicePageVO.setDeptId(deptId);
		String roleIds = userAuth.getRoleId();
		// 根据角色查询角色详情
		R<List<String>> roleNameR = sysClient.getRoleNames(roleIds);
		// client管理员角色才能查看已删除的数据
		if (CollectionUtil.isNotEmpty(roleNameR.getData()) && roleNameR.getData().contains("client_admin") && "1".equals(inverterDevicePageVO.getDeleteFlag())) {
			inverterDevicePageVO.setDeleteFlag("1");
		} else {
			inverterDevicePageVO.setDeleteFlag("0");
		}
		// inverterDevicePageVO新增删除标记，是否删除 0: 未删除 1: 已删除
		// 获取用户类型，展示站点状态
		String userType = StatusDisplayUtil.getRoleType(userAuth.getRoleName(),userAuth.getDeptId());
		// inverterDevicePageVO新增删除标记，是否删除 0: 未删除 1: 已删除
		List<InverterDevicePageVO> inverterDevicePageVOS = baseMapper.selectInverterDevicePageByCountry(page,
			inverterDevicePageVO, userType);
		if(inverterDevicePageVOS.isEmpty()){
			return  page.setRecords(new ArrayList<>());
		}
		//运维人员信息构建
		if(createUserMap.isEmpty()){
			List<Long> userIds =
				inverterDevicePageVOS.stream().map(InverterDevicePageVO::
					getCreatedBy).filter(Objects::nonNull).distinct().collect(Collectors.toList());
			List<User> userResult=userSearchClient.listByUserIds(userIds).getData();
			if (ObjectUtil.isNotNull(userResult)){
				createUserMap = userResult.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
			}
		}
		// 用户信息构建
		if(operateUserMap.isEmpty()){
			// 增加手机号
			List<Long> createUserCollect =
				inverterDevicePageVOS.stream().map(InverterDevicePageVO::getOperationUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
			List<User> listByUserIds = userSearchClient.listByUserIds(createUserCollect).getData();
			if(ObjectUtil.isNotNull(listByUserIds)){
				operateUserMap = listByUserIds.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
			}
		}

		// 增加详细地址
		List<String> regionCodeList = new ArrayList<>();
		//增加逆变器种类信息
		List<String> deviceSerialNumberCollect = inverterDevicePageVOS.stream().map(InverterDevicePageVO::getDeviceSerialNumber).distinct().collect(Collectors.toList());
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntityList = deviceExitFactoryInfoService.getListByDeviceSerialNumberCollect(deviceSerialNumberCollect);
		Map<String, String> deviceTypeMap = deviceExitFactoryInfoEntityList.stream().collect(Collectors.toMap(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getDeviceType));
		//增加逆变器相数信息
		List<Device21Entity> device21EntityList = device21Service.getListByDeviceSerialNumberCollect(deviceSerialNumberCollect);
		Map<String, String> devicePhaseMap = device21EntityList.stream().collect(Collectors.toMap(Device21Entity::getDeviceSerialNumber, Device21Entity::getGridPhaseNumber));

		for (InverterDevicePageVO inverter : inverterDevicePageVOS) {
			regionCodeList.add(inverter.getCountryCode());
			regionCodeList.add(inverter.getProvinceCode());
			regionCodeList.add(inverter.getCityCode());
			if (deviceTypeMap.containsKey(inverter.getDeviceSerialNumber())){
				inverter.setInverterKind(DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, deviceTypeMap.get(inverter.getDeviceSerialNumber())));
			}
			if (devicePhaseMap.containsKey(inverter.getDeviceSerialNumber())){
				inverter.setGridPhaseNumber(devicePhaseMap.get(inverter.getDeviceSerialNumber()));
			}
			// 封装创建人
			if (ObjectUtil.isNotNull(createUserMap)){
				User user = createUserMap.get(inverter.getCreatedBy());
				if (ObjectUtil.isNotNull(user)){
					inverter.setUserPhoneAndAreaCode(user.getPhoneDiallingCode() + " " + user.getPhone());
					inverter.setCreatedByName(user.getRealName());
				}
			}
			// 封装代理商名称
			Long operationUserId = inverter.getOperationUserId();
			if (ObjectUtil.isNotNull(operateUserMap) && operationUserId != null){
				User operationUser=operateUserMap.get(inverter.getOperationUserId());
				if (ObjectUtil.isNotNull(operationUser)) {
					inverter.setOperationUserName(operationUser.getRealName());
				}
			}
		}
		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			this.setRegionInfo(regionCodeList, inverterDevicePageVOS);
		} else {
			this.setRegionInfoEn(regionCodeList, inverterDevicePageVOS);
		}
		return page.setRecords(inverterDevicePageVOS);
	}

	private void setRegionInfoEn(List<String> regionCodeList, List<? extends AddressInfo> addressInfoList) {
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			List<Region> regionList = sysClient.getRegionList(regionCodeNotNullList).getData();
			Collections.reverse(regionList);
			for (AddressInfo addressInfo : addressInfoList) {
				StringBuilder address = new StringBuilder();
				if (CollectionUtils.isEmpty(regionList)) {
					break;
				}
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(addressInfo.getCountryCode())) {
						address.append(region.getName()).append(" ");
						addressInfo.setCountyName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(addressInfo.getCityCode())) {
						address.append(region.getName()).append(" ");
						addressInfo.setCityName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(addressInfo.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						addressInfo.setProvinceName(region.getName());
					}
				}
				addressInfo.setAddress(address.toString());
			}
		}
	}

	private void setRegionInfo(List<String> regionCodeList, List<? extends AddressInfo> addressInfoList) {
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(regionCodeNotNullList)) {
			List<Region> regionList = sysClient.getRegionList(regionCodeNotNullList).getData();
			for (AddressInfo addressInfo : addressInfoList) {
				StringBuilder address = new StringBuilder();
				if (CollectionUtil.isEmpty(regionList)) {
					break;
				}
				for (Region region : regionList) {
					if (region.getCode().equalsIgnoreCase(addressInfo.getCountryCode())) {
						address.append(region.getName()).append(" ");
						addressInfo.setCountryName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(addressInfo.getProvinceCode())) {
						address.append(region.getName()).append(" ");
						addressInfo.setProvinceName(region.getName());
					}
					if (region.getCode().equalsIgnoreCase(addressInfo.getCityCode())) {
						address.append(region.getName()).append(" ");
						addressInfo.setCityName(region.getName());
					}
					addressInfo.setAddress(address.toString());
				}
			}
		}
	}

	@Override
	public JSONObject getInverterOverviewByCountry(InverterDevicePageVO inverterDevicePageVO) {
		Query query = new Query();
		query.setSize(100000);
		List<InverterDevicePageVO> records = this.selectInverterDevicePageByCountry(Condition.getPage(query), inverterDevicePageVO).getRecords();
		List<Long> collect = records.stream().map(InverterDevicePageVO::getPlantId).collect(Collectors.toList());
		if (!collect.isEmpty()) {
			return baseMapper.getInverterOverviewByCountry(collect, inverterDevicePageVO.getDeleteFlag());
		} else {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("ratedPowerSum", 0);
			jsonObject.put("deviceCount", 0);
			return jsonObject;
		}

	}

	@Override
	public InverterDetailHeadVO getInverterDetailHead(Map<String, Object> detail) {
		InverterDetailHeadVO inverterDetailHead = baseMapper.getInverterDetailHead(detail);
		if (ObjectUtil.isEmpty(inverterDetailHead)) {
			inverterDetailHead = baseMapper.getInverterDetailHeadIsDelete(detail);
		}
		if (ObjectUtil.isEmpty(inverterDetailHead)) {
			return new InverterDetailHeadVO();
		}
		// 增加用户手机号
		String createdBy = inverterDetailHead.getCreatedBy();
		User user = userClient.userInfoById(Long.valueOf(createdBy)).getData();
		if (ObjectUtil.isNotNull(user)){
			inverterDetailHead.setUserPhoneAndAreaCode(user.getPhoneDiallingCode() + " " + user.getPhone());
			inverterDetailHead.setCreatedByName(user.getRealName());
		}

		// 增加详细地址
		List<String> regionCodeList = new ArrayList<>();
		regionCodeList.add(inverterDetailHead.getCountryCode());
		regionCodeList.add(inverterDetailHead.getProvinceCode());
		regionCodeList.add(inverterDetailHead.getCityCode());
		List<InverterDetailHeadVO> list = Collections.singletonList(inverterDetailHead);

		String currentLanguage = CommonUtil.getCurrentLanguage();
		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			this.setRegionInfo(regionCodeList, list);
		} else {
			this.setRegionInfoEn(regionCodeList, list);
		}

		return inverterDetailHead;
	}

	@Override
	public InverterDeviceInstallVO getInverterDetailInstallInfo(String deviceSerialNumber, String plantId) {
		HashMap<String, Object> inverterDeviceInstallVO = baseMapper.getPlantInstallInfo(deviceSerialNumber, plantId);
		if (ObjectUtil.isEmpty(inverterDeviceInstallVO)) {
			inverterDeviceInstallVO = baseMapper.getPlantInstallInfoByIsDelete(deviceSerialNumber, plantId);
		}
		InverterDeviceInstallVO installVO = new InverterDeviceInstallVO();

		if (ObjectUtil.isEmpty(inverterDeviceInstallVO)) {
			return installVO;
		}

		if (ObjectUtil.isNotEmpty(inverterDeviceInstallVO.get(EntityFieldConstant.INSTALL_DATE))) {
			LocalDateTime installDate = (LocalDateTime) inverterDeviceInstallVO.get(EntityFieldConstant.INSTALL_DATE);
			installVO.setInstallDate(LocalDateTimeUtil.format(installDate, DatePattern.NORM_DATETIME_PATTERN));
		}
//		if (ObjectUtil.isNotEmpty(inverterDeviceInstallVO.get("createUser"))) {
//			Long createUser = (Long) inverterDeviceInstallVO.get("createUser");
//			User user = userClient.userInfoById(createUser).getData();
//			if (ObjectUtil.isNotNull(user)){
//				installVO.setInstallPerson(user.getRealName());
//			}
//		}
//		if (ObjectUtil.isNotEmpty(inverterDeviceInstallVO.get(EntityFieldConstant.INSTALL_TEAM))) {
//			installVO.setInstallTeam((String) inverterDeviceInstallVO.get(EntityFieldConstant.INSTALL_TEAM));
//		}
		if (ObjectUtil.isNotEmpty(inverterDeviceInstallVO.get(EntityFieldConstant.QUALITY_GUARANTEE_YEAR))) {
			installVO.setQualityGuaranteeYear((String) inverterDeviceInstallVO.get(EntityFieldConstant.QUALITY_GUARANTEE_YEAR));
		}
		if (ObjectUtil.isNotEmpty(inverterDeviceInstallVO.get("warrantyStartDate"))) {
			installVO.setQualityGuaranteeBeginDate((String) inverterDeviceInstallVO.get("warrantyStartDate"));
		}
		if (ObjectUtil.isNotEmpty(inverterDeviceInstallVO.get(EntityFieldConstant.QUALITY_GUARANTEE_YEAR)) && ObjectUtil.isNotEmpty(inverterDeviceInstallVO.get("warrantyStartDate"))) {
			installVO.setQualityGuaranteeEndDate(this.addDate(Integer.parseInt((String)inverterDeviceInstallVO.get(EntityFieldConstant.QUALITY_GUARANTEE_YEAR))
					,(String)inverterDeviceInstallVO.get("warrantyStartDate")));
		}
		if (ObjectUtil.isNotNull(inverterDeviceInstallVO.get("deviceType"))){
			String value = DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, (String) inverterDeviceInstallVO.get("deviceType"));
			if (BizConstant.INVERT_KIND_PHOTOVOLTAIC_INVERTER.equals(value)){
				installVO.setInverterKind(BizConstant.INVERT_KIND_PHOTOVOLTAIC_INVERTER);
			}
		}

		QueryWrapper<PhotovoltaicPlantDeviceEntity> photovoltaicPlantDeviceEntityQueryWrapper = new QueryWrapper<>();
		photovoltaicPlantDeviceEntityQueryWrapper.eq(DatabaseFieldConstant.DEVICE_SERIAL_NUMBER, deviceSerialNumber).eq(DatabaseFieldConstant.PLANT_ID, plantId);
		List<PhotovoltaicPlantDeviceEntity> photovoltaicPlantDeviceEntityList = photovoltaicPlantDeviceService.list(photovoltaicPlantDeviceEntityQueryWrapper);
		Map<String, List<String>> deviceMap = photovoltaicPlantDeviceEntityList.stream()
				.collect(Collectors.groupingBy(PhotovoltaicPlantDeviceEntity::getMpptType,
						Collectors.mapping(PhotovoltaicPlantDeviceEntity::getPhotovoltaicSerialNumber, Collectors.toList())));

		HashMap<String, List<String>> mpptMap = new HashMap<>();
		mpptMap.put("mppt1", deviceMap.get("mppt1") == null ? null : deviceMap.get("mppt1"));
		mpptMap.put("mppt2", deviceMap.get("mppt2") == null ? null : deviceMap.get("mppt2"));
		mpptMap.put("mppt3", deviceMap.get("mppt3") == null ? null : deviceMap.get("mppt3"));
		mpptMap.put("mppt4", deviceMap.get("mppt4") == null ? null : deviceMap.get("mppt4"));
		installVO.setMpptInfo(mpptMap);


		QueryWrapper<BatteryMapDeviceEntity> batteryMapDeviceVoQueryWrapper = new QueryWrapper<>();
		batteryMapDeviceVoQueryWrapper.eq(DatabaseFieldConstant.PLANT_ID, plantId)
			 							.eq(DatabaseFieldConstant.DEVICE_SERIAL_NUMBER, deviceSerialNumber);
		List<BatteryMapDeviceEntity> batteryMapDeviceEntityList = batteryMapDeviceService.list(batteryMapDeviceVoQueryWrapper);
		installVO.setBatteryInfo(batteryMapDeviceEntityList);
		return installVO;
	}

	private String addDate(int num, String startDate) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		// 将日期字符串转换为LocalDate对象
		LocalDate date = LocalDate.parse(startDate, formatter);
		// 添加年数并计算总年限日期
		LocalDate totalYearsDate = date.plusYears(num);
		return totalYearsDate.toString();
	}
	private List<InvertStatusReport> getInvertStatusReport(InverterReportQueryVO queryCondition){
		// 获取web端用户选择时区
		String timeZone = timeZoneUtil.getUserWebTimeZone();
		Date startDateTime1 = queryCondition.getStartDateTime();
		Date endDateTime1 = queryCondition.getEndDateTime();
		// 设置默认时间
		if (startDateTime1 == null || endDateTime1 == null) {
			Calendar start = Calendar.getInstance();
			start.set(Calendar.HOUR_OF_DAY, 0);
			start.set(Calendar.MINUTE, 0);
			start.set(Calendar.SECOND, 0);
			startDateTime1 = start.getTime();
			Calendar end = Calendar.getInstance();
			end.set(Calendar.HOUR_OF_DAY, 23);
			end.set(Calendar.MINUTE, 59);
			end.set(Calendar.SECOND, 59);
			endDateTime1 = end.getTime();
		}
		QueryDeviceLog22Condition queryDeviceLog22Condition = new QueryDeviceLog22Condition();
		queryDeviceLog22Condition.setPlantId(queryCondition.getPlantId());
		queryDeviceLog22Condition.setDeviceSerialNumber(queryCondition.getDeviceSerialNumber());
		queryDeviceLog22Condition.setStartDateTime(startDateTime1);
		queryDeviceLog22Condition.setEndDateTime(endDateTime1);
		queryDeviceLog22Condition.setTimeZone(timeZoneUtil.removeTimeZoneLetters(timeZone));
		List<InvertStatusReport> statusList = deviceLog22ByDorisService.selectStatusReportByTime(queryDeviceLog22Condition);
		Map<String, InvertStatusReport> statusReportMap = statusList.stream().collect(Collectors.toMap(InvertStatusReport::getDeviceDateTimeForCal, e -> e, (oldValue, newValue) -> newValue));

		LocalDateTime startDateTime = startDateTime1.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().withNano(0);
		LocalDateTime endDateTime = endDateTime1.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().withNano(0);
		// 当前时间点
		LocalDateTime currentDateTime = startDateTime;
		// 每5分钟打一个点位，判断前面5分钟是否有数据，如16:01-16:05是否有数据?
		// 有数据则将数据则归并数据到时间点16:05
		// 没有数据则造一条数据
		// 注：查询条件的第一个数据，如每天的开始时间00:00，并且该分钟数是5的倍数，此时有一条数据上报，则会直接通过接口返回到前端，不需要走以下遍历
		while (currentDateTime.isBefore(endDateTime)) {
			// 下一个时间点
			LocalDateTime next5MinDateTime = currentDateTime.plusMinutes(5);
			// 如果下一个5分钟是晚上00点00分，则将next5MinDateTime设置为当天23:59，避免把数据点位打到第二天
			/*if (next5MinDateTime.toLocalTime().equals(LocalTime.MIDNIGHT)) {
				next5MinDateTime = currentDateTime.withHour(23).withMinute(55);
			}*/
			// LocalDateTime mergerTime = next5MinDateTime;
			// 判断deviceDateTimeForCal字段的数据是否在两个时间点内，在就取出Map<String, InvertStatusReport>的数据，放到集合
			List<InvertStatusReport> invertStatusReports = isBetween(currentDateTime, next5MinDateTime,
				statusReportMap);
			// 如果下一个5分钟是晚上00点00分
			/*if (currentDateTime.plusMinutes(5).toLocalTime().equals(LocalTime.MIDNIGHT)) {
				next5MinDateTime = next5MinDateTime.plusMinutes(1);
			}*/
			// 如果有这个时间点（16:01-16:05）的数据，则归并数据到时间点
			if (!invertStatusReports.isEmpty()) {
				// 归并数据到下一个时间点
				mergeData(currentDateTime,invertStatusReports,statusReportMap);
			}else{
				// 造数据
				InvertStatusReport fakeData = getFakeData(currentDateTime);
				statusReportMap.put(currentDateTime.format(ymdhmFormatter),fakeData);
			}
			// 更新当前时间点为下一个时间点
			currentDateTime = next5MinDateTime;
		}
		List<InvertStatusReport> invertStatusReports = new ArrayList<>(statusReportMap.values());
		invertStatusReports.sort(Comparator.comparing(report -> LocalDateTime.parse(report.getDeviceDateTimeForCal(), ymdhmFormatter)));
		return invertStatusReports;
	}

	@NotNull
	private InvertStatusReport getFakeData(LocalDateTime mergerTime) {
		// 没有数据则造一条数据
		InvertStatusReport fakeData = new InvertStatusReport();
		/*fakeData.setMppt1Power(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setMppt2Power(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setMppt3Power(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setMppt4Power(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseRWattOfGrid(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseSWattOfGrid(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseTWattOfGrid(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseRWattOfLoad(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseSWattOfLoad(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseTWattOfLoad(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseRWattOfEps(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseSWattOfEps(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
		fakeData.setPhaseTWattOfEps(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));*/
		fakeData.setDeviceDateTime(mergerTime.format(IInverterDeviceServiceImpl.hmFormatter));
		fakeData.setDeviceDateTimeForCal(mergerTime.format(IInverterDeviceServiceImpl.ymdhmFormatter));
		fakeData.setDeviceDateTimeForDay(mergerTime.format(IInverterDeviceServiceImpl.ymdFormatter));
		return fakeData;
	}

	@Override
	public Map<String, List<Object>> selectStatusReport(InverterReportQueryVO queryCondition) {
		List<InvertStatusReport> invertStatusReports = this.getInvertStatusReport(queryCondition);
		return processStatusListData(invertStatusReports);
	}

	@NotNull
	private Map<String, List<Object>> processStatusListData(List<InvertStatusReport> invertStatusReports) {
		Map<String, List<Object>> groupedData = new HashMap<>();

		// 获取所有字段名
		Field[] fields = InvertStatusReport.class.getDeclaredFields();
		// 提前设置访问权限，避免在循环中重复设置
		setFieldsAccessible(fields);
		for (Field field : fields) {
			try {
				String fieldName = field.getName();
				List<Object> fieldValueList = invertStatusReports.stream()
						.map(report -> {
							try {
								return field.get(report) == null ? "" : field.get(report);
							} catch (IllegalAccessException e) {
								log.error(e.getMessage());
								return "";
							}
						})
						.collect(Collectors.toList());

				groupedData.put(fieldName, fieldValueList);
			} catch (Exception e) {
				log.error("Error processing field " + field.getName() + ": " + e.getMessage());
			}
		}

		return groupedData;
	}

	// 设置字段可访问
	private void setFieldsAccessible(Field[] fields) {
		for (Field field : fields) {
			field.setAccessible(true);
		}
	}

	// 判断给定的数据时间是否在两个时间点之间
	private List<InvertStatusReport> isBetween(LocalDateTime start, LocalDateTime end, Map<String,
		InvertStatusReport> collect) {
		List<InvertStatusReport> invertStatusReports = new ArrayList<>();
		// 如果开始时间>结束时间，则跳过循环，返回上一层调用的方法。如00:06>00:05，则跳出循环，此时才能遍历完整从00:01-00:05分的数据，然后归并到00:05时间点上
		while (start.isBefore(end)){
			String formattedDateTime = start.format(IInverterDeviceServiceImpl.ymdhmFormatter);
			if (collect.containsKey(formattedDateTime)) {
				invertStatusReports.add(collect.get(formattedDateTime));
			}
			start = start.plusMinutes(1L);
		}
		return invertStatusReports;
	}

	// 归并数据到指定的时间点
	private void mergeData(LocalDateTime dateTime, List<InvertStatusReport> invertStatusReports, Map<String, InvertStatusReport> collect) {
		// 实现归并数据的逻辑
		InvertStatusReport sumReport = new InvertStatusReport();
		if (invertStatusReports.size() == 1){
			InvertStatusReport invertStatusReport = invertStatusReports.get(0);
			sumReport = invertStatusReport;
			setInverterValue(sumReport, invertStatusReport);
			collect.remove(sumReport.getDeviceDateTimeForCal());
		}else if (invertStatusReports.size() > 1){
			//取时间最新那一条
			invertStatusReports.sort(Comparator.comparing(report -> LocalDateTime.parse(report.getDeviceDateTimeForCal(), ymdhmFormatter)));
			InvertStatusReport invertStatusReport = invertStatusReports.get(invertStatusReports.size() - 1);
			setInverterValue(sumReport, invertStatusReport);
			invertStatusReports.forEach(a-> collect.remove(a.getDeviceDateTimeForCal()));
		}


		String ymdHmTime = dateTime.format(IInverterDeviceServiceImpl.ymdhmFormatter);
		String hmTime = dateTime.format(IInverterDeviceServiceImpl.hmFormatter);
		String ymd = dateTime.format(IInverterDeviceServiceImpl.ymdFormatter);
		sumReport.setDeviceDateTimeForCal(ymdHmTime);
		sumReport.setDeviceDateTime(hmTime);
		sumReport.setDeviceDateTimeForDay(ymd);
		collect.put(ymdHmTime, sumReport);
	}

	private void setInverterValue(InvertStatusReport sumReport, InvertStatusReport invertStatusReport) {
		sumReport.setMppt1Power(formatDecimal(invertStatusReport.getMppt1Power(), 2));
		sumReport.setMppt2Power(formatDecimal(invertStatusReport.getMppt2Power(), 2));
		sumReport.setMppt3Power(formatDecimal(invertStatusReport.getMppt3Power(), 2));
		sumReport.setMppt4Power(formatDecimal(invertStatusReport.getMppt4Power(), 2));
		sumReport.setPhaseRWattOfGrid(formatDecimal(invertStatusReport.getPhaseRWattOfGrid(), 2));
		sumReport.setPhaseSWattOfGrid(formatDecimal(invertStatusReport.getPhaseSWattOfGrid(), 2));
		sumReport.setPhaseTWattOfGrid(formatDecimal(invertStatusReport.getPhaseTWattOfGrid(), 2));
		sumReport.setPhaseRWattOfLoad(formatDecimal(invertStatusReport.getPhaseRWattOfLoad(), 2));
		sumReport.setPhaseSWattOfLoad(formatDecimal(invertStatusReport.getPhaseSWattOfLoad(), 2));
		sumReport.setPhaseTWattOfLoad(formatDecimal(invertStatusReport.getPhaseTWattOfLoad(), 2));
		sumReport.setPhaseRWattOfEps(formatDecimal(invertStatusReport.getPhaseRWattOfEps(), 2));
		sumReport.setPhaseSWattOfEps(formatDecimal(invertStatusReport.getPhaseSWattOfEps(), 2));
		sumReport.setPhaseTWattOfEps(formatDecimal(invertStatusReport.getPhaseTWattOfEps(), 2));
		sumReport.setL1PhasePowerOfAcCouple(formatDecimal(invertStatusReport.getL1PhasePowerOfAcCouple(), 2));
		sumReport.setL2PhasePowerOfAcCouple(formatDecimal(invertStatusReport.getL2PhasePowerOfAcCouple(), 2));
		sumReport.setL3PhasePowerOfAcCouple(formatDecimal(invertStatusReport.getL3PhasePowerOfAcCouple(), 2));
	}

	private BigDecimal formatDecimal(BigDecimal value, int scale) {
		DecimalFormat decimalFormat = new DecimalFormat("#0." + "0".repeat(scale));
		return new BigDecimal(decimalFormat.format(value));
	}


	@Override
	public InverterDayReport selectEveryDayStat(InverterReportQueryVO queryCondition) {
		String inverterKind = judgeInverterKind(queryCondition.getDeviceSerialNumber());
		InverterDayReport inverterDayReport = new InverterDayReport();
		List<List<String>> resultList = new ArrayList<>();
		String currentLanguage = CommonUtil.getCurrentLanguage();
		List<String> typeList;
		if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
			typeList = Arrays.asList("type", "发电能量(kWh)", "电网输入能量(kWh)", "馈网能量(kWh)", "一般负载能量(kWh)","关键负载能量(kWh)",
				"其他光伏(kWh)");
		} else {
			typeList = Arrays.asList("type",
					EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage, EveryDayPowerAndEnergyEnum.PVGeneration.getCode()),
					EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage, EveryDayPowerAndEnergyEnum.gridConsumption.getCode()),
					EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage, EveryDayPowerAndEnergyEnum.FeedInGrid.getCode()),
					EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage, EveryDayPowerAndEnergyEnum.loadConsumption.getCode()),
					EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage,
						EveryDayPowerAndEnergyEnum.backupConsumption.getCode()),
					EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage,
						EveryDayPowerAndEnergyEnum.otherPVConsumption.getCode())
				);
		}
		if (BizConstant.INVERT_KIND_PHOTOVOLTAIC_INVERTER.equals(inverterKind)){
			if (CommonConstant.CURRENT_LANGUAGE_ZH.equals(currentLanguage)) {
				typeList = Arrays.asList("type", "发电能量(kWh)", "馈网能量(kWh)", "一般负载能量(kWh)", "其他光伏(kWh)");
			} else {
				typeList = Arrays.asList("type",
						EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage, EveryDayPowerAndEnergyEnum.PVGeneration.getCode()),
						EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage, EveryDayPowerAndEnergyEnum.FeedInGrid.getCode()),
						EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage,
							EveryDayPowerAndEnergyEnum.loadConsumption.getCode()),
						EveryDayPowerAndEnergyEnum.matchLanguage(currentLanguage,
							EveryDayPowerAndEnergyEnum.otherPVConsumption.getCode()));
			}
		}
		// 获取web端用户选择时区
		String timeZone = timeZoneUtil.getUserWebTimeZone();
		Map<String, DeviceEverydayTotalEntity> collect;
		LocalDate startDateTime = queryCondition.getStartDateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		LocalDate endDateTime = queryCondition.getEndDateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		queryCondition.setTimeZone(timeZoneUtil.removeTimeZoneLetters(timeZone));
		if (ObjectUtil.isNull(queryCondition.getEveryDayStatQueryType()) || queryCondition.getEveryDayStatQueryType().equals(1)){
			List<DeviceEverydayTotalEntity> list = deviceEverydayTotalService.queryDayReport(queryCondition);
			collect = list.stream()
					.collect(Collectors.toMap(DeviceEverydayTotalEntity::getTotalDate,
							entity -> entity,
							(existingValue, newValue) -> existingValue));
			LocalDate sldt = startDateTime;
			LocalDate eldt = endDateTime.plusDays(1);
			//没有的则进行补
			while (!sldt.isEqual(eldt)) {
				buildEveryStat(collect, resultList, sldt,1,inverterKind);
				sldt = sldt.plusDays(1);
			}
		}else if (queryCondition.getEveryDayStatQueryType().equals(2)){
			List<DeviceEverydayTotalEntity> list = deviceEverydayTotalService.queryMonthReport(queryCondition);
			// 过滤掉空值
			collect = list.stream()
					.filter(Objects::nonNull)
					.collect(Collectors.toMap(DeviceEverydayTotalEntity::getTotalDate,
							entity -> entity));
			LocalDate sldt = startDateTime;
			LocalDate eldt = endDateTime;
			//没有的则进行补
			while (!sldt.isAfter(eldt)) {
				buildEveryStat(collect, resultList, sldt,2,inverterKind);
				sldt = sldt.plusMonths(1);
			}
		}
		inverterDayReport.setTypeList(typeList);
		inverterDayReport.setDataList(resultList);
		return inverterDayReport;
	}


	private void buildEveryStat(Map<String, DeviceEverydayTotalEntity> collect, List<List<String>> resultList, LocalDate sldt,Integer everyDayStatQueryType,String inverterKind ) {
		List<String> objects = new ArrayList<>();
		String sldtString = sldt.toString();

		String stringYm = sldt.format(ymFormatter);
		String dataTime = everyDayStatQueryType.equals(2) ? stringYm : sldtString;
		if (collect.containsKey(dataTime)) {
			DeviceEverydayTotalEntity deviceEverydayTotalEntity = collect.get(dataTime);
			objects.add(dataTime);

			BigDecimal todayEnergy = Optional.ofNullable(deviceEverydayTotalEntity.getTodayEnergy()).orElse(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			objects.add(StrUtil.toString(todayEnergy.divide(new BigDecimal(1000),2, RoundingMode.HALF_UP)));

			if (!BizConstant.INVERT_KIND_PHOTOVOLTAIC_INVERTER.equals(inverterKind)){
				BigDecimal todayImportEnergy = Optional.ofNullable(deviceEverydayTotalEntity.getTodayImportEnergy()).orElse(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
				objects.add(StrUtil.toString(todayImportEnergy.divide(new BigDecimal(1000),2, RoundingMode.HALF_UP)));
			}

			BigDecimal todayExportEnergy = Optional.ofNullable(deviceEverydayTotalEntity.getTodayExportEnergy()).orElse(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			objects.add(StrUtil.toString(todayExportEnergy.divide(new BigDecimal(1000),2, RoundingMode.HALF_UP)));

			BigDecimal todayLoadEnergy = Optional.ofNullable(deviceEverydayTotalEntity.getTodayLoadEnergy()).orElse(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			objects.add(StrUtil.toString(todayLoadEnergy.divide(new BigDecimal(1000),2, RoundingMode.HALF_UP)));

			if (!BizConstant.INVERT_KIND_PHOTOVOLTAIC_INVERTER.equals(inverterKind)){
				BigDecimal dailyEnergyToEps = Optional.ofNullable(deviceEverydayTotalEntity.getDailyEnergyToEps()).orElse(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
				objects.add(StrUtil.toString(dailyEnergyToEps.divide(new BigDecimal(1000),2, RoundingMode.HALF_UP)));
			}
			BigDecimal energyTodayOfAcCoupleWh =
				Optional.ofNullable(deviceEverydayTotalEntity.getEnergyTodayOfAcCoupleWh()).orElse(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE));
			objects.add(StrUtil.toString(energyTodayOfAcCoupleWh.divide(new BigDecimal(1000),2, RoundingMode.HALF_UP)));
			resultList.add(objects);
		}else{
			objects.add(dataTime);
			objects.add(StrUtil.toString(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE)));
			objects.add(StrUtil.toString(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE)));
			objects.add(StrUtil.toString(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE)));
			if (!BizConstant.INVERT_KIND_PHOTOVOLTAIC_INVERTER.equals(inverterKind)){
				objects.add(StrUtil.toString(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE)));
				objects.add(StrUtil.toString(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE)));
			}
			objects.add(StrUtil.toString(new BigDecimal(BizConstant.BIGDECIMAL_DEFAULT_VALUE)));
			resultList.add(objects);
		}

	}
	@Override
	public JSONObject selectEnergyTotalStat(InverterReportQueryVO queryCondition) {
		return deviceCurrentStatusService.selectEnergyTotalStat(queryCondition);
	}

	@Override
	public Device23Entity getDetailParameter(Device23Entity device23Entity) {
		LambdaQueryWrapper<Device23Entity> eq = Wrappers.<Device23Entity>query().lambda().eq(Device23Entity::getPlantId, device23Entity.getPlantId())
				.eq(Device23Entity::getDeviceSerialNumber, device23Entity.getDeviceSerialNumber());
		Device23Entity device23 = device23Service.getOne(eq);

		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = new DeviceExitFactoryInfoEntity();
		deviceExitFactoryInfoEntity.setDeviceSerialNumber(device23Entity.getDeviceSerialNumber());
		DeviceExitFactoryInfoEntity deviceExitFactoryInfo = deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(deviceExitFactoryInfoEntity));
		if (ObjectUtil.isNotNull(deviceExitFactoryInfo) && ObjectUtil.isNotNull(deviceExitFactoryInfo.getCompany())
				&& ObjectUtil.isNotNull(device23) && ObjectUtil.isNotNull(device23.getHybridWorkMode())) {
			List<DictBiz> dictData = dictBizClient.getList("inverter_mode").getData();
			String currentLanguage = CommonUtil.getCurrentLanguage();
			DictBiz dictBiz = dictData.stream().filter(p -> deviceExitFactoryInfo.getCompany().equals(p.getAttribute2())
							&& p.getLanguage().equals(currentLanguage) && p.getDictKey().equals(device23.getHybridWorkMode()))
					.collect(Collectors.toList()).stream().findFirst().orElse(new DictBiz());
			device23.setHybridWorkMode(dictBiz.getDictValue());
		}

		return device23;
	}

	@Override
	public DeviceCurrentStatusEntity getDetailCurrentStatus(DeviceCurrentStatusEntity deviceCurrentStatus) {
		Long plantId = deviceCurrentStatus.getPlantId();
		String deviceSerialNumber = deviceCurrentStatus.getDeviceSerialNumber();
		LambdaQueryWrapper<DeviceCurrentStatusEntity> eq = Wrappers.<DeviceCurrentStatusEntity>query().lambda().eq(DeviceCurrentStatusEntity::getPlantId, plantId).eq(DeviceCurrentStatusEntity::getDeviceSerialNumber, deviceSerialNumber);
		DeviceCurrentStatusEntity detail = deviceCurrentStatusService.getOne(eq);
		if(ObjectUtil.isEmpty(detail)) {
			LambdaQueryWrapper<DeviceCurrentStatusEntity> eq2 = Wrappers.<DeviceCurrentStatusEntity>query().lambda().eq(DeviceCurrentStatusEntity::getPlantId, plantId).eq(DeviceCurrentStatusEntity::getDeviceSerialNumber, deviceSerialNumber).eq(DeviceCurrentStatusEntity::getIsDeleted, 1);
			List<DeviceCurrentStatusEntity> deviceCurrentStatusEntityList = deviceCurrentStatusService.selectDeviceCurrentStatusIsDelete(plantId, deviceSerialNumber);
			if (CollectionUtil.isNotEmpty(deviceCurrentStatusEntityList)) {
				detail = deviceCurrentStatusEntityList.get(0);
			}
		}

		LambdaQueryWrapper<Device23Entity> device23EntityLambdaQueryWrapper = Wrappers.<Device23Entity>query().lambda().eq(Device23Entity::getPlantId, plantId)
			.eq(Device23Entity::getDeviceSerialNumber, deviceSerialNumber);
		Device23Entity device23 = device23Service.getOne(device23EntityLambdaQueryWrapper);

		if(ObjectUtil.isNotNull(detail)){
//			detail.setTotalEnergy(DataUnitConversionUtil.smallToLarge(detail.getTotalEnergy(), null, null));
			detail.setTodayEnergy(DataUnitConversionUtil.smallToLarge(detail.getTodayEnergy(), null, null));
			detail.setAccumulatedEnergyOfPositive(DataUnitConversionUtil.smallToLarge(detail.getAccumulatedEnergyOfPositive(), null, null));
			detail.setAccumulatedEnergyOfNegative(DataUnitConversionUtil.smallToLarge(detail.getAccumulatedEnergyOfNegative(), null, null));
			detail.setAccumulatedEnergyOfLoad(DataUnitConversionUtil.smallToLarge(detail.getAccumulatedEnergyOfLoad(), null, null));
			detail.setTodayImportEnergy(DataUnitConversionUtil.smallToLarge(detail.getTodayImportEnergy(), null, null));
			detail.setTodayExportEnergy(DataUnitConversionUtil.smallToLarge(detail.getTodayExportEnergy(), null, null));
			detail.setAccumulatedEnergyToEps(DataUnitConversionUtil.smallToLarge(detail.getAccumulatedEnergyToEps(), null, null));
			detail.setBatteryDailyChargeEnergy(DataUnitConversionUtil.smallToLarge(detail.getBatteryDailyChargeEnergy(), null, null));
			detail.setBatteryDailyDischargeEnergy(DataUnitConversionUtil.smallToLarge(detail.getBatteryDailyDischargeEnergy(), null, null));
			detail.setBatteryAccumulatedChargeEnergy(DataUnitConversionUtil.smallToLarge(detail.getBatteryAccumulatedChargeEnergy(), null, null));
			detail.setBatteryAccumulatedDischargeEnergy(DataUnitConversionUtil.smallToLarge(detail.getBatteryAccumulatedDischargeEnergy(), null, null));
			detail.setDailyEnergyToEps(DataUnitConversionUtil.smallToLarge(detail.getDailyEnergyToEps(), null, null));
			detail.setTodayLoadEnergy(DataUnitConversionUtil.smallToLarge(detail.getTodayLoadEnergy(), null, null));

			if (ObjectUtil.isNotNull(device23) && ValidationUtil.isNotEmpty(device23.getHybridWorkMode())) {
				List<DictBiz> inverterMode = dictBizClient.getListByLang("inverter_mode", CommonUtil.getCurrentLanguage()).getData();
				List<DictBiz> snj = inverterMode.stream().filter(x -> Func.equals(x.getDictKey(), device23.getHybridWorkMode()))
					.filter(x -> Func.equals(x.getAttribute2(), "snj"))
					.collect(Collectors.toList());
				if (CollectionUtil.isNotEmpty(snj)) {
					DictBiz dictBiz = snj.get(0);
					detail.setHybridWorkMode(dictBiz.getDictValue());
				}
			}

			Device21Entity device21Entity = new Device21Entity();
			device21Entity.setDeviceSerialNumber(deviceSerialNumber);
			device21Entity.setPlantId(plantId);
			Device21Entity device21 = device21Service.getOne(Condition.getQueryWrapper(device21Entity));
			if (ObjectUtil.isNotEmpty(device21) && CharSequenceUtil.isNotEmpty(device21.getGridPhaseNumber())) {
				detail.setGridPhaseNumber(device21.getGridPhaseNumber());
			}

			DeviceExitFactoryInfoEntity exitFactoryInfoEntity = new DeviceExitFactoryInfoEntity();
			exitFactoryInfoEntity.setDeviceSerialNumber(deviceSerialNumber);
			DeviceExitFactoryInfoEntity one = deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(exitFactoryInfoEntity));
			if (ObjectUtil.isNotNull(one) && CharSequenceUtil.isNotEmpty(one.getActivationDate())){
				LocalDate localDate = LocalDate.parse(one.getActivationDate());
				LocalDate currentDate = LocalDate.now();
				long daysDiff = ChronoUnit.DAYS.between(localDate, currentDate);
				detail.setRunDays(daysDiff);
			}
		}

		return detail;
	}

	@Override
	public Device21Entity getDetailDeviceInfo(Device21Entity device21Entity) {
		QueryWrapper<Device21Entity> queryWrapper = Condition.getQueryWrapper(device21Entity);
		LambdaQueryWrapper<Device21Entity> lambdaQueryWrapper = queryWrapper.lambda().eq(Device21Entity::getPlantId, device21Entity.getPlantId()).eq(Device21Entity::getDeviceSerialNumber, device21Entity.getDeviceSerialNumber());
		Device21Entity device21 = device21Service.getOne(lambdaQueryWrapper);
		if (ObjectUtil.isEmpty(device21)) {
			List<Device21Entity> device21List = device21Service.getEntityIsDelete(device21Entity.getPlantId(), device21Entity.getDeviceSerialNumber());
			if (CollectionUtil.isNotEmpty(device21List)) {
				device21 = device21List.get(0);
			}
		}

		if (ObjectUtil.isNotEmpty(device21) && device21.getRatedVoltage() != null) {
			device21.setRatedVoltage(device21.getRatedVoltage().setScale(0, BigDecimal.ROUND_DOWN));
		}
		if (ObjectUtil.isNotEmpty(device21) && device21.getRatedFrequency() != null) {
			device21.setRatedFrequency(device21.getRatedFrequency().setScale(0, BigDecimal.ROUND_DOWN));
		}
		DeviceExitFactoryInfoEntity deviceExitFactoryInfoEntity = new DeviceExitFactoryInfoEntity();
		deviceExitFactoryInfoEntity.setDeviceSerialNumber(device21Entity.getDeviceSerialNumber());
		DeviceExitFactoryInfoEntity factoryInfoEntity = deviceExitFactoryInfoService.getOne(Condition.getQueryWrapper(deviceExitFactoryInfoEntity));
		if (ObjectUtil.isNotEmpty(device21) && ObjectUtil.isNotNull(factoryInfoEntity) && StrUtil.isNotEmpty(factoryInfoEntity.getPower())){
			device21.setRatedPower(factoryInfoEntity.getPower());
		}
		if (ObjectUtil.isNotEmpty(device21) && StrUtil.isNotEmpty(device21.getDeviceModel())) {
			String deviceModel = device21.getDeviceModel().replace("\u0000", CommonConstant.BLANK);
			device21.setDeviceModel(deviceModel);
		}
		return device21;
	}

	@Override
	public void selectStatusReportExport(InverterReportQueryVO queryCondition, HttpServletResponse response) {
		try {
			String language=CommonUtil.getCurrentLanguage();
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.INVERTER_EVERY_DAY_POWER.getCode()), StandardCharsets.UTF_8) + ".xlsx");

			List<InvertStatusReport> invertStatusReportList = this.getInvertStatusReport(queryCondition);
			Map<String, List<Object>> statusListDataMap = this.processStatusListData(invertStatusReportList);
			List<Object> dateTimeForDay = statusListDataMap.get("deviceDateTimeForDay");
			List<Object> minuteTime = statusListDataMap.get("deviceDateTime");
			// 分钟去重
			List<String> distinctMinuteTime = minuteTime.stream()
					.distinct()
					.map(Object::toString)
					.collect(Collectors.toList());
			// 日期去重
			List<String> firstHeadList = dateTimeForDay.stream()
					.distinct() // 去重
					.map(obj -> LocalDate.parse((String) obj, ymdFormatter)) // 转换为LocalDateTime类型
					.sorted(Comparator.naturalOrder()) // 按照时间顺序排序
					.map(dateTime -> dateTime.format(ymdFormatter)) // 格式化为字符串
					.collect(Collectors.toList()); // 转换为列表
			String isPhotovoltaicInverter = judgeInverterKind(queryCondition.getDeviceSerialNumber());
			if (BizConstant.INVERT_KIND_PHOTOVOLTAIC_INVERTER.equals(isPhotovoltaicInverter)){
				constructStaticExcelData4PhotovoltaicInverter(response, invertStatusReportList, distinctMinuteTime, firstHeadList, language);
			}else{
				constructStaticExcelData4GeneralInverter(response, invertStatusReportList, distinctMinuteTime, firstHeadList, language);
			}
		} catch (Exception var6) {
			log.error("export Inverter Status Report error: {}",var6.getMessage(),var6);
		}
	}

	private String judgeInverterKind(String deviceSerialNumber) {
		DeviceExitFactoryInfoEntity modelAndProtocolBySn = deviceExitFactoryInfoService.getModelAndProtocolBySn(new DeviceExitFactoryInfoEntity(deviceSerialNumber));
		if (ObjectUtil.isNotNull(modelAndProtocolBySn) && ValidationUtil.isNotEmpty(modelAndProtocolBySn.getDeviceType())){
			return DictBizCache.getValue(BizConstant.INVERT_KIND_DICT_CODE, modelAndProtocolBySn.getDeviceType());
		}else{
			return CommonConstant.BLANK;
		}
	}

	private void constructStaticExcelData4PhotovoltaicInverter(HttpServletResponse response, List<InvertStatusReport> invertStatusReportList, List<String> distinctMinuteTime, List<String> firstHeadList, String language)  throws IOException {
		List<String> secondHead4Mppt = Arrays.asList("MPPT1 (W)", "MPPT2 (W)");
		List<String> secondHead4Grid = Arrays.asList("L1 (W)");
		List<String> secondHead4Load = Arrays.asList("L1 (W)");
		List<String> secondHead4AcPower = Arrays.asList("L1 (W)", "L2 (W)", "L3 (W)");
		List<List<String>> mpptDataList = new ArrayList<>();
		List<List<String>> gridDataList = new ArrayList<>();
		List<List<String>> loadDataList = new ArrayList<>();
		List<List<String>> acPowerDataList = new ArrayList<>();
		// 根据时间分组、再根据日期分组
		Map<String, Map<String, InvertStatusReport>> groupedMap = invertStatusReportList.stream()
				.collect(Collectors.groupingBy(InvertStatusReport::getDeviceDateTime,
						Collectors.toMap(InvertStatusReport::getDeviceDateTimeForDay, report -> report)));

		// 循环外层：按每5分钟的行数据
		for (String mintStr : distinctMinuteTime) {
			List<String> rowMppt = Lists.newArrayList(mintStr);
			List<String> rowGrid = Lists.newArrayList(mintStr);
			List<String> rowLoad = Lists.newArrayList(mintStr);
			List<String> rowAcPower = Lists.newArrayList(mintStr);
			if (groupedMap.containsKey(mintStr)) {
				Map<String, InvertStatusReport> stringListMap = groupedMap.get(mintStr);
				// 循环内层：按日的列数据
				// 限制流的大小为总元素数减1(去除最后一天冗余日期)
				for (String dayData : firstHeadList) {
					if (stringListMap.containsKey(dayData)) {
						InvertStatusReport invertStatusReport = stringListMap.get(dayData);
						rowMppt.add(objectToString(invertStatusReport.getMppt1Power()));
						rowMppt.add(objectToString(invertStatusReport.getMppt2Power()));
//						rowMppt.add(objectToString(invertStatusReport.getMppt3Power()));
//						rowMppt.add(objectToString(invertStatusReport.getMppt4Power()));
						rowGrid.add(objectToString(invertStatusReport.getPhaseRWattOfGrid()));
//						rowGrid.add(objectToString(invertStatusReport.getPhaseSWattOfGrid()));
//						rowGrid.add(objectToString(invertStatusReport.getPhaseTWattOfGrid()));
						rowLoad.add(objectToString(invertStatusReport.getPhaseRWattOfLoad()));
//						rowLoad.add(objectToString(invertStatusReport.getPhaseSWattOfLoad()));
//						rowLoad.add(objectToString(invertStatusReport.getPhaseTWattOfLoad()));
//						rowBackUp.add(objectToString(invertStatusReport.getPhaseRWattOfEps()));
//						rowBackUp.add(objectToString(invertStatusReport.getPhaseSWattOfEps()));
//						rowBackUp.add(objectToString(invertStatusReport.getPhaseTWattOfEps()));
						rowAcPower.add(objectToString(invertStatusReport.getL1PhasePowerOfAcCouple()));
						rowAcPower.add(objectToString(invertStatusReport.getL2PhasePowerOfAcCouple()));
						rowAcPower.add(objectToString(invertStatusReport.getL3PhasePowerOfAcCouple()));
					}
				}
			}
			mpptDataList.add(rowMppt);
			gridDataList.add(rowGrid);
			loadDataList.add(rowLoad);
			acPowerDataList.add(rowAcPower);
		}

		ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
		// 构建sheet对象&写数据
		WriteSheet writeSheet1 = EasyExcelFactory.writerSheet("MPPT").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4Mppt,"Time")).build();
		excelWriter.write(mpptDataList,writeSheet1);
		WriteSheet writeSheet2 = EasyExcelFactory.writerSheet(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.GRID.getCode())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4Grid,"Time")).build();
		excelWriter.write(gridDataList,writeSheet2);
		WriteSheet writeSheet3 = EasyExcelFactory.writerSheet(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.LOAD.getCode())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4Load,"Time")).build();
		excelWriter.write(loadDataList,writeSheet3);
		WriteSheet writeSheet4 = EasyExcelFactory.writerSheet(EveryDayPowerAndEnergyEnum.matchLanguage(language,
			EveryDayPowerAndEnergyEnum.OTHER_PV.getCode())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4AcPower,"Time")).build();
		excelWriter.write(acPowerDataList,writeSheet4);

		excelWriter.finish();
	}

	private void constructStaticExcelData4GeneralInverter(HttpServletResponse response, List<InvertStatusReport> invertStatusReportList, List<String> distinctMinuteTime, List<String> firstHeadList, String language) throws IOException {
		List<String> secondHead4Mppt = Arrays.asList("MPPT1 (W)", "MPPT2 (W)", "MPPT3 (W)", "MPPT4 (W)");
		List<String> secondHead4Grid = Arrays.asList("L1 (W)", "L2 (W)", "L3 (W)");
		List<String> secondHead4Load = Arrays.asList("L1 (W)", "L2 (W)", "L3 (W)");
		List<String> secondHead4Backup = Arrays.asList("L1 (W)", "L2 (W)", "L3 (W)");
		List<String> secondHead4AcPower = Arrays.asList("L1 (W)", "L2 (W)", "L3 (W)");
		List<List<String>> mpptDataList = new ArrayList<>();
		List<List<String>> gridDataList = new ArrayList<>();
		List<List<String>> loadDataList = new ArrayList<>();
		List<List<String>> backupDataList = new ArrayList<>();
		List<List<String>> acPowerDataList = new ArrayList<>();
		// 根据时间分组、再根据日期分组
		Map<String, Map<String, InvertStatusReport>> groupedMap = invertStatusReportList.stream()
				.collect(Collectors.groupingBy(InvertStatusReport::getDeviceDateTime,
						Collectors.toMap(InvertStatusReport::getDeviceDateTimeForDay, report -> report)));
		// 循环外层：按每5分钟的行数据
		for (String mintStr : distinctMinuteTime) {
			List<String> rowMppt = Lists.newArrayList(mintStr);
			List<String> rowGrid = Lists.newArrayList(mintStr);
			List<String> rowLoad = Lists.newArrayList(mintStr);
			List<String> rowBackUp = Lists.newArrayList(mintStr);
			List<String> rowAcPower = Lists.newArrayList(mintStr);
			if (groupedMap.containsKey(mintStr)) {
				Map<String, InvertStatusReport> stringListMap = groupedMap.get(mintStr);
				// 循环内层：按日的列数据
				// 限制流的大小为总元素数减1(去除最后一天冗余日期)
				for (String dayData : firstHeadList) {
					if (stringListMap.containsKey(dayData)) {
						InvertStatusReport invertStatusReport = stringListMap.get(dayData);
						rowMppt.add(objectToString(invertStatusReport.getMppt1Power()));
						rowMppt.add(objectToString(invertStatusReport.getMppt2Power()));
						rowMppt.add(objectToString(invertStatusReport.getMppt3Power()));
						rowMppt.add(objectToString(invertStatusReport.getMppt4Power()));
						rowGrid.add(objectToString(invertStatusReport.getPhaseRWattOfGrid()));
						rowGrid.add(objectToString(invertStatusReport.getPhaseSWattOfGrid()));
						rowGrid.add(objectToString(invertStatusReport.getPhaseTWattOfGrid()));
						rowLoad.add(objectToString(invertStatusReport.getPhaseRWattOfLoad()));
						rowLoad.add(objectToString(invertStatusReport.getPhaseSWattOfLoad()));
						rowLoad.add(objectToString(invertStatusReport.getPhaseTWattOfLoad()));
						rowBackUp.add(objectToString(invertStatusReport.getPhaseRWattOfEps()));
						rowBackUp.add(objectToString(invertStatusReport.getPhaseSWattOfEps()));
						rowBackUp.add(objectToString(invertStatusReport.getPhaseTWattOfEps()));
						rowAcPower.add(objectToString(invertStatusReport.getL1PhasePowerOfAcCouple()));
						rowAcPower.add(objectToString(invertStatusReport.getL2PhasePowerOfAcCouple()));
						rowAcPower.add(objectToString(invertStatusReport.getL3PhasePowerOfAcCouple()));
					}
				}
			}
			mpptDataList.add(rowMppt);
			gridDataList.add(rowGrid);
			loadDataList.add(rowLoad);
			backupDataList.add(rowBackUp);
			acPowerDataList.add(rowAcPower);
		}
		ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
		// 构建sheet对象&写数据
		WriteSheet writeSheet1 = EasyExcelFactory.writerSheet("MPPT").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4Mppt,"Time")).build();
		excelWriter.write(mpptDataList,writeSheet1);
		WriteSheet writeSheet2 = EasyExcelFactory.writerSheet(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.GRID.getCode())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4Grid,"Time")).build();
		excelWriter.write(gridDataList,writeSheet2);
		WriteSheet writeSheet3 = EasyExcelFactory.writerSheet(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.LOAD.getCode())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4Load,"Time")).build();
		excelWriter.write(loadDataList,writeSheet3);
		WriteSheet writeSheet4 = EasyExcelFactory.writerSheet(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.BACKUP.getCode())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4Backup,"Time")).build();
		excelWriter.write(backupDataList,writeSheet4);
		WriteSheet writeSheet5 = EasyExcelFactory.writerSheet(EveryDayPowerAndEnergyEnum.matchLanguage(language,
			EveryDayPowerAndEnergyEnum.OTHER_PV.getCode())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(getHeadList(firstHeadList,secondHead4AcPower,"Time")).build();
		excelWriter.write(acPowerDataList,writeSheet5);
		excelWriter.finish();
	}
	/**
	 * null 转""展示
	 * @param obj 入参
	 * @return String
	 * <AUTHOR>
	 * @since 2024/10/22 16:54
	 **/
	private String  objectToString(Object obj){
		if(obj == null){
			return Strings.EMPTY;
		}
		return String.valueOf(obj);
	}
	private List<List<String>> getHeadList(List<String> firstHeadList,List<String> secondHeadList,String leftmostRow) {
		List<List<String>> headList = new ArrayList<>();
		headList.add(Lists.newArrayList(leftmostRow));
		for (String first : firstHeadList) {
			for (String second : secondHeadList) {
				List<String> secondList = new ArrayList<>();
				secondList.add(first);
				secondList.add(second);
				headList.add(secondList);
			}
		}
		return headList;
	}

	@Override
	public void selectEveryDayStatExport(InverterReportQueryVO queryCondition, HttpServletResponse response) {
		try {
			String language=CommonUtil.getCurrentLanguage();
			String fileName = URLEncoder.encode(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.Inverter_Everyday_Energy.getCode()), StandardCharsets.UTF_8);
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

			InverterDayReport inverterDayReport = this.selectEveryDayStat(queryCondition);
			List<List<String>> dataList = inverterDayReport.getDataList();
			List<String> typeList = inverterDayReport.getTypeList().stream()
					.filter(s -> !"type".equals(s))
					.collect(Collectors.toList());
			List<List<String>> headList = getHeadList(Lists.newArrayList(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.everydayEnergy.getCode())),
					typeList, EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.Date.getCode()));

			ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();
			// 构建sheet对象&写数据
			WriteSheet writeSheet1 = EasyExcelFactory.writerSheet(EveryDayPowerAndEnergyEnum.matchLanguage(language, EveryDayPowerAndEnergyEnum.inverter.getCode())).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(headList).build();
			excelWriter.write(dataList,writeSheet1);
			excelWriter.finish();
		} catch (Exception e) {
			log.error("export Inverter Day Report error: {}",e.getMessage(),e);
		}
	}

}
