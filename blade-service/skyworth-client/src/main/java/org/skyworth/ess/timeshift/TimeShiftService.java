package org.skyworth.ess.timeshift;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.device.service.TimeZoneDeviceService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class TimeShiftService implements ITimeShiftService{

	private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	@Autowired
	private TimeZoneDeviceService timeZoneDeviceService;

	/**
	 * 通用方法，遍历 List 并根据字段名称提取对应的字段值，将其转换为字符串类型
	 *
	 * @param list 列表
	 * @param fieldName 字段名称
	 * @return 包含字段值的字符串列表
	 */
	@Override
	public  <T>  void shiftTimeZoneAndDateTime(List<T> list, String fieldName, List<Long> plantIdList) {
		// 获取当前请求头的时间和时区
		String currentTimeZoneByRequest = CommonUtil.getCurrentTimeZoneByRequest();
		Map<String, String> timeZoneMap = new HashMap<>();
		if (ValidationUtil.isEmpty(currentTimeZoneByRequest) && !CollectionUtils.isNullOrEmpty(plantIdList)) {
			timeZoneMap = timeZoneDeviceService.getMapFromCacheByPlantIdList(plantIdList);
		}

		for (T element : list) {
			try {
				Field declaredField = element.getClass().getDeclaredField(fieldName);
				Field plantIdField = element.getClass().getDeclaredField("plantId");
				Field timeZoneField = element.getClass().getDeclaredField("timeZone");
				declaredField.setAccessible(true);
				plantIdField.setAccessible(true);
				timeZoneField.setAccessible(true);

				Object timeValue = declaredField.get(element);
				Object plantIdValue = plantIdField.get(element);

				if (timeValue != null) {
					Long plantIdValueLong = (Long) plantIdValue;
					String plantIdValueStr = String.valueOf(plantIdValueLong);
					LocalDateTime dbDataTime = convertToDateTime(timeValue);
					String timeZone = getTimeZone(currentTimeZoneByRequest, timeZoneMap, plantIdValueStr);
					LocalDateTime afterShiftTime = DateUtil.convertLocalDateTimeZone(dbDataTime, CommonConstant.COMMON_DEFAULT_TIME_ZONE,timeZone);
					declaredField.set(element, DateUtil.convertToDate(afterShiftTime));
					timeZoneField.set(element, timeZone);
				}
			} catch (NoSuchFieldException | IllegalAccessException e) {
				log.error("Error accessing field " + fieldName + " in class " + element.getClass().getName() + ": " + e.getMessage(), e);
			}
		}
	}

	@Override
	public void shiftTimeZoneAndDateTimeForEntity(Object entity, String... fieldNames) {
		try {
			for (String fieldName : fieldNames) {
				Field declaredField = entity.getClass().getDeclaredField(fieldName);
				Field plantIdField = entity.getClass().getDeclaredField("plantId");
				Field timeZoneField = entity.getClass().getDeclaredField("timeZone");
				declaredField.setAccessible(true);
				plantIdField.setAccessible(true);
				timeZoneField.setAccessible(true);
				Object timeValue = declaredField.get(entity);
				Object plantIdValue = plantIdField.get(entity);

				List<Long> plantIdList = new ArrayList<>();
				plantIdList.add((Long) plantIdValue);
				// 获取当前请求头的时间和时区
				String currentTimeZoneByRequest = CommonUtil.getCurrentTimeZoneByRequest();
				Map<String, String> timeZoneMap = new HashMap<>();
				if (ValidationUtil.isEmpty(currentTimeZoneByRequest) && !CollectionUtils.isNullOrEmpty(plantIdList)) {
					timeZoneMap = timeZoneDeviceService.getMapFromCacheByPlantIdList(plantIdList);
				}

				if (timeValue != null) {
					Long plantIdValueLong = (Long) plantIdValue;
					String plantIdValueStr = String.valueOf(plantIdValueLong);
					LocalDateTime dbDataTime = convertToDateTime(timeValue);
					String timeZone = getTimeZone(currentTimeZoneByRequest, timeZoneMap, plantIdValueStr);
					LocalDateTime afterShiftTime = DateUtil.convertLocalDateTimeZone(dbDataTime, CommonConstant.COMMON_DEFAULT_TIME_ZONE, timeZone);
					declaredField.set(entity, DateUtil.convertToDate(afterShiftTime));
					timeZoneField.set(entity, timeZone);
				}
			}
		} catch (NoSuchFieldException | IllegalAccessException e) {
			log.error("Error accessing field in class " + entity.getClass().getName() + ": " + e.getMessage(), e);
		}
	}


	/**
	 * 先取请求头中的时区
	 * 如果没有，则取数据库中的时区
	 * 最后才取系统默认时区
	 * @param currentTimeZoneByRequest
	 * @param timeZoneMap
	 * @param plantIdValue
	 * @return
	 */
	private String getTimeZone(String currentTimeZoneByRequest, Map<String, String> timeZoneMap, String plantIdValue) {
		// 01 获取请求头中的时区信息
		if (ValidationUtil.isNotEmpty(currentTimeZoneByRequest)) {
			return currentTimeZoneByRequest;
		}


		// 02 获取数据库站点时区表中的时区
		if (ObjectUtil.isNotNull(timeZoneMap) && ValidationUtil.isNotEmpty(timeZoneMap.get(plantIdValue))) {
			return timeZoneMap.get(plantIdValue);
		}

		// 03 获取系统默认时区
		return CommonConstant.COMMON_DEFAULT_TIME_ZONE;
	}


	/**
	 * 通用方法，遍历 List 并根据字段名称提取对应的字段值，将其转换为字符串类型
	 * 并返回原 List
	 *
	 * @param list 列表
	 * @param fieldName 字段名称
	 * @param <T> 列表元素的类型
	 * @return 原列表
	 */
	@Override
	public <T> List<T> getAndReturnList(List<T> list, String fieldName, List<Long> plantIdList) {
		shiftTimeZoneAndDateTime(list, fieldName,plantIdList);
		return list;
	}

	/**
	 * 校验并转换日期时间字符串
	 *
	 * @param value 日期时间字符串
	 * @return 转换后的 LocalDateTime 对象，如果不符合格式则返回 null
	 */
	private LocalDateTime convertToDateTime(Object value) {
		if (value instanceof String) {
			String valueStr = (String) value;
			try {
				return LocalDateTime.parse(valueStr, DATE_TIME_FORMATTER);
			} catch (Exception e) {
				try {
					LocalDate date = LocalDate.parse(valueStr, DATE_FORMATTER);
					return date.atStartOfDay();
				} catch (Exception ex) {
					log.warn("Invalid date format: " + valueStr);
					return null;
				}
			}
		} else if (value instanceof Date) {
			Date date = (Date) value;
			return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		} else {
			log.warn("Unsupported type: " + value.getClass().getName());
			return null;
		}

	}
}
