package org.skyworth.ess.remark.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.remark.entity.RemarkHistoryEntity;
import org.skyworth.ess.remark.vo.RemarkHistoryVO;
import java.util.Objects;

/**
 * 站点评论记录表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
public class RemarkHistoryWrapper extends BaseEntityWrapper<RemarkHistoryEntity, RemarkHistoryVO>  {

	public static RemarkHistoryWrapper build() {
		return new RemarkHistoryWrapper();
 	}

	@Override
	public RemarkHistoryVO entityVO(RemarkHistoryEntity remarkHistory) {
		RemarkHistoryVO remarkHistoryVO = Objects.requireNonNull(BeanUtil.copy(remarkHistory, RemarkHistoryVO.class));

		//User createUser = UserCache.getUser(remarkHistory.getCreateUser());
		//User updateUser = UserCache.getUser(remarkHistory.getUpdateUser());
		//remarkHistoryVO.setCreateUserName(createUser.getName());
		//remarkHistoryVO.setUpdateUserName(updateUser.getName());

		return remarkHistoryVO;
	}


}
