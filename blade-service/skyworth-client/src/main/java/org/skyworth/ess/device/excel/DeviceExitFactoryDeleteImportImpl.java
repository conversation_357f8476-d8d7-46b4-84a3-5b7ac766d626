package org.skyworth.ess.device.excel;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.device.service.IDeviceExitFactoryInfoService;
import org.skyworth.ess.i18n.util.I18nUtil;
import org.springblade.common.excel.ExcelImportServiceAbstract;
import org.springblade.core.tool.constant.BladeConstant;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DeviceExitFactoryDeleteImportImpl extends ExcelImportServiceAbstract<DeviceExitFactoryDeleteImportExcel> {
	private final IDeviceExitFactoryInfoService deviceExitFactoryInfoService;

	@Override
	public String validateDataEffective(List<DeviceExitFactoryDeleteImportExcel> dataList) {
		if (dataList.isEmpty()){
			return I18nUtil.getString("client.inverter.sn.empty");
		}
		List<String> deviceSerialNumbers =
			dataList.stream().map(DeviceExitFactoryDeleteImportExcel::getDeviceSerialNumber).distinct().collect(Collectors.toList());
		List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntityList =
			deviceExitFactoryInfoService.list(Wrappers.lambdaQuery(DeviceExitFactoryInfoEntity.class)
				.select(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, DeviceExitFactoryInfoEntity::getId,
					DeviceExitFactoryInfoEntity::getStatus)
				.in(DeviceExitFactoryInfoEntity::getDeviceSerialNumber, deviceSerialNumbers).eq(DeviceExitFactoryInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		StringBuilder errorReturn = new StringBuilder();
		// 检查设备出厂信息是否存在
		if (!deviceExitFactoryInfoEntityList.isEmpty()) {
			// 如果入参中个数 和 DB中个数不相等，则标识录入SN不准确，则返回错误信息
			if (deviceSerialNumbers.size() != deviceExitFactoryInfoEntityList.size()) {
				validDeviceSerialNumberIsExists(deviceExitFactoryInfoEntityList, deviceSerialNumbers, errorReturn);
			}
			// 检查设备出厂信息是否被使用，被使用不能删除
			List<String> usedDeviceSerialNumbers =
				deviceExitFactoryInfoEntityList.stream().filter(p -> p.getStatus() == BladeConstant.DB_STATUS_NORMAL).map(DeviceExitFactoryInfoEntity::getDeviceSerialNumber).collect(Collectors.toList());
			if (!usedDeviceSerialNumbers.isEmpty()) {
				String errorMsg = I18nUtil.getString("client.inverter.sn.has.used");
				errorReturn.append(String.format(errorMsg, String.join(",", usedDeviceSerialNumbers))).append(";");
			}
		} else {
			String errorMsg = I18nUtil.getString("client.inverter.sn.not.exists");
			errorReturn.append(String.format(errorMsg, String.join(",", deviceSerialNumbers))).append(";");
		}
		return errorReturn.toString();
	}

	/**
	 * 检查设备出厂信息是否存在
	 *
	 * @param deviceExitFactoryInfoEntityList 设备出厂信息列表
	 * @param deviceSerialNumbers             设备出厂信息列表
	 * @param errorReturn                     错误信息
	 */
	private void validDeviceSerialNumberIsExists(List<DeviceExitFactoryInfoEntity> deviceExitFactoryInfoEntityList,
												 List<String> deviceSerialNumbers, StringBuilder errorReturn) {
		List<String> dbDeviceSerialNumbers =
			deviceExitFactoryInfoEntityList.stream().map(DeviceExitFactoryInfoEntity::getDeviceSerialNumber).collect(Collectors.toList());
		StringBuilder notExistError = new StringBuilder();
		deviceSerialNumbers.forEach(p -> {
			if (!dbDeviceSerialNumbers.contains(p)) {
				notExistError.append(p).append(",");
			}
		});
		// 检查末尾是否有逗号，有则删除
		if (notExistError.length() > 0 && notExistError.charAt(notExistError.length() - 1) == ',') {
			// 删除最后一个字符
			notExistError.deleteCharAt(notExistError.length() - 1);
		}
		String errorMsg = I18nUtil.getString("client.inverter.sn.not.exists");
		errorReturn.append(String.format(errorMsg, notExistError)).append(";");
	}
}
