/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.company.entity.AgentOperationLogEntity;
import org.skyworth.ess.company.service.IAgentOperationLogService;
import org.skyworth.ess.company.vo.AgentOperationLogVO;
import org.skyworth.ess.company.wrapper.AgentOperationLogWrapper;
import org.skyworth.ess.constant.OperateEnum;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;

/**
 * 操作日志表 控制器
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("operationLog")
@Api(value = "操作日志表", tags = "操作日志表接口")
public class AgentOperationLogController extends BladeController {

	private final IAgentOperationLogService agentOperationLogService;


	/**
	 * 操作日志表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入agentOperationLog")
	public R<IPage<AgentOperationLogVO>> list(@ApiIgnore @RequestParam Map<String, Object> agentOperationLog, Query query) {
		IPage<AgentOperationLogEntity> pages = agentOperationLogService.page(Condition.getPage(query),
			Wrappers.<AgentOperationLogEntity>query().orderByDesc("id").select("operate_type", "create_user_account", "create_time").lambda().eq(AgentOperationLogEntity::getBusinessId, agentOperationLog.get("businessId").toString()));
		List<AgentOperationLogEntity> list = pages.getRecords();
		String language = CommonUtil.getCurrentLanguage();
		for (AgentOperationLogEntity agentOperationLogEntity : list) {
			OperateEnum operateEnum = OperateEnum.of(agentOperationLogEntity.getOperateType());
			if (operateEnum == null) {
				continue;
			}
			if (language.equalsIgnoreCase(CommonConstant.CURRENT_LANGUAGE_ZH)) {
				agentOperationLogEntity.setOperateType(operateEnum.getCnDesc());
			} else {
				agentOperationLogEntity.setOperateType(operateEnum.getEnDesc());
			}
		}
		return R.data(AgentOperationLogWrapper.build().pageVO(pages));
	}
}
