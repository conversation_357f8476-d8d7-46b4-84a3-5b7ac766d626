/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.additionalInfo.wrapper;

import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.vo.AdditionalInfoVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 图片附加信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public class AdditionalInfoWrapper extends BaseEntityWrapper<AdditionalInfoEntity, AdditionalInfoVO>  {

	public static AdditionalInfoWrapper build() {
		return new AdditionalInfoWrapper();
 	}

	@Override
	public AdditionalInfoVO entityVO(AdditionalInfoEntity additionalInfo) {
		AdditionalInfoVO additionalInfoVO = Objects.requireNonNull(BeanUtil.copy(additionalInfo, AdditionalInfoVO.class));

		//User createUser = UserCache.getUser(additionalInfo.getCreateUser());
		//User updateUser = UserCache.getUser(additionalInfo.getUpdateUser());
		//additionalInfoVO.setCreateUserName(createUser.getName());
		//additionalInfoVO.setUpdateUserName(updateUser.getName());

		return additionalInfoVO;
	}


}
