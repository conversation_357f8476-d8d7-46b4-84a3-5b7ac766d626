/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.battery.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.battery.entity.BatteryMapDeviceEntity;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-09-21 10:20
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class BatteryMapDeviceVO extends BatteryMapDeviceEntity {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "电池型号")
	private String batteryType;
	/**
	 * 额定电池电压
	 */
	@ApiModelProperty(value = "额定电池电压")
	private String ratedBatteryVoltage;
	/**
	 * 额定电池容量
	 */
	@ApiModelProperty(value = "额定电池容量")
	private String ratedBatteryCapacity;
	/**
	 * 额定电池能量
	 */
	@ApiModelProperty(value = "额定电池能量")
	private String ratedBatteryEnergy;
	/**
	 * 单体容量
	 */
	@ApiModelProperty(value = "单体容量")
	private String singleCapacity;
	/**
	 * 内含单体并联数
	 */
	@ApiModelProperty(value = "内含单体串并联数")
	private String singleSeriesParallelingNumber;
	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String deviceType;

	/**
	 * 电池数量
	 */
	@ApiModelProperty(value = "电池数量")
	private Integer batteryNumber;

}
