/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * 设备/逆变器表，记录2.1数据 实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@TableName("device_21")
@ApiModel(value = "Device21对象", description = "设备/逆变器表，记录2.1数据")
@EqualsAndHashCode(callSuper = true)
public class Device21Entity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 逆变器/设备SN
	 */
	@ApiModelProperty(value = "逆变器/设备SN")
	private String deviceSerialNumber;
	/**
	 * modbus协议版本
	 */
	@ApiModelProperty(value = "modbus协议版本")
	private String modbusProtocolVersion;
	/**
	 * 设备/逆变器型号
	 */
	@ApiModelProperty(value = "设备/逆变器型号")
	private String deviceModel;
	/**
	 * 主机软件版本
	 */
	@ApiModelProperty(value = "主机软件版本")
	private String masterSoftwareVersion;
	/**
	 * 主机软件建立日期
	 */
	@ApiModelProperty(value = "主机软件建立日期")
	private String masterSoftwareBuildDate;
	/**
	 * 从机固件版本
	 */
	@ApiModelProperty(value = "从机固件版本")
	private String slaveFirmwareVersion;
	/**
	 * 从机固件建立日期
	 */
	@ApiModelProperty(value = "从机固件建立日期")
	private String slaveFirmwareBuildDate;
	/**
	 * mppt路数
	 */
	@ApiModelProperty(value = "mppt路数")
	private String mpptNumber;
	/**
	 * 额定电压
	 */
	@ApiModelProperty(value = "额定电压")
	private BigDecimal ratedVoltage;
	/**
	 * 额定频率
	 */
	@ApiModelProperty(value = "额定频率")
	private BigDecimal ratedFrequency;
	/**
	 * 额定功率
	 */
	@ApiModelProperty(value = "额定功率")
	private String ratedPower;
	/**
	 * 电网相数
	 */
	@ApiModelProperty(value = "电网相数")
	private String gridPhaseNumber;
	/**
	 * EMS固件版本
	 */
	@ApiModelProperty(value = "EMS固件版本")
	private String emsFirmwareVersion;
	/**
	 * EMS固件建立日期
	 */
	@ApiModelProperty(value = "EMS固件建立日期")
	private String emsFirmwareBuildDate;
	/**
	 * DCDC固件版本
	 */
	@ApiModelProperty(value = "DCDC固件版本")
	private String dcdcFirmwareVersion;
	/**
	 * DCDC固件建立日期
	 */
	@ApiModelProperty(value = "DCDC固件建立日期")
	private String dcdcFirmwareBuildDate;
	/**
	 * wifi棒SN
	 */
	@ApiModelProperty(value = "wifi棒SN")
	private String wifiStickSerialNumber;
	/**
	 * 设备状态
	 */
	@ApiModelProperty(value = "设备状态")
	private String deviceStatus;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "设备类型,对应ota的大类型：wifibom/inverter/")
	@TableField(exist = false)
	private String deviceType;

	/**wifi棒版本号*/
	@ApiModelProperty(name = "wifi棒版本号",notes = "")
	private String wifiVersion;

	/**电池版本号*/
	@ApiModelProperty(name = "电池版本号",notes = "")
	private String batteryVersion;

	/**是否存在用户类告警(0/1:不存在/存在)*/
	@ApiModelProperty("是否存在用户类告警(0/1:不存在/存在)")
	private Integer existUserTypeAlarm;

	/**是否存在代理类告警(0/1:不存在/存在)*/
	@ApiModelProperty("是否存在代理类告警(0/1:不存在/存在)")
	private Integer existAgentTypeAlarm;

}
