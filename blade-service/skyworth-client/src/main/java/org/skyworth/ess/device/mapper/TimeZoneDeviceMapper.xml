<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.TimeZoneDeviceMapper">

    <resultMap id="TimeZoneDeviceMap" type="org.skyworth.ess.device.entity.TimeZoneDevice">
        <id property="id" column="id"/>
        <result property="deviceSerialNumber" column="device_serial_number"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="time_zone" property="timeZone"/>
        <result column="plant_id" property="plantId"/>
    </resultMap>

    <select id="getListBySnList" resultMap="TimeZoneDeviceMap">
        select id, device_serial_number, time_zone, create_user_account, update_user_account,plant_id,
        tenant_id, create_user, create_dept, create_time, update_user, update_time, status, is_deleted,plant_id
        from time_zone_device
        where is_deleted=0 and device_serial_number in
        <foreach collection="deviceSnList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getListByPlantIdList" resultMap="TimeZoneDeviceMap">
        select id, device_serial_number, time_zone, create_user_account, update_user_account,plant_id,
        tenant_id, create_user, create_dept, create_time, update_user, update_time, status, is_deleted,plant_id
        from time_zone_device
        where plant_id in
        <foreach collection="plantIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getTimeZoneListByPlantIds" resultType="java.util.Map">
        select plant_id,time_zone from time_zone_device where is_deleted=0 and plant_id in
        <foreach collection="plantIds" item="plantId" open="(" separator="," close=")">
            #{plantId}
        </foreach>
    </select>

    <update id="deleteByPlantId">
        update time_zone_device set is_deleted=1,update_time=now() where plant_id in
        <foreach collection="plantIdList" item="plantId" open="(" separator="," close=")">
            #{plantId}
        </foreach>
    </update>
</mapper>

