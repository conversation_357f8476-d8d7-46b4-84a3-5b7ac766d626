package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 设备/逆变器日志表，记录23数据;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-9-14
 */
@Data
@ApiModel(value = "设备/逆变器设备表",description = "")
@TableName("device_23")
public class Device23Entity extends TenantEntity implements Serializable,Cloneable{

	/** 工作模式 */
	@ApiModelProperty(name = "工作模式",notes = "工作模式")
	private String hybridWorkMode ;
	/** 一次/每天 */
	@ApiModelProperty(name = "一次/每天",notes = "一次/每天")
	private String onceEveryday ;
	/** 充电开始时间 */
	@ApiModelProperty(name = "充电开始时间",notes = "充电开始时间")
	private String chargeStartTime1 ;
	/** 充电结束时间 */
	@ApiModelProperty(name = "充电结束时间",notes = "充电结束时间")
	private String chargeEndTime1 ;
	/** 放电开始时间 */
	@ApiModelProperty(name = "放电开始时间",notes = "放电开始时间")
	private String dischargeStartTime1 ;
	/** 放电结束时间 */
	@ApiModelProperty(name = "放电结束时间",notes = "放电结束时间")
	private String dischargeEndTime1 ;
	/** 电池类型选择 */
	@ApiModelProperty(name = "电池类型选择",notes = "电池类型选择")
	private String batteryTypeSelection ;
	/** 通讯地址 */
	@ApiModelProperty(name = "通讯地址",notes = "通讯地址")
	private String commAddress ;
	/** 电池容量 */
	@ApiModelProperty(name = "电池容量",notes = "电池容量")
	private BigDecimal batteryAh ;
	/** 铅酸电池放电截止电压 */
	@ApiModelProperty(name = "铅酸电池放电截止电压",notes = "铅酸电池放电截止电压")
	private BigDecimal stopDischargeVoltage ;
	/** 铅酸电池充电截止电压 */
	@ApiModelProperty(name = "铅酸电池充电截止电压",notes = "铅酸电池充电截止电压")
	private BigDecimal stopChargeVoltage ;
	/** 电网充电使能 */
	@ApiModelProperty(name = "电网充电使能",notes = "电网充电使能")
	private String gridCharge ;
	/** 最大电网充电功率 */
	@ApiModelProperty(name = "最大电网充电功率",notes = "最大电网充电功率")
	private BigDecimal maximumGridChargerPower ;
	/** 电网充电截止电量% */
	@ApiModelProperty(name = "电网充电截止电量%",notes = "电网充电截止电量%")
	private BigDecimal capacityOfGridChargerEnd ;
	/** 最大充电功率 */
	@ApiModelProperty(name = "最大充电功率",notes = "最大充电功率")
	private BigDecimal maximumChargerPower ;
	/** 充电截止SOC */
	@ApiModelProperty(name = "充电截止SOC",notes = "充电截止SOC")
	private BigDecimal capacityOfChargerEnd ;
	/** 最大放电功率 */
	@ApiModelProperty(name = "最大放电功率",notes = "最大放电功率")
	private BigDecimal maximumDischargerPower ;
	/** 放电截止EOD */
	@ApiModelProperty(name = "放电截止EOD",notes = "放电截止EOD")
	private BigDecimal capacityOfDischargerEnd ;
	/** 离网模式使能 */
	@ApiModelProperty(name = "离网模式使能",notes = "离网模式使能")
	private String offGridMode ;
	/** 额定输出电压 */
	@ApiModelProperty(name = "额定输出电压",notes = "额定输出电压")
	private BigDecimal ratedOutputVoltage ;
	/** 额定输出频率 */
	@ApiModelProperty(name = "额定输出频率",notes = "额定输出频率")
	private BigDecimal ratedOutputFrequency ;
	/** 切换离网模式的电池最低SOC */
	@ApiModelProperty(name = "切换离网模式的电池最低SOC",notes = "切换离网模式的电池最低SOC")
	private BigDecimal offGridStartUpBatteryCapacity ;
	/** 最大放电电流 */
	@ApiModelProperty(name = "最大放电电流",notes = "最大放电电流")
	private BigDecimal maximumDischargeCurrent ;
	/** 最大充电电流 */
	@ApiModelProperty(name = "最大充电电流",notes = "最大充电电流")
	private BigDecimal maximumChargerCurrent ;

	@ApiModelProperty(name = "GEN端口",notes = "")
	private String genPort;


	@ApiModelProperty(name = "锂电池激活功能",notes = "")
	private String lithiumBatteryActivationFunction;

	@ApiModelProperty(name = "最大发电机充电功率",notes = "")
	private BigDecimal maximumGenChargePower;

	@ApiModelProperty(name = "发电机的最大输入功率",notes = "")
	private BigDecimal maximumInputPowerFromGenerator;


	@ApiModelProperty(name = "支持正常负载功能",notes = "")
	private String supportNormalLoadFunction;

	@ApiModelProperty(name = "并行模式功能",notes = "")
	private String parallelModeFunction;

	@ApiModelProperty(name = "馈入网格功能",notes = "")
	private String feedInGridFunction;

	@ApiModelProperty(name = "最大电网强制充电功率",notes = "")
	private BigDecimal maximumGridForcedChargePower;


	private BigDecimal generatorStartSoc;

	private BigDecimal generatorEndSoc;

	private BigDecimal maximumInputPowerFromGrid;

	private BigDecimal capacityOfDischargeEndOnGrid;

	private BigDecimal forceChargeStartSoc;

	private BigDecimal forceChargeEndSoc;

	private BigDecimal backupMinimumOutputVoltage;

	private BigDecimal backupMaximumOutputVoltage;

	private String generatorDryForceOnOrOff;

	private String onceEveryday2;
	private String chargeStartTime2;
	private String chargeEndTime2;
	private String chargePowerInTime2HighWord;
	private String chargeEndSocInTime2;
	private String dischargeStartTime2;
	private String dischargeEndTime2;
	private String dischargePowerInTime2HighWord;
	private String dischargeEndSocInTime2;

	private String onceEveryday3;
	private String chargeStartTime3;
	private String chargeEndTime3;
	private String chargePowerInTime3HighWord;
	private String chargeEndSocInTime3;
	private String dischargeStartTime3;
	private String dischargeEndTime3;
	private String dischargePowerInTime3HighWord;
	private String dischargeEndSocInTime3;

	public Device23Entity(){

	}
	/** 站点ID */
	@ApiModelProperty(name = "站点ID",notes = "站点ID")
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	private Long plantId ;


	/** modbus协议版本 */
	@ApiModelProperty(name = "modbus协议版本",notes = "modbus协议版本")
	private String modbusProtocolVersion ;


	/** 逆变器/设备SN */
	@ApiModelProperty(name = "逆变器/设备SN",notes = "逆变器/设备SN")
	private String deviceSerialNumber ;

	@ApiModelProperty(name = "同步状态（N未同步；Y已同步）",notes = "同步状态（N未同步；Y已同步）")
	private String synchStatus ;

	/** 创建人账号 */
	@ApiModelProperty(name = "创建人账号",notes = "创建人账号")
	private String createUserAccount ;
	/** 更新人账号 */
	@ApiModelProperty(name = "更新人账号",notes = "更新人账号")
	private String updateUserAccount ;
	/** 分时控制开关 */
	@ApiModelProperty(name = "分时控制开关",notes = "分时控制开关")
	private String timeBasedControlEnable ;

	@ApiModelProperty(value = "最大充电功率(新)")
	private String chargePowerInTime1HighWord;

	@ApiModelProperty(value = "结束充电容量比例(新)")
	private String chargeEndSocInTime1;

	@ApiModelProperty(value = "最大放电功率(新)")
	private String dischargePowerInTime1HighWord;

	@ApiModelProperty(value = "结束放电容量比例(新)")
	private String dischargeEndSocInTime1;
	// ac_coupling开关;(0/1:关/开)
	private BigDecimal acCouplingFunction;
	// 通讯电表,业务字典 device_re_485_hybrid
	private String rs485Device;

	private String parallelSystemBatteryConnectType;
}
