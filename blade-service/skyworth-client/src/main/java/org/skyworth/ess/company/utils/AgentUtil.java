package org.skyworth.ess.company.utils;

import java.util.Map;
import java.util.Optional;

/**
 *
 * org.skyworth.ess.companyInfo.utils
 *
 * <AUTHOR>
 * @since 2023/11/6
 */
public class AgentUtil {
	private AgentUtil() {
		// 私有构造方法，防止外部实例化
	}
	public static String getParameter(Map<String, Object> map, String key) {
		return Optional.ofNullable(map.get(key)).map(Object::toString).orElse(null);
	}
}
