<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.DeviceLog22Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="Device22ResultMap" type="org.skyworth.ess.device.entity.DeviceLog22">
        <result column="id" property="id"/>
        <result column="plant_id" property="plantId"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_date_time" property="deviceDateTime"/>
        <result column="today_export_energy" property="todayExportEnergy"/>
        <result column="today_import_energy" property="todayImportEnergy"/>
        <result column="accumulated_energy_of_positive" property="accumulatedEnergyOfPositive"/>
    </resultMap>

    <select id="queryDeviceLog22list" resultMap="Device22ResultMap">

        WITH RankedLogs AS (
        select ROW_NUMBER() OVER (PARTITION BY plant_id,device_serial_number
        ORDER BY
        id desc) as rn, id
        from device_log22 a where (is_deleted is null or is_deleted =0) and
        <foreach collection="list" item="item" separator="or" open="(" close=")">
          (  <if test="item.plantId!=null ">
                plant_id =#{item.plantId},
            </if>
            <if test="item.deviceSerialNumber!=null and item.deviceSerialNumber!=''">
                device_serial_number =#{item.deviceSerialNumber},
            </if>
            <if test="item.deviceDateTime!=null ">
                device_date_time BETWEEN  DATE_FORMAT(#{item.deviceDateTime}, '%Y-%m-%d 00:00:00')
                AND DATE_FORMAT(#{item.deviceDateTime}, '%Y-%m-%d 23:59:59')  ,
            </if>
            )
        </foreach>
        )
        select dl.id,plant_id, device_serial_number, device_date_time, today_export_energy, today_import_energy,
        accumulated_energy_of_positive from  device_log22 dl
        left join RankedLogs b on
        dl.id = b.id
        WHERE
        rn = 1
    </select>

</mapper>
