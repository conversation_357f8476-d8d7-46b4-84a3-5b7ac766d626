/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.company.entity.AgentEntity;
import org.skyworth.ess.company.vo.AgentListVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 代理商公司信息表 服务类
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
public interface IAgentService extends BaseService<AgentEntity>{

	/**
	 * 获取代理商列表
	 *
	 * @param page        分页参数
	 * @param agentArea   合作区域
	 * @param companyName 代理商
	 * @return 分页的代理商列表
	 */
	IPage<AgentListVO> getAgentList(IPage<AgentListVO> page, Integer agentArea, String companyName);

	/**
	 * 新增供应商
	 *
	 * @param agentEntity 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/11/9 18:33
	 **/
	Boolean saveAgent(AgentEntity agentEntity);

	/**
	 * 修改供应商
	 *
	 * @param agentEntity 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/11/9 18:33
	 **/
	Boolean update(AgentEntity agentEntity);

	/**
	 * 删除供应商
	 *
	 * @param ids 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/11/9 18:33
	 **/
	Boolean deleteBatch(List<Long> ids);

	/**
	 * 查询详情
	 *
	 * @param id 入参
	 * @return AgentEntity
	 * <AUTHOR>
	 * @since 2023/11/10 14:54
	 **/
	AgentEntity detail(Long id);
}
