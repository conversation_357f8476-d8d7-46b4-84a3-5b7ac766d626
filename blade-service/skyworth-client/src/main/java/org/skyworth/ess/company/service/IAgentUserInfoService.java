/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.service;

import cn.hutool.json.JSONObject;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 代理商用户信息 服务类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public interface IAgentUserInfoService extends BaseService<AgentUserInfoEntity> {
	/**
	 * 删除代理商人员
	 *
	 * @param agentId 入参
	 * <AUTHOR>
	 * @since 2023/11/27 17:22
	 **/
	void deleteByAgentId(Long agentId);

	/**
	 * 角色注销，切换，删除，清理代理商下面的安装人员和电气工程师
	 *
	 * @param jsonObjectList 入参
	 * <AUTHOR>
	 * @since 2024/1/12 16:02
	 **/
	void deleteUserByJsonList(List<JSONObject> jsonObjectList);

	/**
	 * 根据代理商id查询代理商人员信息
	 *
	 * @param ids 入参
	 * @return List<AgentUserInfoEntity>
	 * <AUTHOR>
	 * @since 2024/3/12 15:48
	 **/
	List<AgentUserInfoEntity> queryAgentUserList(List<Long> ids);

	// 软删除代理商人员信息
	Boolean updateUserInfo4Delete(@Param("userIdList") List<Long> userIdList);

}
