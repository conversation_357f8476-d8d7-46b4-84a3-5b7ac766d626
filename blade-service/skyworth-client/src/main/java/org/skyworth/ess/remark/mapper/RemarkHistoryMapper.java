package org.skyworth.ess.remark.mapper;

import org.skyworth.ess.remark.entity.RemarkHistoryEntity;
import org.skyworth.ess.remark.vo.RemarkHistoryVO;
import org.skyworth.ess.remark.excel.RemarkHistoryExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 站点评论记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
public interface RemarkHistoryMapper extends BaseMapper<RemarkHistoryEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param remarkHistory
	 * @return
	 */
	List<RemarkHistoryVO> selectRemarkHistoryPage(IPage page, RemarkHistoryVO remarkHistory);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<RemarkHistoryExcel> exportRemarkHistory(@Param("ew") Wrapper<RemarkHistoryEntity> queryWrapper);

}
