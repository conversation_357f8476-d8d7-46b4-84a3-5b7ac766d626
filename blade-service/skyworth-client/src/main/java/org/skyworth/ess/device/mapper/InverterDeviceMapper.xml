<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.device.mapper.InverterDeviceMapper">
    <resultMap id="InverterDevicePageResultMap" type="org.skyworth.ess.device.vo.InverterDevicePageVO">
        <result column="country_code" property="countryCode"/>
        <result column="province_code" property="provinceCode"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="plant_id" property="plantId"/>
        <result column="plant_name" property="plantName"/>
        <result column="create_user" property="createdBy"/>
        <result column="rated_power" property="ratedPower"/>
        <result column="device_status" property="deviceStatus"/>
        <result column="install_team" property="installTeam"/>
        <result column="operation_team" property="operationTeam"/>
        <result column="install_date" property="installDate"/>
        <result column="city_code" property="cityCode"/>
        <result column="county_code" property="countyCode"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="operation_company_id" property="operationCompanyId"/>
        <result column="operation_user_id" property="operationUserId"/>
    </resultMap>

    <select id="selectInverterDevicePageByCountry" resultMap="InverterDevicePageResultMap">
        SELECT
        p.id AS plant_id,
        p.plant_name,
        p.country_code,
        p.province_code,
        p.city_code,
        p.county_code,
        p.detail_address,
        p.install_team,
        p.operation_team,
        p.install_date,
        p.create_user,
        p.create_user_account,
        defi.power AS rated_power,
        d.device_serial_number,
        <choose>
            <when test="userType == 'user'">
                if(d21.device_status = 2,if(d21.exist_user_type_alarm = 1,2,1),d21.device_status) as device_status,
            </when>
            <when test="userType == 'agent'">
                if(d21.device_status = 2,if(d21.exist_agent_type_alarm = 1,2,1),d21.device_status) as device_status,
            </when>
            <otherwise>
                device_status,
            </otherwise>
        </choose>
        d.update_time,
        p.operation_company_id,
        p.operation_user_id
        FROM
        wifi_stick_plant d
        LEFT JOIN plant p ON d.plant_id = p.id
        LEFT JOIN device_exit_factory_info defi ON d.device_serial_number = defi.device_serial_number
        left join device_21 d21 on d.device_serial_number = d21.device_serial_number and d.plant_id = d21.plant_id  and d21.is_deleted = 0
        WHERE
        <choose>
            <when test='PageVO.deleteFlag!=null and PageVO.deleteFlag!="" and PageVO.deleteFlag == "1" '>
                p.is_deleted = 1
                AND d.is_deleted = 1
            </when>
            <otherwise>
                p.is_deleted = 0
                AND d.is_deleted = 0
            </otherwise>
        </choose>
        AND defi.is_deleted = 0
        <if test="PageVO.countryCode!=null and PageVO.countryCode!=''">
            AND p.country_code = #{PageVO.countryCode}
        </if>
        <if test="PageVO.provinceCode!=null and PageVO.provinceCode!=''">
            AND p.province_code = #{PageVO.provinceCode}
        </if>
        <if test="PageVO.deviceStatus!=null and PageVO.deviceStatus!=''">
            <if test="PageVO.deviceStatus == 0">
                AND d21.device_status = 0
            </if>
            <if test="PageVO.deviceStatus == 1">
                AND (d21.device_status = 1
                <choose>
                    <when test="userType=='user'">
                        or (d21.device_status = 2 and d21.exist_user_type_alarm = 0)
                    </when>
                    <when test="userType=='agent'">
                        or (d21.device_status = 2 and d21.exist_agent_type_alarm = 0)
                    </when>
                    <otherwise>
                        and 1=1
                    </otherwise>
                </choose>
                )
            </if>
            <if test="PageVO.deviceStatus == 2">
                AND d21.device_status = 2
                <choose>
                    <when test="userType=='user'">
                        and d21.exist_user_type_alarm = 1
                    </when>
                    <when test="userType=='agent'">
                        and d21.exist_agent_type_alarm = 1
                    </when>
                    <otherwise>
                        and 1=1
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="PageVO.plantName!=null and PageVO.plantName!=''">
            and p.plant_name LIKE CONCAT(#{PageVO.plantName}, '%')
        </if>
        <if test="PageVO.deviceSerialNumber!=null and PageVO.deviceSerialNumber!=''">
            and d.device_serial_number LIKE CONCAT(#{PageVO.deviceSerialNumber}, '%')
        </if>

        <if test="PageVO.createUserIdList!=null and PageVO.createUserIdList.size() > 0 ">
            and p.create_user in
            <foreach item="item" index="index" collection="PageVO.createUserIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="PageVO.operateUserIdList!=null and PageVO.operateUserIdList.size() > 0 ">
            and p.operation_user_id in
            <foreach item="item" index="index" collection="PageVO.operateUserIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="PageVO.deptId!=null and PageVO.deptId!= ''">
            and (
            find_in_set(p.operation_company_id ,#{PageVO.deptId}) != 0
            or ( p.create_user =#{PageVO.createUser} and p.operation_company_id is null )
            or ( p.create_user =#{PageVO.createUser} and find_in_set(p.operation_company_id ,#{PageVO.deptId}) = 0 )
            )
            and p.id not in (select plant_id  from plant_agent_unauthorized_user where unauthorized_user_id=#{PageVO.createUser} and is_deleted =0)
        </if>

        <if test="PageVO.operationCompanyDeptId!=null">
            and p.operation_company_id = #{PageVO.operationCompanyDeptId}
        </if>

        <if test="PageVO.operationUserIds!=null and PageVO.operationUserIds.size >0" >
            and p.operation_user_id in
            <foreach collection="PageVO.operationUserIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY d.update_time DESC,d.id desc
    </select>

    <select id="getInverterOverviewByCountry" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        IFNULL(ROUND(SUM(defi.power), 2), 0) AS ratedPowerSum,
        IFNULL(COUNT(d.device_serial_number), 0) AS deviceCount
        FROM
        wifi_stick_plant d
        LEFT JOIN plant p ON d.plant_id = p.id
        LEFT JOIN device_exit_factory_info defi ON d.device_serial_number = defi.device_serial_number
        <where>
            <choose>
                <when test='deleteFlag!=null and deleteFlag!="" and deleteFlag == "1" '>
                    d.is_deleted = 1
                    and p.is_deleted = 1
                </when>
                <otherwise>
                    d.is_deleted = 0
                    and p.is_deleted = 0
                </otherwise>
            </choose>
            and defi.is_deleted = 0
            <if test="plantIds!=null and plantIds.size >0">
                and p.id in
                <foreach collection="plantIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getInverterDetailHead" resultType="org.skyworth.ess.device.vo.InverterDetailHeadVO">
        SELECT wsp.create_user_account AS createdByName,
               wsp.create_user AS createdBy,
               CONVERT(p.id, CHAR)     AS plantId,
               p.plant_name          AS plantName,
               p.detail_address      AS detailAddress,
               p.country_code        AS countryCode,
               p.province_code       AS provinceCode,
               p.city_code           AS cityCode,
               p.county_code           AS countyCode
        FROM wifi_stick_plant wsp
                 INNER JOIN plant p ON wsp.plant_id = p.id
        WHERE wsp.is_deleted = 0
          and p.is_deleted = 0
          and wsp.device_serial_number = #{detail.deviceSerialNumber}
          and p.id = #{detail.plantId}
    </select>

    <select id="getInverterDetailHeadIsDelete" resultType="org.skyworth.ess.device.vo.InverterDetailHeadVO">
        SELECT wsp.create_user_account AS createdByName,
               wsp.create_user AS createdBy,
               CONVERT(p.id, CHAR)     AS plantId,
               p.plant_name          AS plantName,
               p.detail_address      AS detailAddress,
               p.country_code        AS countryCode,
               p.province_code       AS provinceCode,
               p.city_code           AS cityCode,
               p.county_code           AS countyCode
        FROM wifi_stick_plant wsp
                 INNER JOIN plant p ON wsp.plant_id = p.id
        WHERE wsp.is_deleted = 1
          and p.is_deleted = 1
          and wsp.device_serial_number = #{detail.deviceSerialNumber}
          and p.id = #{detail.plantId}
    </select>

    <select id="getPlantInstallInfo" resultType="hashmap">
        SELECT p.install_date              installDate,
               p.create_user               createUser,
               p.create_user_account       createUserAccount,
               p.install_team              installTeam,
               defi.quality_guarantee_year qualityGuaranteeYear,
               defi.exit_factory_date      exitFactoryDate,
               defi.warranty_start_date    warrantyStartDate,
               defi.warranty_deadline      warrantyDeadline,
               defi.device_type            deviceType
        FROM wifi_stick_plant d
                 LEFT JOIN plant p ON d.plant_id = p.id
                 left join device_exit_factory_info defi on defi.device_serial_number = d.device_serial_number
        WHERE d.is_deleted = 0
          and p.is_deleted = 0
          and defi.is_deleted = 0
          and d.device_serial_number = #{deviceSerialNumber}
          and p.id = #{plantId}
    </select>

    <select id="getPlantInstallInfoByIsDelete" resultType="hashmap">
        SELECT p.install_date              installDate,
               p.create_user               createUser,
               p.create_user_account       createUserAccount,
               p.install_team              installTeam,
               defi.quality_guarantee_year qualityGuaranteeYear,
               defi.exit_factory_date      exitFactoryDate,
               defi.warranty_start_date    warrantyStartDate,
               defi.warranty_deadline      warrantyDeadline,
               defi.device_type            deviceType
        FROM wifi_stick_plant d
                 LEFT JOIN plant p ON d.plant_id = p.id
                 left join device_exit_factory_info defi on defi.device_serial_number = d.device_serial_number
        WHERE d.is_deleted = 1
          and p.is_deleted = 1
          and defi.is_deleted = 0
          and d.device_serial_number = #{deviceSerialNumber}
          and p.id = #{plantId}
    </select>
</mapper>
