/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.battery.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.util.BigDecimalSerializer;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 电池每日统计 实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@TableName("battery_everyday_total")
@ApiModel(value = "BatteryEverydayTotal对象", description = "电池每日统计")
@EqualsAndHashCode(callSuper = true)
public class BatteryEverydayTotalEntity extends TenantEntity {

	/**
	 * 站点ID
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "站点ID")
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 统计日期
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd"
	)
	@ApiModelProperty(value = "统计日期")
	private Date totalDate;
	/**
	 * 充电能量
	 */
	@ApiModelProperty(value = "充电能量")
	private BigDecimal batteryDailyChargeEnergy;
	/**
	 * 放电能量
	 */
	@ApiModelProperty(value = "放电能量")
	private BigDecimal batteryDailyDischargeEnergy;
	/**
	 * 累计充电能量
	 */
	@ApiModelProperty(value = "累计充电能量")
	private BigDecimal batteryAccumulatedChargeEnergy;
	/**
	 * 累计放电能量
	 */
	@ApiModelProperty(value = "累计放电能量")
	private BigDecimal batteryAccumulatedDischargeEnergy;
	/**
	 * 吞吐量使用率
	 */
	@ApiModelProperty(value = "吞吐量使用率")
	private BigDecimal throughputUseRate;
	/**
	 * 最高单体电池电压
	 */
	@ApiModelProperty(value = "最高单体电池电压")
	private BigDecimal batteryMaximumCellVoltage;
	/**
	 * 最低单体电池电压
	 */
	@ApiModelProperty(value = "最低单体电池电压")
	private BigDecimal batteryMinimumCellVoltage;
	/**
	 * 最高单体电池温度
	 */
	@ApiModelProperty(value = "最高单体电池温度")
	private BigDecimal batteryMaximumCellTemperature;
	/**
	 * 最低单体电池温度
	 */
	@ApiModelProperty(value = "最低单体电池温度")
	private BigDecimal batteryMinimumCellTemperature;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;


	@ApiModelProperty(value = "")
	private BigDecimal pvTotalInputPower;

	@ApiModelProperty(value = "")
	private BigDecimal todayEnergy;


	@ApiModelProperty(value = "")
	private BigDecimal phaseRWattOfLoad;

	@ApiModelProperty(value = "")
	private BigDecimal phaseRWattOfEps;

	@ApiModelProperty(value = "")
	private BigDecimal todayLoadEnergy;

	@ApiModelProperty(value = "")
	private BigDecimal dailyEnergyToEps;
	// app 饼图字段 begin
	private BigDecimal selfConsumed;

	private BigDecimal fedToGrid;

	private BigDecimal selfSufficiency;

	private BigDecimal fromGrid;
	// app 饼图字段 end
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L1相交流耦合电压，L1 phase voltage of AC Couple")
	private BigDecimal l1PhaseVoltageOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L1相交流耦合电流，L1 phase current of AC Couple")
	private BigDecimal l1PhaseCurrentOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L1相交流耦合功率，L1 phase power of AC Couple")
	private BigDecimal l1PhasePowerOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L2相交流耦合电压，L2 phase voltage of AC Couple")
	private BigDecimal l2PhaseVoltageOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L2相交流耦合电流，L2 phase current of AC Couple")
	private BigDecimal l2PhaseCurrentOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L2相交流耦合功率，L2 phase power of AC Couple")
	private BigDecimal l2PhasePowerOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L3相交流耦合电压，L3 phase voltage of AC Couple")
	private BigDecimal l3PhaseVoltageOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L3相交流耦合电流，L3 phase current of AC Couple")
	private BigDecimal l3PhaseCurrentOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "L3相交流耦合功率，L3 phase power of AC Couple")
	private BigDecimal l3PhasePowerOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "交流耦合频率，Frequency of AC Couple")
	private BigDecimal frequencyOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "今日交流耦合能量（单位kWh），Energy today of AC Couple in kWh")
	private BigDecimal energyTodayOfAcCoupleKwh;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "总交流耦合能量，Energy total of AC Couple")
	private BigDecimal energyTotalOfAcCouple;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "今日交流耦合能量（单位wh)，Energy today of AC Couple in wh")
	private BigDecimal energyTodayOfAcCoupleWh;
	// 并机 begin
	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "PV当日发电")
	private BigDecimal pvlDailyGeneratingEnergySum;

	@JsonSerialize(using = BigDecimalSerializer.class)
	@ApiModelProperty(value = "电池当日充电")
	private BigDecimal batteryDailyChargeEnergyParallel;

	@ApiModelProperty(value = "每日负载总和")
	private BigDecimal dailyEnergyOfLoadSum;

	@ApiModelProperty(value = "每日支持能量总和")
	private BigDecimal dailySupportEnergySumToBackup;

	@ApiModelProperty(value = "今日输入能量")
	private BigDecimal todayImportEnergy ;
	/**  */
	@ApiModelProperty(value = "今日输出能量")
	private BigDecimal todayExportEnergy ;
	// 13E4
	private BigDecimal batteryDailyDischargeEnergyParallel;

	private BigDecimal generatorTodayEnergySum;

	private BigDecimal parallelSelfConsumed;

	private BigDecimal parallelFedToGrid;

	private BigDecimal parallelSelfSufficiency;

	private BigDecimal parallelFromGrid;

	private BigDecimal batteryPowerSum;
	// 并机 end

}
