package org.skyworth.ess.device.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.Device23Entity;
import org.skyworth.ess.device.entity.DeviceCurrentStatusEntity;
import org.skyworth.ess.device.service.IInverterDeviceService;
import org.skyworth.ess.device.vo.*;
import org.skyworth.ess.device.wrapper.Device21Wrapper;
import org.skyworth.ess.device.wrapper.Device23Wrapper;
import org.skyworth.ess.device.wrapper.DeviceCurrentStatusWrapper;
import org.skyworth.ess.event.service.IImportantEventService;
import org.skyworth.ess.event.vo.ImportantEventVO;
import org.skyworth.ess.exception.service.IExceptionLogService;
import org.skyworth.ess.exception.vo.ExceptionLogVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description 逆变器接口控制器
 * @create-time 2023/9/21 09:48:50
 */
@RestController
@AllArgsConstructor
@RequestMapping("/inverterDevice")
@Api(value = "设备/逆变器信息", tags = "设备/逆变器信息接口")
public class InverterDeviceController extends BladeController {

	private final IInverterDeviceService inverterDeviceService;

	private final IImportantEventService importantEventService;

	private final IExceptionLogService exceptionLogService;

	/**
	 * 国家下逆变器 列表查询
	 *
	 * @param inverterDevicePageVO
	 * @param query
	 * @return
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入InverterDevicePageVO")
	@PreAuth("hasPermission('client:inverterDevice:list')")
	public R<IPage<InverterDevicePageVO>> list(InverterDevicePageVO inverterDevicePageVO, Query query) {
		return R.data(inverterDeviceService.selectInverterDevicePageByCountry(Condition.getPage(query), inverterDevicePageVO));
	}


	/**
	 * 国家下逆变器 总览查询
	 *
	 * @param inverterDevicePageVO
	 * @return
	 */
	@GetMapping("/overview")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "总览", notes = "传入InverterDevicePageVO")
	@PreAuth("hasPermission('client:inverterDevice:list')")
	public R<JSONObject> overview(InverterDevicePageVO inverterDevicePageVO) {
		return R.data(inverterDeviceService.getInverterOverviewByCountry(inverterDevicePageVO));
	}

	/**
	 * 单个逆变器  头部查询
	 *
	 * @param detail
	 * @return
	 */
	@GetMapping("/detail/head")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "详情-头部数据", notes = "传入Map<String, Object>")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<InverterDetailHeadVO> detailHead(@ApiIgnore @RequestParam Map<String, Object> detail) {
		return R.data(inverterDeviceService.getInverterDetailHead(detail));
	}


	/**
	 * 单个逆变器  当前状态查询
	 *
	 * @param deviceCurrentStatus
	 * @return
	 */
	@GetMapping("/detail/currentStatus")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "详情-当前状态数据", notes = "传入DeviceCurrentStatusEntity")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<DeviceCurrentStatusVO> detailCurrentStatus(DeviceCurrentStatusEntity deviceCurrentStatus) {
		return R.data(DeviceCurrentStatusWrapper.build().entityVO(inverterDeviceService.getDetailCurrentStatus(deviceCurrentStatus)));
	}

	/**
	 * 单个逆变器  参数查询
	 *
	 * @param device23Entity
	 * @return
	 */
	@GetMapping("/detail/parameter")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "详情-参数信息数据", notes = "传入Device23Entity")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<Device23VO> detailParameter(Device23Entity device23Entity) {
		return R.data(Device23Wrapper.build().entityVO(inverterDeviceService.getDetailParameter(device23Entity)));
	}

	/**
	 * 单个逆变器  设备信息查询
	 *
	 * @param device21Entity
	 * @return
	 */
	@GetMapping("/detail/deviceInfo")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "详情-设备信息数据", notes = "传入Device21Entity")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<Device21VO> detailDeviceInfo(Device21Entity device21Entity) {
		return R.data(Device21Wrapper.build().entityVO(inverterDeviceService.getDetailDeviceInfo(device21Entity)));
	}

	/**
	 * 单个逆变器  安装信息查询
	 *
	 * @param deviceSerialNumber
	 * @return
	 */
	@GetMapping("/detail/installInfo")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "详情-安装信息数据", notes = "传入deviceSerialNumber")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<InverterDeviceInstallVO> detailInstallInfo(String deviceSerialNumber, String plantId) {
		return R.data(inverterDeviceService.getInverterDetailInstallInfo(deviceSerialNumber, plantId));
	}


	/**
	 * 单个逆变器 重要事件分页
	 *
	 * @param importantEventVO
	 * @param query
	 * @return
	 */
	@GetMapping("/detail/eventList")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "详情-重要事件分页", notes = "传入ImportantEventVO")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<IPage<ImportantEventVO>> eventList(ImportantEventVO importantEventVO, Query query) {
		return R.data(importantEventService.selectImportantEventPage(Condition.getPage(query), importantEventVO));
	}

	/**
	 * 单个逆变器 异常信息分页
	 *
	 * @param exceptionLogVO
	 * @param query
	 * @return
	 */
	@GetMapping("/detail/exceptionList")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "详情-异常信息分页", notes = "传入ExceptionLogVO")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<IPage<ExceptionLogVO>> exceptionList(ExceptionLogVO exceptionLogVO, Query query) {
		return R.data(exceptionLogService.selectExceptionLogPage(Condition.getPage(query), exceptionLogVO));
	}


	/**
	 * 单个逆变器 状态曲线
	 *
	 * @param queryCondition
	 * @return
	 */
	@GetMapping("/detail/statusStat")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "详情-状态曲线", notes = "传入QueryDeviceLog22Condition")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<Map<String, List<Object>>> statusStat(InverterReportQueryVO queryCondition) {
		return R.data(inverterDeviceService.selectStatusReport(queryCondition));
	}

	/**
	 * 单个逆变器 状态曲线
	 *
	 * @param queryCondition
	 * @return
	 */
	@GetMapping("/detail/statusStat/export")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "详情-状态曲线导出", notes = "传入QueryDeviceLog22Condition")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public void statusStatExport(InverterReportQueryVO queryCondition, HttpServletResponse response) {
		inverterDeviceService.selectStatusReportExport(queryCondition, response);
	}

	/**
	 * 单个逆变器 每日统计
	 *
	 * @param queryCondition
	 * @return
	 */
	@GetMapping("/detail/everyDayStat")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "详情-每日统计", notes = "传入QueryDeviceLog22Condition")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<JSONArray> everyDayStat(InverterReportQueryVO queryCondition) {
		InverterDayReport inverterDayReport = inverterDeviceService.selectEveryDayStat(queryCondition);
		JSONArray jsonArray = new JSONArray();
		jsonArray.add(inverterDayReport.getTypeList());
		jsonArray.addAll(inverterDayReport.getDataList());
		return R.data(jsonArray);
	}

	/**
	 * 单个逆变器 每日统计
	 *
	 * @param queryCondition
	 * @return
	 */
	@GetMapping("/detail/everyDayStat/export")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "详情-每日统计导出", notes = "传入QueryDeviceLog22Condition")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public void everyDayStatExport(InverterReportQueryVO queryCondition, HttpServletResponse response) {
		inverterDeviceService.selectEveryDayStatExport(queryCondition, response);
	}

	/**
	 * 单个逆变器 总量统计
	 *
	 * @param queryCondition
	 * @return
	 */
	@GetMapping("/detail/energyTotalStat")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "详情-总量统计", notes = "传入QueryDeviceLog22Condition")
	@PreAuth("hasPermission('client:inverterDevice:detail')")
	public R<JSONObject> energyTotalStat(InverterReportQueryVO queryCondition) {
		return R.data(inverterDeviceService.selectEnergyTotalStat(queryCondition));
	}

}
