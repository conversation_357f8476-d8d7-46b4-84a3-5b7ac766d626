/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Date;


/**
 * 设备/逆变器出厂信息表 实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@TableName("device_exit_factory_info")
@ApiModel(value = "DeviceExitFactoryInfo对象", description = "设备/逆变器出厂信息表")
@EqualsAndHashCode(callSuper = true)
public class DeviceExitFactoryInfoEntity extends TenantEntity {
	public DeviceExitFactoryInfoEntity() {
	}

	public DeviceExitFactoryInfoEntity(String deviceSerialNumber) {
		this.deviceSerialNumber = deviceSerialNumber;
	}

	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	@NotNull(message = "deviceSerialNumber can not be empty!")
	private String deviceSerialNumber;
	/**
	 * 设备/逆变器型号
	 */
	@ApiModelProperty(value = "设备/逆变器型号")
	private String deviceType;
	/**
	 * 厂家
	 */
	@ApiModelProperty(value = "厂家")
	private String company;
	/**
	 * 质保年限
	 */
	@ApiModelProperty(value = "质保年限")
	@NotNull(message = "qualityGuaranteeYear can not be empty!")
	private String qualityGuaranteeYear;
	/**
	 * 出厂日期
	 */
	@ApiModelProperty(value = "出厂日期")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@NotNull(message = "exitFactoryDate can not be empty!")
	private LocalDate exitFactoryDate;
	/**
	 * 更换电池后新的质保年限
	 */
	@ApiModelProperty(value = "更换电池后新的质保年限")
	private String newQualityGuaranteeYear;
	/**
	 * 单相/三相
	 */
	@ApiModelProperty(value = "单相/三相")
	private String singleThirdPhase;
	/**
	 * 离网/并网/混合
	 */
	@ApiModelProperty(value = "离网/并网/混合")
	private String netType;
	/**
	 * 功率
	 */
	@ApiModelProperty(value = "功率")
	private String power;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "状态")
	private Integer status;

	/**
	 * 激活日期
	 */
	@ApiModelProperty(value = "激活日期")
	private String activationDate;

	/**
	 * 质保开始日期
	 */
	@ApiModelProperty(value = "质保开始日期")
	private String warrantyStartDate;

	/**
	 * 质保截止日期
	 */
	@ApiModelProperty(value = "质保截止日期")
	private String warrantyDeadline;

	/**
	 * modbus版本
	 */
	@ApiModelProperty(value = "modbus版本")
	private String modbusProtocolVersion;

	/**
	 * 固件批次(业务字典:device_firmware_batch)
	 */
	@ApiModelProperty(value = "固件批次(业务字典:device_firmware_batch)")
	private String firmwareBatch;

	private String warrantyHalvedMark;
}
