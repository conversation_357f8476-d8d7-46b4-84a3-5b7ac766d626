/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.wrapper;

import org.skyworth.ess.device.entity.DeviceCustomModeEntity;
import org.skyworth.ess.device.vo.DeviceCustomModeVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 设备/逆变器自定义模式表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
public class DeviceCustomModeWrapper extends BaseEntityWrapper<DeviceCustomModeEntity, DeviceCustomModeVO>  {

	public static DeviceCustomModeWrapper build() {
		return new DeviceCustomModeWrapper();
 	}

	@Override
	public DeviceCustomModeVO entityVO(DeviceCustomModeEntity deviceCustomMode) {
		if (deviceCustomMode == null) {
			return null;
		}
		DeviceCustomModeVO deviceCustomModeVO = Objects.requireNonNull(BeanUtil.copy(deviceCustomMode, DeviceCustomModeVO.class));

		//User createUser = UserCache.getUser(deviceCustomMode.getCreateUser());
		//User updateUser = UserCache.getUser(deviceCustomMode.getUpdateUser());
		//deviceCustomModeVO.setCreateUserName(createUser.getName());
		//deviceCustomModeVO.setUpdateUserName(updateUser.getName());

		return deviceCustomModeVO;
	}


}
