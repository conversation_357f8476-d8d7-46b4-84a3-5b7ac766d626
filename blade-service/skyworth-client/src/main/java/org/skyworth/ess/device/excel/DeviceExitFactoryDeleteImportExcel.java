/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.common.excel.ExcelBusinessUniqueValidate;
import org.springblade.common.excel.ExcelNotNullValidate;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * 设备/逆变器出厂信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-16
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceExitFactoryDeleteImportExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备/逆变器SN
	 */
	@ColumnWidth(50)
	@ExcelProperty("Inverter SN")
	@ExcelNotNullValidate(message = "inverter Serial Number")
	@ExcelBusinessUniqueValidate(uniqueFlag = true)
	private String deviceSerialNumber;
}
