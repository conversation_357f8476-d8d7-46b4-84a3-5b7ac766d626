/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.service;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.excel.OrderExcel;
import org.skyworth.ess.createorder.order.vo.OrderVO;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.SkyWorthFileEntity;

import java.util.List;
import java.util.Map;


/**
 * 订单表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface IOrderService extends BaseService<OrderEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param order
	 * @return
	 */
	IPage<OrderVO> selectOrderPage(IPage<OrderVO> page, OrderVO order);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<OrderExcel> exportOrder(Wrapper<OrderEntity> queryWrapper);


	R insertOrder(OrderEntity order);

	R<?> selectBaseOrderInfo(OrderEntity order);


	R<?> selectBasicOrderInfo(OrderEntity order);

	R<List<AgentCompanyInfoEntity>> selectAgencyCompany(OrderDTO orderDTO);


	 Map<String, String> getDictBizListByName(String type);

	R<?> selectDeliveryManager(OrderDTO orderDTO);

	 SkyWorthFileEntity getFileAttachmentInfo(List<Long> businessIdList);

	 OrderEntity findRegionInfoByCode(OrderEntity orderEntity);
}
