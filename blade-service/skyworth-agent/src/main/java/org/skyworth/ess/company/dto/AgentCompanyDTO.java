package org.skyworth.ess.company.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单表管理列表 视图实体类
 * org.skyworth.ess.order.vo
 *
 * <AUTHOR>
 * @since 2023/10/27
 */

@Data
@EqualsAndHashCode(callSuper = false)
public class AgentCompanyDTO {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@ApiModelProperty(value = "主键id")
	private Long id;

	/**
	 * 公司名称
	 */
	@ApiModelProperty(value = "公司名称")
	private String companyName;

	/**
	 * 代理商编号
	 */
	@ApiModelProperty(value = "代理商编号")
	private String agentNumber;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private Long createUser;

	/**
	 * 创建部门
	 */
	@ApiModelProperty(value = "创建部门")
	private Long deptId;
	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户id")
	private String tenantId;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;


}
