package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 光伏板 视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/28 - 11 - 28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SolarPanelsDTO extends SkyWorthEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 光伏板图片业务主键
	 */
	@ApiModelProperty(value = "光伏板图片业务主键")
	private Long panelsImgBizKey;
	/**
	 * 序号和型号图片业务主键
	 */
	@ApiModelProperty(value = "序号和型号图片业务主键")
	private Long serialNumVerificationImgBizKey;
	/**
	 * 船损检验图片业务主键
	 */
	@ApiModelProperty(value = "船损检验图片业务主键")
	private Long inspecationShipDamageImgBizKey;
	/**
	 * 光伏板的方向和倾斜角度图片业务主键
	 */
	@ApiModelProperty(value = "光伏板的方向和倾斜角度图片业务主键")
	private Long panelsVerifyPanelTiltImgBizKey;
	/**
	 * 锚杆检查图片业务主键
	 */
	@ApiModelProperty(value = "锚杆检查图片业务主键")
	private Long inspectRoofFlashingImgBizKey;
	/**
	 * 密封接头和开口图片业务主键
	 */
	@ApiModelProperty(value = "密封接头和开口图片业务主键")
	private Long allRoofSealedImgBizKey;
	/**
	 * 电缆和布线图片业务主键
	 */
	@ApiModelProperty(value = "电缆和布线图片业务主键")
	private Long panelsVerifyCableWiringImgBizKey;
	/**
	 * pvc管使用图片业务主键
	 */
	@ApiModelProperty(value = "pvc管使用图片业务主键")
	private Long confirmPvcPipeImgBizKey;

}
