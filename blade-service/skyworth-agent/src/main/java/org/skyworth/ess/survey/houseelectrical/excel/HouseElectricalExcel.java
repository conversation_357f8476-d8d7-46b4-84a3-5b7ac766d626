/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.houseelectrical.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;


/**
 * 踏勘房屋电气信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class HouseElectricalExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private Long orderId;
	/**
	 * 电力基础设施
	 */
	@ColumnWidth(20)
	@ExcelProperty("电力基础设施")
	private String infrastructure;
	/**
	 * 电力基础设施说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("电力基础设施说明")
	private String infrastructureRemark;
	/**
	 * 电力基础设施图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("电力基础设施图片key")
	private Long infrastructureImgBizKey;
	/**
	 * 备用发电机
	 */
	@ColumnWidth(20)
	@ExcelProperty("备用发电机")
	private String backupGenerator;
	/**
	 * 备用发电机说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("备用发电机说明")
	private String backupGeneratorRemark;
	/**
	 * 备用发电机图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("备用发电机图片key")
	private Long backupGeneratorImgBizKey;
	/**
	 * 备用电源
	 */
	@ColumnWidth(20)
	@ExcelProperty("备用电源")
	private String backupPowerSource;
	/**
	 * 备用电源图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("备用电源图片key")
	private Long backupPowerSourceImgBizKey;
	/**
	 * 电力条件
	 */
	@ColumnWidth(20)
	@ExcelProperty("电力条件")
	private String electricalCondition;
	/**
	 * 电力条件图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("电力条件图片key")
	private Long electricalConditionImgBizKey;
	/**
	 * 电线确认
	 */
	@ColumnWidth(20)
	@ExcelProperty("电线确认")
	private String routingConfirmation;
	/**
	 * 电线确认图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("电线确认图片key")
	private Long routingConfirmationImgBizKey;
	/**
	 * 电线草案
	 */
	@ColumnWidth(20)
	@ExcelProperty("电线草案")
	private String routingDraft;
	/**
	 * 电线草案图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("电线草案图片key")
	private Long routingDraftImgBizKey;
	/**
	 * 检查布线
	 */
	@ColumnWidth(20)
	@ExcelProperty("检查布线")
	private String inspectWiring;
	/**
	 * 检查布线图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("检查布线图片key")
	private Long inspectWiringImgBizKey;
	/**
	 * 指定升级
	 */
	@ColumnWidth(20)
	@ExcelProperty("指定升级")
	private String upgradeRequired;
	/**
	 * 指定升级图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("指定升级图片key")
	private Long upgradeRequiredImgBizKey;
	/**
	 * 月功率图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("月功率图片key")
	private Long powerMonthlyImgBizKey;
	/**
	 * 年功率图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("年功率图片key")
	private Long powerAnnualImgBizKey;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}
