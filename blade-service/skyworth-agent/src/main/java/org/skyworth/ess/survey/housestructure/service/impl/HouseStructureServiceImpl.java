/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.housestructure.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.survey.housestructure.entity.HouseStructureEntity;
import org.skyworth.ess.survey.housestructure.excel.HouseStructureExcel;
import org.skyworth.ess.survey.housestructure.mapper.HouseStructureMapper;
import org.skyworth.ess.survey.housestructure.service.IHouseStructureService;
import org.skyworth.ess.survey.housestructure.vo.HouseStructureVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 踏勘房屋结构信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Service
public class HouseStructureServiceImpl extends BaseServiceImpl<HouseStructureMapper, HouseStructureEntity> implements IHouseStructureService {

	@Override
	public IPage<HouseStructureVO> selectHouseStructurePage(IPage<HouseStructureVO> page, HouseStructureVO houseStructure) {
		return page.setRecords(baseMapper.selectHouseStructurePage(page, houseStructure));
	}


	@Override
	public List<HouseStructureExcel> exportHouseStructure(Wrapper<HouseStructureEntity> queryWrapper) {
		List<HouseStructureExcel> houseStructureList = baseMapper.exportHouseStructure(queryWrapper);
		//houseStructureList.forEach(houseStructure -> {
		//	houseStructure.setTypeName(DictCache.getValue(DictEnum.YES_NO, HouseStructure.getType()));
		//});
		return houseStructureList;
	}

}
