/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 代理商文件管理 实体类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@TableName("agent_file_info")
@ApiModel(value = "AgentFileInfo对象", description = "代理商文件管理")
@EqualsAndHashCode(callSuper = true)
public class AgentFileInfoEntity extends SkyWorthEntity {

	/**
	 * 代理商id
	 */
	@ApiModelProperty(value = "代理商id")
	private Long agentId;
	/**
	 * 代理商上传文件业务主键
	 */
	@ApiModelProperty(value = "代理商上传文件业务主键")
	private Long fileDocBizKey;
	/**
	 * 文件别名
	 */
	@ApiModelProperty(value = "文件别名")
	private String fileAliasing;

}
