/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.houseelectrical.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.survey.houseelectrical.entity.HouseElectricalEntity;
import org.skyworth.ess.survey.houseelectrical.excel.HouseElectricalExcel;
import org.skyworth.ess.survey.houseelectrical.vo.HouseElectricalVO;
import org.springblade.core.mp.base.BaseService;

import java.util.List;

/**
 * 踏勘房屋电气信息 服务类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface IHouseElectricalService extends BaseService<HouseElectricalEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param houseElectrical
	 * @return
	 */
	IPage<HouseElectricalVO> selectHouseElectricalPage(IPage<HouseElectricalVO> page, HouseElectricalVO houseElectrical);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<HouseElectricalExcel> exportHouseElectrical(Wrapper<HouseElectricalEntity> queryWrapper);

}
