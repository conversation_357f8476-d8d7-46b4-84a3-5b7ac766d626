package org.skyworth.ess.survey.constant;

/**
 * 踏勘信息模块枚举类
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public enum HouseModuleType {
	HOUSEINFO(1, "HouseInfo"),
	TECHNICIANSIGNATURE(2, "TechnicianSignature"),
	TECHNICIANOWNERPHOTO(3, "TechnicianOwnerPhoto"),
	HOUSEELECTRICAL(4,"HouseElectrical"),
	HOUSEELECTRICALAPPLY(5,"HouseElectricalApply"),
	ELECTRICIAN(6, "Electrician"),
	ELECTRICIANSIGNATURE(7, "ElectricianSignature"),
	ELECTRICIANOWNERPHOTO(8, "ElectricianOwnerPhoto"),
	LANDLORDSIGN(9, "LandlordSign");

	private Integer type;
	private String dec;

	HouseModuleType(Integer type, String dec) {
		this.type = type;
		this.dec = dec;
	}

	public static String getDecByType(Integer type) {
		for (HouseModuleType houseModuleType : HouseModuleType.values()) {
			if (houseModuleType.getType().equals(type)) {
				return houseModuleType.getDec();
			}
		}
		return null;
	}


	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDec() {
		return dec;
	}

	public void setDec(String dec) {
		this.dec = dec;
	}
}
