package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 测试视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/29 - 11 - 29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemTestingDTO extends SkyWorthEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 逆变器状态图片业务主键
	 */
	@ApiModelProperty(value = "逆变器状态图片业务主键")
	private Long stConfirmInverterStatusImgBizKey;

}
