/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.housestructure.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 踏勘房屋结构信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("survey_house_structure")
@ApiModel(value = "HouseStructure对象", description = "踏勘房屋结构信息")
@EqualsAndHashCode(callSuper = true)
public class HouseStructureEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 安装类型（数据字典agent_type_of_install）
	 */
	@ApiModelProperty(value = "安装类型（数据字典agent_type_of_install）")
	private String typeOfInstall;
	/**
	 * 安装类型说明
	 */
	@ApiModelProperty(value = "安装类型说明")
	private String typeOfInstallRemark;
	/**
	 * 普通进入
	 */
	@ApiModelProperty(value = "普通进入")
	private String accessEasy;
	/**
	 * 指定进入
	 */
	@ApiModelProperty(value = "指定进入")
	private String accessSpecify;
	/**
	 * 建筑许可
	 */
	@ApiModelProperty(value = "建筑许可")
	private String buildingPermit;

	/**
	 * 建筑许可图片key
	 */
	@ApiModelProperty(value = "建筑许可图片key")
	private Long buildingPermitImgBizKey;

	/**
	 * 建筑许可说明
	 */
	@ApiModelProperty(value = "建筑许可说明")
	private String buildingPermitRemark;
	/**
	 * 电气许可
	 */
	@ApiModelProperty(value = "电气许可")
	private String electricalPermit;

	/**
	 * 电气许可图片key
	 */
	@ApiModelProperty(value = "电气许可图片key")
	private Long electricalPermitImgBizKey;

	/**
	 * 电气许可说明
	 */
	@ApiModelProperty(value = "电气许可说明")
	private String electricalPermitRemark;
	/**
	 * 分区许可
	 */
	@ApiModelProperty(value = "分区许可")
	private String zoningPermit;

	/**
	 * 分区许可图片key
	 */
	@ApiModelProperty(value = "分区许可图片key")
	private Long zoningPermitImgBizKey;

	/**
	 * 分区许可说明
	 */
	@ApiModelProperty(value = "分区许可说明")
	private String zoningPermitRemark;
	/**
	 * 房屋建筑材料类型
	 */
	@ApiModelProperty(value = "房屋建筑材料类型")
	private String buildingMaterialType;

	/**
	 * 其它房屋建筑材料类型
	 */
	@ApiModelProperty(value = "其它房屋建筑材料类型")
	private String buildingMaterialTypeOther;

	/**
	 * 房屋建筑材料类型图片key
	 */
	@ApiModelProperty(value = "房屋建筑材料类型图片key")
	private Long buildingMaterialTypeImgBizKey;


	/**
	 * 房屋正面图片key
	 */
	@ApiModelProperty(value = "房屋正面图片key")
	private Long frontViewOfHouseImgBizKey;


	/**
	 * 特殊图图片key
	 */
	@ApiModelProperty(value = "特殊图图片key")
	private Long specialRequestImgBizKey;

	/**
	 * 屋顶类型
	 */
	@ApiModelProperty(value = "屋顶类型")
	private String roofType;
	/**
	 * 其它屋顶类型
	 */
	@ApiModelProperty(value = "其它屋顶类型")
	private String roofTypeOther;
	/**
	 * 屋顶类型图片key
	 */
	@ApiModelProperty(value = "屋顶类型图片key")
	private Long roofTypeImgBizKey;


	/**
	 * 屋顶材料
	 */
	@ApiModelProperty(value = "屋顶材料")
	private String roofMaterial;

	/**
	 * 其它屋顶材料
	 */
	@ApiModelProperty(value = "其它屋顶材料")
	private String roofMaterialOther;

	/**
	 * 屋顶材料图片key
	 */
	@ApiModelProperty(value = "屋顶材料图片key")
	private Long roofMaterialImgBizKey;
	/**
	 * 屋顶长度
	 */
	@ApiModelProperty(value = "屋顶长度")
	private Double roofLength;

	/**
	 * 屋顶宽度
	 */
	@ApiModelProperty(value = "屋顶宽度")
	private Double roofWidth;

	/**
	 * 屋顶大小图片
	 */
	@ApiModelProperty(value = "屋顶大小图片")
	private Long roofSizeImgBizKey;

	/**
	 * 屋顶檐沟
	 */
	@ApiModelProperty(value = "屋顶檐沟")
	private String roofGutter;

	/**
	 * 屋顶檐沟说明
	 */
	private String roofGutterRemark;


	/**
	 * 屋顶檐沟图片key
	 */
	@ApiModelProperty(value = "屋顶檐沟图片key")
	private Long roofGutterImgBizKey;
	/**
	 * 屋顶障碍物
	 */
	@ApiModelProperty(value = "屋顶障碍物")
	private String roofOcclusion;


	/**
	 * 其它屋顶障碍物
	 */
	@ApiModelProperty(value = "其它屋顶障碍物")
	private String roofOcclusionOther;

	/**
	 * 屋顶障碍物图片key
	 */
	@ApiModelProperty(value = "屋顶障碍物图片key")
	private Long roofOcclusionImgBizKey;
	/**
	 * 太阳能电池板安装位置
	 */
	@ApiModelProperty(value = "太阳能电池板安装位置")
	private String solarPanelsMountingPosition;
	/**
	 * 太阳能电池板其它安装位置
	 */
	@ApiModelProperty(value = "太阳能电池板其它安装位置")
	private String solarPanelsMountingPositionOther;
	/**
	 * 安装结构图片key
	 */
	@ApiModelProperty(value = "安装结构图片key")
	private Long solarPanelsMountingPositionImgBizKey;

	/**
	 * 板类型
	 */
	@ApiModelProperty(value = "板类型")
	private String panelType;
	/**
	 * 其它板类型
	 */
	@ApiModelProperty(value = "其它板类型")
	private String panelTypeOther;
	/**
	 * 板类型图片key
	 */
	@ApiModelProperty(value = "板类型图片key")
	private Long panelTypeImgBizKey;

	/**
	 * 板方向
	 */
	@ApiModelProperty(value = "板方向")
	private String panelOrientation;
	/**
	 * 其它板方向
	 */
	@ApiModelProperty(value = "其它板方向")
	private String panelOrientationOther;
	/**
	 * 板方向图片key
	 */
	@ApiModelProperty(value = "板方向图片key")
	private Long panelOrientationImgBizKey;

	/**
	 * 上传文档
	 */
	@ApiModelProperty(value = "上传文档")
	private String relevantDoc;

	/**
	 * 其它文档
	 */
	@ApiModelProperty(value = "其它文档")
	private String relevantDocOther;

	/**
	 * 上传文档图片key
	 */
	@ApiModelProperty(value = "上传文档图片key")
	private Long relevantDocBizKey;
	/**
	 * 特殊要求
	 */
	@ApiModelProperty(value = "特殊要求")
	private String specialRequest;


	@ApiModelProperty(value = "app端第二页保存标识")
	private Integer pageSaveFlg;

	/**
	 * 屋顶损坏程度
	 */
	@ApiModelProperty(value = "屋顶损坏程度")
	private String roofPhysicalDamages;

	/**
	 * 屋顶损坏程度其它描述
	 */
	@ApiModelProperty(value = "屋顶损坏程度其它描述")
	private String roofPhysicalDamagesOther;


	/**
	 * 屋顶损坏程度图片key
	 */
	@ApiModelProperty(value = "屋顶损坏程度图片key")
	private Long roofPhysicalDamagesImgBizKey;

	/**
	 * 瓷砖损坏程度
	 */
	@ApiModelProperty(value = "瓷砖损坏程度")
	private String looseDisplacedTiles;

	/**
	 * 瓷砖损坏程度其它描述
	 */
	@ApiModelProperty(value = "瓷砖损坏程度其它描述")
	private String looseDisplacedTilesOther;

	/**
	 * 瓷砖损坏程度图片key
	 */
	@ApiModelProperty(value = "瓷砖损坏程度图片key")
	private Long looseDisplacedTilesImgBizKey;


	/**
	 * 踏勘附加文档key
	 */
	@ApiModelProperty(value = "踏勘附加文档key")
	private Long additionalSurveyElementBizKey;

	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@TableField(exist = false)
	private String projectType;

	@TableField(exist = false)
	private String projectTypeOtherRemark;

	@TableField(exist = false)
	private Long technicianSignImgBizKey;

	@TableField(exist = false)
	private Long technicianPhotoImgBizKey;

	@TableField(exist = false)
	private Date surveySubmitDate;

}
