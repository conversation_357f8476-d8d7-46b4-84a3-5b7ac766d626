/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.electricalcomponents.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 施工-电器元件信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@TableName("qc_electrical_components")
@ApiModel(value = "ElectricalComponents对象", description = "施工-电器元件信息")
@EqualsAndHashCode(callSuper = true)
public class ElectricalComponentsEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 电缆布线图片业务主键
	 */
	@ApiModelProperty(value = "电缆布线图片业务主键")
	private Long electricalVerifyCableWiringImgBizKey;
	/**
	 * 确认使用管道图片业务主键
	 */
	@ApiModelProperty(value = "确认使用管道图片业务主键")
	private Long confirmUseCondultImgBizKey;
	/**
	 * 密封接头和开口图片业务主键
	 */
	@ApiModelProperty(value = "密封接头和开口图片业务主键")
	private Long allSealedJointsImgBizKey;
	/**
	 * 电器箱图片业务主键
	 */
	@ApiModelProperty(value = "电器箱图片业务主键")
	private Long electricalBoxImgBizKey;
	/**
	 * 检查发电机电池图片的业务主键
	 */
	@ApiModelProperty(value = "检查发电机电池图片的业务主键")
	private Long checkGeneratorBatteryImgBizKey;
	/**
	 * 电表或CT图片业务主键
	 */
	@ApiModelProperty(value = "电表或CT图片业务主键")
	private Long currentMeterCtImgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}
