/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.survey.houseelectricalapply.entity.HouseElectricalApplyEntity;
import org.skyworth.ess.survey.houseelectricalapply.excel.HouseElectricalApplyExcel;
import org.skyworth.ess.survey.houseelectricalapply.service.IHouseElectricalApplyService;
import org.skyworth.ess.survey.houseelectricalapply.vo.HouseElectricalApplyVO;
import org.skyworth.ess.survey.houseelectricalapply.wrapper.HouseElectricalApplyWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 踏勘房屋电气应用设备 控制器
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-houseElectricalApply/houseElectricalApply")
@Api(value = "踏勘房屋电气应用设备", tags = "踏勘房屋电气应用设备接口")
public class HouseElectricalApplyController extends BladeController {

	private final IHouseElectricalApplyService houseElectricalApplyService;

	/**
	 * 踏勘房屋电气应用设备 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入houseElectricalApply")
	public R<HouseElectricalApplyVO> detail(HouseElectricalApplyEntity houseElectricalApply) {
		HouseElectricalApplyEntity detail = houseElectricalApplyService.getOne(Condition.getQueryWrapper(houseElectricalApply));
		return R.data(HouseElectricalApplyWrapper.build().entityVO(detail));
	}
	/**
	 * 踏勘房屋电气应用设备 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入houseElectricalApply")
	public R<IPage<HouseElectricalApplyVO>> list(@ApiIgnore @RequestParam Map<String, Object> houseElectricalApply, Query query) {
		IPage<HouseElectricalApplyEntity> pages = houseElectricalApplyService.page(Condition.getPage(query), Condition.getQueryWrapper(houseElectricalApply, HouseElectricalApplyEntity.class));
		return R.data(HouseElectricalApplyWrapper.build().pageVO(pages));
	}

	/**
	 * 踏勘房屋电气应用设备 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入houseElectricalApply")
	public R<IPage<HouseElectricalApplyVO>> page(HouseElectricalApplyVO houseElectricalApply, Query query) {
		IPage<HouseElectricalApplyVO> pages = houseElectricalApplyService.selectHouseElectricalApplyPage(Condition.getPage(query), houseElectricalApply);
		return R.data(pages);
	}

	/**
	 * 踏勘房屋电气应用设备 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入houseElectricalApply")
	public R save(@Valid @RequestBody HouseElectricalApplyEntity houseElectricalApply) {
		return R.status(houseElectricalApplyService.save(houseElectricalApply));
	}

	/**
	 * 踏勘房屋电气应用设备 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入houseElectricalApply")
	public R update(@Valid @RequestBody HouseElectricalApplyEntity houseElectricalApply) {
		return R.status(houseElectricalApplyService.updateById(houseElectricalApply));
	}

	/**
	 * 踏勘房屋电气应用设备 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入houseElectricalApply")
	public R submit(@Valid @RequestBody HouseElectricalApplyEntity houseElectricalApply) {
		return R.status(houseElectricalApplyService.saveOrUpdate(houseElectricalApply));
	}

	/**
	 * 踏勘房屋电气应用设备 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(houseElectricalApplyService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-houseElectricalApply")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入houseElectricalApply")
	public void exportHouseElectricalApply(@ApiIgnore @RequestParam Map<String, Object> houseElectricalApply, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<HouseElectricalApplyEntity> queryWrapper = Condition.getQueryWrapper(houseElectricalApply, HouseElectricalApplyEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(HouseElectricalApply::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(HouseElectricalApplyEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<HouseElectricalApplyExcel> list = houseElectricalApplyService.exportHouseElectricalApply(queryWrapper);
		ExcelUtil.export(response, "踏勘房屋电气应用设备数据" + DateUtil.time(), "踏勘房屋电气应用设备数据表", list, HouseElectricalApplyExcel.class);
	}

}
