/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.housestructure.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;


/**
 * 踏勘房屋结构信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class HouseStructureExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private Long orderId;
	/**
	 * 安装类型（数据字典agent_type_of_install）
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装类型（数据字典agent_type_of_install）")
	private String typeOfInstall;
	/**
	 * 安装类型说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装类型说明")
	private String typeOfInstallRemark;
	/**
	 * 普通进入
	 */
	@ColumnWidth(20)
	@ExcelProperty("普通进入")
	private String accessEasy;
	/**
	 * 指定进入
	 */
	@ColumnWidth(20)
	@ExcelProperty("指定进入")
	private String accessSpecify;
	/**
	 * 建筑许可
	 */
	@ColumnWidth(20)
	@ExcelProperty("建筑许可")
	private String buildingPermit;
	/**
	 * 建筑许可说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("建筑许可说明")
	private String buildingPermitRemark;
	/**
	 * 电气许可
	 */
	@ColumnWidth(20)
	@ExcelProperty("电气许可")
	private String electricalPermit;
	/**
	 * 电气许可说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("电气许可说明")
	private String electricalPermitRemark;
	/**
	 * 分区许可
	 */
	@ColumnWidth(20)
	@ExcelProperty("分区许可")
	private String zoningPermit;
	/**
	 * 分区许可说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("分区许可说明")
	private String zoningPermitRemark;
	/**
	 * 房屋结构
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋结构")
	private String structure;
	/**
	 * 房屋结构图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋结构图片key")
	private Long structureImgBizKey;
	/**
	 * 房屋结构说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋结构说明")
	private String structureRemark;
	/**
	 * 房屋框架
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋框架")
	private String frameType;
	/**
	 * 房屋框架图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋框架图片key")
	private Long frameTypeImgBizKey;
	/**
	 * 房屋框架说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋框架说明")
	private String frameTypeRemark;
	/**
	 * 房屋类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋类型")
	private String houseType;
	/**
	 * 房屋类型图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋类型图片key")
	private Long houseTypeImgBizKey;
	/**
	 * 房屋类型说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋类型说明")
	private String houseTypeRemark;
	/**
	 * 房屋全貌图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋全貌图片key")
	private Long fullViewOfHouseImgBizKey;
	/**
	 * 房屋入口图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("房屋入口图片key")
	private Long fullViewOfEnteranceImgBizKey;
	/**
	 * 俯瞰图图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("俯瞰图图片key")
	private Long verticalViewImgBizKey;
	/**
	 * 屋顶类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶类型")
	private String roofType;
	/**
	 * 屋顶类型图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶类型图片key")
	private Long roofTypeImgBizKey;
	/**
	 * 屋顶材料
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶材料")
	private String roofMaterial;
	/**
	 * 屋顶材料图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶材料图片key")
	private Long roofMaterialImgBizKey;
	/**
	 * 屋顶大小
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶大小")
	private String roofSize;
	/**
	 * 屋顶朝向
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶朝向")
	private String roofFaceDirection;
	/**
	 * 屋顶朝向图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶朝向图片key")
	private Long roofFaceDirectionImgBizKey;
	/**
	 * 屋顶侧边度
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶侧边度")
	private String roofFaceSideAngel;
	/**
	 * 屋顶斜坡度
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶斜坡度")
	private String roofFaceSideSlope;
	/**
	 * 屋顶檐沟图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶檐沟图片key")
	private Long roofGutterImgBizKey;
	/**
	 * 屋顶障碍物
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶障碍物")
	private String roofOcclusion;
	/**
	 * 屋顶障碍物图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("屋顶障碍物图片key")
	private Long roofOcclusionImgBizKey;
	/**
	 * 安装结构
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装结构")
	private String mountingStructure;
	/**
	 * 安装结构图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装结构图片key")
	private Long mountingStructureImgBizKey;
	/**
	 * 板类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("板类型")
	private String panelType;
	/**
	 * 板类型说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("板类型说明")
	private String panelTypeRemark;
	/**
	 * 板类型图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("板类型图片key")
	private Long panelTypeImgBizKey;
	/**
	 * 板方向
	 */
	@ColumnWidth(20)
	@ExcelProperty("板方向")
	private String panelOrientation;
	/**
	 * 板方向说明
	 */
	@ColumnWidth(20)
	@ExcelProperty("板方向说明")
	private String panelOrientationRemark;
	/**
	 * 板方向图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("板方向图片key")
	private Long panelOrientationImgBizKey;
	/**
	 * 上传文档
	 */
	@ColumnWidth(20)
	@ExcelProperty("上传文档")
	private String relevantDoc;
	/**
	 * 上传文档图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("上传文档图片key")
	private Long relevantDocBizKey;
	/**
	 * 特殊要求
	 */
	@ColumnWidth(20)
	@ExcelProperty("特殊要求")
	private String specialRequest;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}
