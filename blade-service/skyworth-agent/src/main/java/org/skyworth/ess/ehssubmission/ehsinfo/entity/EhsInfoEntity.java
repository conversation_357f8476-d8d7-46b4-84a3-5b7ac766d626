/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.ehsinfo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * ehs信息 实体类
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Data
@TableName("ehs_info")
@ApiModel(value = "Info对象", description = "ehs信息")
@EqualsAndHashCode(callSuper = true)
public class EhsInfoEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	@NotNull(message = "{agent.createOrder.orderId.notNull}")
	private Long orderId;

	/**
	 * 安全检查（业务字典agent_member_safety_checklist，多个逗号拼接）
	 */
	@ApiModelProperty(value = "安全检查（业务字典agent_member_safety_checklist，多个逗号拼接）")
	@NotBlank(message = "{agent.ehsSubmission.memberSafetyChecklist.notNull}")
	private String memberSafetyChecklist;
	/**
	 * 安全检查图片key
	 */
	@ApiModelProperty(value = "安全检查图片key")
	private Long memberSafetyChecklistImgBizKey;
	/**
	 * 急救箱图片key
	 */
	@ApiModelProperty(value = "急救箱图片key")
	private Long firstAidKitImgBizKey;
	/**
	 * 救援装备图片key
	 */
	@ApiModelProperty(value = "救援装备图片key")
	private Long rescueKitImgBizKey;
	/**
	 * 灭火器图片key
	 */
	@ApiModelProperty(value = "灭火器图片key")
	private Long fireExtinguisherImgBizKey;
	/**
	 * 通知书图片key
	 */
	@ApiModelProperty(value = "通知书图片key")
	private Long acceptanceLetterImgBizKey;
	/**
	 * 风险图片key
	 */
	@ApiModelProperty(value = "风险图片key")
	private Long riskAssessmentImgBizKey;

	/**
	 * 预任务图片key
	 */
	@ApiModelProperty(value = "预任务图片key")
	private Long preTaskControlImgBizKey;


	/**
	 * 风险列表
	 */
	@ApiModelProperty(value = "风险列表")
	@NotBlank(message = "{agent.ehsSubmission.riskList.notNull}")
	private String riskList;
	/**
	 * 风险解决措施
	 */
	@ApiModelProperty(value = "风险解决措施")
	@NotBlank(message = "{agent.ehsSubmission.riskSolution.notNull}")
	private String riskSolution;
	/**
	 * 声明（1勾选）
	 */
	@ApiModelProperty(value = "声明（1勾选）")
	@NotNull(message = "{agent.ehsSubmission.declaration.notNull}")
	private Integer declaration;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;


	@ApiModelProperty(value = "ehs最新版本时间")
	private String ehsVersion;


	/**
	 * 安全检查名称
	 */
	@TableField(exist = false)
	private List<String> memberSafetyCheckNameList;

}
