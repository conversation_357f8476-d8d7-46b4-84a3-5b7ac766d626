package org.skyworth.ess.company.feign;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.skyworth.ess.company.service.IAgentCompanyInfoService;
import org.skyworth.ess.company.service.IAgentUserInfoService;
import org.skyworth.ess.constant.AgentUserTypeEnum;
import org.skyworth.ess.company.wrapper.AgentCompanyInfoWrapper;
import org.skyworth.ess.dto.OrderWorkFlowDTO;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.feign.IAgentClient;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.common.utils.tool.BeanUtils;
import org.springblade.common.utils.tool.StringUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.tenant.annotation.TenantIgnore;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理商列表 视图实体类
 * org.skyworth.ess.companyInfo.feign
 *
 * <AUTHOR>
 * @since 2023/11/6 - 11 - 06
 */
@ApiIgnore
@RestController
@AllArgsConstructor
public class AgentClient extends BladeController implements IAgentClient {

    private final IAgentCompanyInfoService agentService;
    private final IAgentUserInfoService agentUserInfoService;
    private final IUserSearchClient userSearchClient;
    private final IOrderWorkFlowService iOrderWorkFlowService;
    private IOrderNodeSubStatusService orderNodeSubStatusService;
    private IDictBizClient dictBizClient;

    @TenantIgnore
    @Override
    @GetMapping(AGENT_COMPANY_INFO)
    public R<List<AgentCompanyVO>> agentCompany(@RequestParam("companyName") String companyName) {
        // 获取当前用户的租户ID和部门ID
//		String tenantId = this.getUser().getTenantId();
//		String deptId = this.getUser().getDeptId();
        // 创建查询条件
        LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.likeRight(StringUtils.isNotBlank(companyName), AgentCompanyInfoEntity::getCompanyName, companyName)
                .eq(AgentCompanyInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
        // 根据是否是默认租户，添加不同的筛选条件
//		if (!BladeConstant.ADMIN_TENANT_ID.equals(tenantId)) {
//			queryWrapper.eq(AgentCompanyInfoEntity::getDeptId, deptId);
//		} else {
//			queryWrapper.eq(AgentCompanyInfoEntity::getTenantId, BladeConstant.ADMIN_TENANT_ID);
//		}
        // 执行分页查询
        Page<AgentCompanyInfoEntity> companyInfoPage = agentService.page(new Page<>(1, 100), queryWrapper);
        List<AgentCompanyVO> agentCompanyVOList = AgentCompanyInfoWrapper.build().listVO(companyInfoPage.getRecords());
        return R.data(agentCompanyVOList);
    }

    @Override
    @GetMapping(AGENT_DEPT_ID)
    public R<String> agentDeptId(String agentNumber) {
        LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentCompanyInfoEntity::getAgentNumber, agentNumber)
                .eq(AgentCompanyInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
        AgentCompanyInfoEntity agentCompanyInfoEntity = agentService.getOne(queryWrapper);
        if (agentCompanyInfoEntity != null) {
            return R.data(agentCompanyInfoEntity.getAgentNumber());
        }
        return R.fail("Agent does not exist");
    }

    @Override
    @GetMapping(AGENT_NUMBER)
    public R<AgentCompanyVO> agentNumber(String agentNumber) {
        LambdaQueryWrapper<AgentCompanyInfoEntity> agentCompanyQueryWrapper = new LambdaQueryWrapper<>();
        agentCompanyQueryWrapper.eq(agentNumber != null, AgentCompanyInfoEntity::getAgentNumber, agentNumber)
                .eq(AgentCompanyInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
        AgentCompanyInfoEntity agentCompanyInfoEntity = agentService.getOne(agentCompanyQueryWrapper);
        if (ObjectUtil.isNotNull(agentCompanyInfoEntity)) {
            AgentCompanyVO agentCompanyVO = AgentCompanyInfoWrapper.build().entityVO(agentCompanyInfoEntity);
            return R.data(agentCompanyVO);
        } else {
            return R.fail("The agent code entered is incorrect. Please re-enter it!");
        }
    }

    @Override
    @GetMapping(AGENT_USER_LIST)
    public R<List<JSONObject>> findAgentUserList(String roleCode, Long agentId, String realName) {
        if (StringUtils.isBlank(roleCode) || agentId == null) {
            throw new BusinessException("agent.parameter.notEmpty");
        }
        // 通过角色名称，查询代理商下面挂的人员信息
        String userTypeCode = AgentUserTypeEnum.of(roleCode).getCode();
        if (StringUtils.isBlank(userTypeCode)) {
            throw new BusinessException("agent.parameter.invalid");
        }
        // 通过用户类型查询用户id
        List<AgentUserInfoEntity> agentUserInfoEntityList = agentUserInfoService.list(Wrappers.<AgentUserInfoEntity>lambdaQuery().eq(AgentUserInfoEntity::getUserType, userTypeCode).eq(AgentUserInfoEntity::getAgentId, agentId));
        List<Long> userIds = agentUserInfoEntityList.stream().map(AgentUserInfoEntity::getUserId).collect(Collectors.toList());
        List<User> userList = userSearchClient.listByUserIds(userIds).getData();
        List<JSONObject> jsonObjectList = new ArrayList<>();
        if (CollectionUtils.isNullOrEmpty(userList)) {
            return R.data(jsonObjectList);
        }
        // java过滤数据
        if (StringUtils.isNotEmpty(realName)) {
            userList = userList.stream().filter(a -> a.getRealName().toLowerCase().startsWith(realName.toLowerCase()) || (StringUtils.isNotBlank(a.getPhone()) && a.getPhone().toLowerCase().startsWith(realName.toLowerCase()))).collect(Collectors.toList());
        }
        if (!CollectionUtils.isNullOrEmpty(userList)) {
            userList.forEach(a -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.set("userId", a.getId());
                jsonObject.set("userName", a.getRealName());
                jsonObject.set("phone", a.getPhone());
                jsonObjectList.add(jsonObject);
            });
        }
        userList.clear();
        return R.data(jsonObjectList);
    }

    @Override
    @PostMapping(ORDER_WORK_FLOW_INFO)
    public R<OrderWorkFlowVO> getWorkFlowDataByOrderId(OrderWorkFlowDTO orderWorkFlow) {
        return R.data(iOrderWorkFlowService.getWorkFlowDataByOrderId(orderWorkFlow));
    }

    @Override
    public R<JSONObject> getQcSubNodeAuditInfo(String taskId) {
        JSONObject jsonObject = new JSONObject();

        LambdaQueryWrapper<OrderWorkFlowEntity> flowEq = Wrappers.<OrderWorkFlowEntity>query().lambda()
                .eq(OrderWorkFlowEntity::getTaskId, taskId);

        List<OrderWorkFlowEntity> flowList = iOrderWorkFlowService.list(flowEq);
        if (ValidationUtil.isNotEmpty(flowList) && !flowList.isEmpty()) {
            long orderId = flowList.get(0).getOrderId();
            String nodeType = dictBizClient.getValue("agent_business_order_node_name", "QC-VERIFY").getData();
            LambdaQueryWrapper<OrderNodeSubStatusEntity> orderNode = Wrappers.<OrderNodeSubStatusEntity>query().lambda()
                    .eq(OrderNodeSubStatusEntity::getOrderId, orderId).eq(OrderNodeSubStatusEntity::getNodeName, nodeType).eq(OrderNodeSubStatusEntity::getBusinessType, "approve");
            List<OrderNodeSubStatusEntity> orderNodeSubStatusEntities = orderNodeSubStatusService.list(orderNode);
            if (ValidationUtil.isNotEmpty(orderNodeSubStatusEntities) && !orderNodeSubStatusEntities.isEmpty()) {
                orderNodeSubStatusEntities.stream().parallel().forEach(v -> {
                    Map<String, String> map = new HashMap<>(2);
                    map.put("remark", v.getSubRemark());
                    map.put("status", v.getSubStatus());
                    JSONObject node = new JSONObject();
                    node.putAll(map);
                    jsonObject.set(v.getSubNodeName(), node);
                });
            }
        }
        return R.data(jsonObject);
    }

    @Override
    public R cleanUpAgentUser(List<JSONObject> jsonObjectList) {
        if (CollectionUtils.isNullOrEmpty(jsonObjectList)) {
            return R.fail("The parameter cannot be empty");
        }
        agentUserInfoService.deleteUserByJsonList(jsonObjectList);
        return R.success("success");
    }

    @TenantIgnore
    @Override
    public R<List<AgentCompanyVO>> agentCompanyInfoByIds(List<Long> ids) {
        LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (ValidationUtil.isNotEmpty(ids) && !ids.isEmpty()) {
            queryWrapper.in(AgentCompanyInfoEntity::getDeptId, ids);
        }
		List<AgentCompanyInfoEntity> list = agentService.list(queryWrapper);
		// 执行分页查询
        List<AgentCompanyVO> agentCompanyVOList = AgentCompanyInfoWrapper.build().listVO(list);
        return R.data(agentCompanyVOList);
    }

    @TenantIgnore
    @Override
    public R<List<AgentUserVo>> agentUserInfo(Long agentId, Long deptId) {
        // 通过用户类型查询用户id
        List<AgentUserInfoEntity> agentUserInfoEntityList = agentUserInfoService.list(Wrappers.<AgentUserInfoEntity>lambdaQuery().eq(AgentUserInfoEntity::getAgentId, agentId));
        List<Long> userIds = agentUserInfoEntityList.stream().map(AgentUserInfoEntity::getUserId).collect(Collectors.toList());
        List<User> userList = userSearchClient.listByMappingUser(userIds).getData();
        List<AgentUserVo> agentUserVos = new ArrayList<>();
        if (CollectionUtils.isNullOrEmpty(userList)) {
            return R.data(agentUserVos);
        }
        userList.parallelStream().forEach(v -> {
            AgentUserVo agentUserVo = new AgentUserVo();
            BeanUtils.copyProperties(v, agentUserVo);
            agentUserVos.add(agentUserVo);
        });
        return R.data(agentUserVos);
    }

    @TenantIgnore
    @Override
    public R<List<AgentCompanyVO>> agentCompanyByDeptId(String deptId, Long userId) {

        //获取EPC的代理商人员的userId
        List<User> userList = userSearchClient.listByMappingSourceUser(Collections.singletonList(userId)).getData();
        if (CollectionUtils.isNullOrEmpty(userList)) {
            return R.data(new ArrayList<>());
        }
        List<Long> userIds = userList.stream().map(User::getId).collect(Collectors.toList());

        List<AgentUserInfoEntity> agentUserInfoEntityList = agentUserInfoService.list(Wrappers.<AgentUserInfoEntity>lambdaQuery().in(AgentUserInfoEntity::getUserId, userIds));
        if (CollectionUtils.isNullOrEmpty(agentUserInfoEntityList)) {
            return R.data(new ArrayList<>());
        }
        List<Long> agentIds = agentUserInfoEntityList.stream().map(AgentUserInfoEntity::getAgentId).collect(Collectors.toList());
        LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentCompanyInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
        queryWrapper.in(AgentCompanyInfoEntity::getId, agentIds);
        Page<AgentCompanyInfoEntity> companyInfoPage = agentService.page(new Page<>(1, 1000), queryWrapper);
        List<AgentCompanyVO> agentCompanyVOList = AgentCompanyInfoWrapper.build().listVO(companyInfoPage.getRecords());

        return R.data(agentCompanyVOList);
    }

    @Override
    @PostMapping(SAVE_AGENT_USER_INFO)
    public R<Boolean> saveAgentUserInfo(@RequestBody AgentUserInfoEntity agentUserInfoEntity) {
        return R.data(agentUserInfoService.save(agentUserInfoEntity));
    }

    @Override
    @PostMapping(DELETE_AGENT_USER_INFO)
    public R<Boolean> deleteAgentUserInfo(List<Long> userIdList) {
        return R.data(agentUserInfoService.updateUserInfo4Delete(userIdList));
    }
}
