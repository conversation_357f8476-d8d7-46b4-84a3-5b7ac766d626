package org.skyworth.ess.survey.constant;

/**
 * 操作类型枚举类
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public enum OperationType {
	SAVE(1, "save");

	private Integer type;
	private String dec;

	OperationType(Integer type, String dec) {
		this.type = type;
		this.dec = dec;
	}

	public static String getDecByType(Integer type) {
		for (OperationType houseModuleType : OperationType.values()) {
			if (houseModuleType.getType().equals(type)) {
				return houseModuleType.getDec();
			}
		}
		return null;
	}


	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDec() {
		return dec;
	}

	public void setDec(String dec) {
		this.dec = dec;
	}
}
