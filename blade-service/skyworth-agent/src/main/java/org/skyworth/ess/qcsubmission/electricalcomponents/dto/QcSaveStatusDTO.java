package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Qc小节点保存状态
 * org.skyworth.ess.qcsubmission.electricalcomponents.dto
 *
 * <AUTHOR>
 * @since 2023/12/6
 */
@Data
public class QcSaveStatusDTO {
	private static final long serialVersionUID = 1L;
	/**
	 * 业主签名保存状态
	 */
	@ApiModelProperty(value = "业主签名保存状态")
	private String landlordVerificationSaveStatus;
	/**
	 * 逆变器安装保存状态
	 */
	@ApiModelProperty(value = "逆变器安装保存状态")
	private String inverterSaveStatus;
	/**
	 * 太阳能板安装保存状态
	 */
	@ApiModelProperty(value = "太阳能板安装保存状态")
	private String solarPanelsSaveStatus;
	/**
	 * 电气组件保存状态
	 */
	@ApiModelProperty(value = "电气组件保存状态")
	private String electricalComponentsSaveStatus;
	/**
	 * 接地设备保存状态
	 */
	@ApiModelProperty(value = "接地设备保存状态")
	private String groundingEquipmentSaveStatus;
	/**
	 * 安装机架保存状态
	 */
	@ApiModelProperty(value = "安装机架保存状态")
	private String mountingAndRackingSaveStatus;
	/**
	 * 安装与遵守保存状态
	 */
	@ApiModelProperty(value = "安装与遵守保存状态")
	private String safetyAndComplianceSaveStatus;
	/**
	 * 系统测试保存状态
	 */
	@ApiModelProperty(value = "系统测试保存状态")
	private String systemTestingSaveStatus;
	/**
	 * 最终检验保存状态
	 */
	@ApiModelProperty(value = "最终检验保存状态")
	private String finalInspectionSaveStatus;

}
