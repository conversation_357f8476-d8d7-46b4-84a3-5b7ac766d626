/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.installationdate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.installationdate.service.InstallationDateService;
import org.skyworth.ess.qcsubmission.installrelatedInfo.dto.InstallRelatedInfoDTO;
import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import org.skyworth.ess.qcsubmission.installrelatedInfo.mapper.InstallRelatedInfoMapper;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;

/**
 * 安装相关信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Service
@AllArgsConstructor
public class InstallationDateServiceImpl extends BaseServiceImpl<InstallRelatedInfoMapper, InstallRelatedInfoEntity> implements InstallationDateService {

	private final IReviewOrderService iReviewOrderService;

	private final IOrderService iOrderService;

	/**
	 * @Description: 审核确认施工日期
	 * @Param: [installRelatedInfoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/5 9:24
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> examineInstallationDate(InstallRelatedInfoDTO installRelatedInfoDTO) {
		//修改安装相关信息
		boolean updateInstallInfoFlag = updateInstallInfo(installRelatedInfoDTO.getInstallRelatedInfoEntity(), installRelatedInfoDTO);
		//审批流转
		if (updateInstallInfoFlag) {
			R<?> examineApproveResult = iReviewOrderService.examineApprove(installRelatedInfoDTO.getOrderFlowDTO());
			if (!examineApproveResult.isSuccess()) {
				throw new BusinessException("agent.reviewOrder.workFlow.fail");
			}
		}
		return R.status(true);
	}


	/**
	 * @Description: 查询安装日期和文件信息
	 * @Param: [installRelatedInfoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/5 9:45
	 **/
	@Override
	public R<?> selectInstallDateInfo(InstallRelatedInfoDTO installRelatedInfoDTO) {
		if (ObjectUtils.isEmpty(installRelatedInfoDTO)) {
			throw new BusinessException("agent.installationDate.selectInstallDateInfo.installRelatedInfoDTO.notNull");
		}
		if (ObjectUtils.isEmpty(installRelatedInfoDTO.getInstallRelatedInfoEntity().getOrderId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		//获取安装日期和文件Key
		InstallRelatedInfoDTO installRelatedResultInfo = new InstallRelatedInfoDTO();
		InstallRelatedInfoEntity installRelatedInfoEntity = getInstallRelatedInfo(installRelatedInfoDTO.getInstallRelatedInfoEntity());
		if (ObjectUtils.isEmpty(installRelatedInfoEntity)) {
			return R.data(installRelatedResultInfo);
		}
		installRelatedResultInfo.setInstallRelatedInfoEntity(installRelatedInfoEntity);
		//获取附件信息
		SkyWorthFileEntity skyWorthFileEntity = this.iOrderService.getFileAttachmentInfo(Collections.singletonList(installRelatedInfoEntity.getFeedbackDocImgBizKey()));
		if (ObjectUtils.isNotEmpty(skyWorthFileEntity)) {
			//附件信息
			installRelatedResultInfo.setAttachmentMap(skyWorthFileEntity.getAttachmentMap());
			//附件描述
			installRelatedResultInfo.setImgDescViewMap(skyWorthFileEntity.getImgDescViewMap());
		}
		return R.data(installRelatedResultInfo);
	}


	/**
	 * @Description: 获取安装相关信息
	 * @Param: [installRelatedInfoEntity]
	 * @Return: org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity
	 * @Author: baixu
	 * @Date: 2023/12/5 9:48
	 **/
	private InstallRelatedInfoEntity getInstallRelatedInfo(InstallRelatedInfoEntity installRelatedInfoEntity) {
		LambdaQueryWrapper<InstallRelatedInfoEntity> infoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		infoEntityLambdaQueryWrapper.eq(InstallRelatedInfoEntity::getOrderId, installRelatedInfoEntity.getOrderId());
		infoEntityLambdaQueryWrapper.select(InstallRelatedInfoEntity::getOrderId, InstallRelatedInfoEntity::getFeedbackDocImgBizKey, InstallRelatedInfoEntity::getConstructionDate);
		return this.getOne(infoEntityLambdaQueryWrapper);
	}


	/**
	 * @Description: 修改安装相关信息
	 * @Param: [installRelatedInfoEntity, installRelatedInfoDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/5 9:36
	 **/
	@Transactional(rollbackFor = Exception.class)
	public boolean updateInstallInfo(InstallRelatedInfoEntity installRelatedInfoEntity, InstallRelatedInfoDTO installRelatedInfoDTO) {
		//保存或修改附件信息
		boolean updateInstallInfoFlag = true;
		boolean saveFlag = this.save(installRelatedInfoEntity);
		if (!saveFlag) {
			throw new BusinessException("agent.installationDate.updateInstallInfo.saveInstallRelatedInfo.fail");
		}
		if (ObjectUtils.isNotEmpty(installRelatedInfoDTO.getSkyWorthFileEntity())) {
			//保存反馈文件
			SkyWorthFileEntity skyWorthFileEntity = installRelatedInfoDTO.getSkyWorthFileEntity();
			updateInstallInfoFlag = this.iReviewOrderService.setOrUpdateFileImgInfo(skyWorthFileEntity);
		}
		return updateInstallInfoFlag;
	}
}
