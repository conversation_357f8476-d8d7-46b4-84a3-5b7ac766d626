/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;

/**
 * 操作日志表 实体类
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@TableName("agent_operation_log")
@ApiModel(value = "AgentOperationLog对象", description = "操作日志表")
@EqualsAndHashCode(callSuper = true)
public class AgentOperationLogEntity extends SkyWorthEntity {

	/**
	 * 业务主键id
	 */
	@ApiModelProperty(value = "业务主键id")
	private String businessId;
	/**
	 * 操作
	 */
	@ApiModelProperty(value = "操作")
	private String operateType;
	/**
	 * 所属功能模块
	 */
	@ApiModelProperty(value = "所属功能模块")
	private String modelType;
	/**
	 * 修改内容:json对象
	 */
	@ApiModelProperty(value = "修改内容:json对象")
	private String requestBody;
	/**
	 * 响应
	 */
	@ApiModelProperty(value = "响应")
	private String responseBody;

}
