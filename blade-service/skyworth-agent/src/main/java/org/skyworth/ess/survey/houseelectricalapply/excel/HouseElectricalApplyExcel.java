/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.houseelectricalapply.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;


/**
 * 踏勘房屋电气应用设备 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class HouseElectricalApplyExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private String orderId;
	/**
	 * 应用类型
	 */
	@ColumnWidth(20)
	@ExcelProperty("应用类型")
	private String applyType;
	/**
	 * 应用数量
	 */
	@ColumnWidth(20)
	@ExcelProperty("应用数量")
	private Integer applyQty;
	/**
	 * 应用容量
	 */
	@ColumnWidth(20)
	@ExcelProperty("应用容量")
	private String applyCapacity;
	/**
	 * 应用图片key
	 */
	@ColumnWidth(20)
	@ExcelProperty("应用图片key")
	private Long applyImgBizKey;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}
