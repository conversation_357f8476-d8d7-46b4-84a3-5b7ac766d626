/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.toolsmachinery.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.system.entity.AttachmentInfoEntity;

import java.util.Date;
import java.util.List;

/**
 * ehs相关工具设备 实体类
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Data
@TableName("ehs_tools_machinery")
@ApiModel(value = "ToolsMachinery对象", description = "ehs相关工具设备")
@EqualsAndHashCode(callSuper = true)
public class ToolsMachineryEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 工具设备类型（业务字典agent_ehs_tools_machinery_type）
	 */
	@ApiModelProperty(value = "工具设备类型（业务字典agent_ehs_tools_machinery_type）")
	private String toolsMachineryType;
	/**
	 * 其他工具设备名称
	 */
	@ApiModelProperty(value = "其他工具设备名称")
	private String otherMachineryName;
	/**
	 * 工具设备图片key
	 */
	@ApiModelProperty(value = "工具设备图片key")
	private Long toolsMachineryImgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@ApiModelProperty(value = "ehs最新版本时间")
	private String ehsVersion;


	/**
	 * 工具设备类型名称
	 */
	@TableField(exist = false)
	private String toolsMachineryTypeName;

	//ehs相关工具图片信息
	@TableField(exist = false)
	private List<AttachmentInfoEntity> ehsToolsAttachmentList;


	//ehs相关工具描述信息
	@TableField(exist = false)
	private String ehsToolsDesc;
}
