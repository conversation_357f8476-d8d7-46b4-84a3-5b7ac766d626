/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.houseelectricalapply.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 踏勘房屋电气应用设备 实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("survey_house_electrical_apply")
@ApiModel(value = "HouseElectricalApply对象", description = "踏勘房屋电气应用设备")
@EqualsAndHashCode(callSuper = true)
public class HouseElectricalApplyEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 应用类型
	 */
	@ApiModelProperty(value = "应用类型")
	private String applyType;

	/**
	 *
	 * 其它应用类型【非数据字典里面的】
	 *
	 */
	@ApiModelProperty(value = "其它应用类型")
	private String otherApplyType;

	/**
	 * 应用数量
	 */
	@ApiModelProperty(value = "应用数量")
	private String applyQty;
	/**
	 * 应用容量
	 */
	@ApiModelProperty(value = "应用容量")
	private String applyCapacity;
	/**
	 * 应用图片key
	 */
	@ApiModelProperty(value = "应用图片key")
	private Long applyImgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}
