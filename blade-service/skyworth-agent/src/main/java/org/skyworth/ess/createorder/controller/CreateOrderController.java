/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-order/createOrder")
@Api(value = "创建订单节点", tags = "创建订单接口")
public class CreateOrderController extends BladeController {

	private final IOrderService orderService;


	/**
	 * 新增订单
	 */
	@PostMapping("/insertOrder")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "新增订单", notes = "传入order")
	public R<?> insertOrder(@Valid @RequestBody OrderEntity order) {
		return orderService.insertOrder(order);
	}


	/**
	 * 查询代理商公司信息
	 */
	@PostMapping("/selectAgencyCompany")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "查询代理商公司信息", notes = "传入orderDTO")
	public R<List<AgentCompanyInfoEntity>> selectAgencyCompany(@RequestBody OrderDTO orderDTO) {
		return orderService.selectAgencyCompany(orderDTO);
	}


	/**
	 * 查询代理商公司下交付经理信息
	 */
	@PostMapping("/selectDeliveryManager")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "查询代理商公司下交付经理信息", notes = "传入orderDTO")
	public R<?> selectDeliveryManager(@RequestBody OrderDTO orderDTO) {
		return orderService.selectDeliveryManager(orderDTO);
	}


	/**
	 * 查询订单公共信息
	 */
	@PostMapping("/selectBaseOrderInfo")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "查询订单公共信息", notes = "传入order")
	public R<?> selectBaseOrderInfo(@RequestBody OrderEntity order) {
		return orderService.selectBaseOrderInfo(order);
	}


	/**
	 * 查询订单基本信息
	 */
	@PostMapping("/selectBasicOrderInfo")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "查询基本订单信息", notes = "传入order")
	public R<?> selectBasicOrderInfo(@RequestBody OrderEntity order) {
		return orderService.selectBasicOrderInfo(order);
	}


}
