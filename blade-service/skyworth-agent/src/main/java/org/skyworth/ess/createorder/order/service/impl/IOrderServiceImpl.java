/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.order.service.impl;


import com.alibaba.nacos.common.utils.MapUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.company.service.IAgentCompanyInfoService;
import org.skyworth.ess.constant.OrderStatusConstants;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.excel.OrderExcel;
import org.skyworth.ess.createorder.order.mapper.OrderMapper;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.createorder.order.vo.OrderVO;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.utils.WordToPdfUtil;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.common.mail.SendMail;
import org.springblade.common.utils.CommonUtil;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.flow.core.feign.IFlowClient;
import org.springblade.flow.core.vo.OrderFlowVO;
import org.springblade.system.entity.*;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.springblade.common.constant.CommonConstant.INTERIOR_TYPE;

/**
 * 订单表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class IOrderServiceImpl extends BaseServiceImpl<OrderMapper, OrderEntity> implements IOrderService {

	private final IFlowClient flowClient;

	private final IDictBizClient dictBizClient;

	private final IOrderWorkFlowService iOrderWorkFlowService;

	private final IAgentCompanyInfoService iAgentCompanyInfoService;

	private final IAttachmentInfoClient attachmentInfoService;

	private final IUserSearchClient userSearchClient;

	private final IAgentCompanyInfoService companyInfoService;

	private final IAdditionalInfoService iAdditionalInfoService;

	private final ISysClient iSysClient;

	private final SendMail sendMail;


	//代理商编码
	private static final String BUSINESS_MANAGER_ROLE_CODE = "018";


	@Override
	public IPage<OrderVO> selectOrderPage(IPage<OrderVO> page, OrderVO order) {
		BladeUser user = AuthUtil.getUser();
		if (user.getDetail() != null) {
			Boolean roleInnerFlag = (Boolean) user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
			// 如果包含创维内部角色，则可查看所有订单
			if (roleInnerFlag != null && roleInnerFlag) {
				order.setRoleType(CommonConstant.ROLE_TYPE_INNER);
			} else {
				order.setRoleType(CommonConstant.ROLE_TYPE_OUT);
				// 如果为外部角色，
				// 代理商，1、根据登录人对应的部门（代理商部门），对应订单表中的创建部门（代理商部门）； 2、登录人还有可能是安装商。  取2者交集
				// 安装商，无部门，判断登录人是否在 订单人员关系中，如果存在，则能查看相应订单
				if (StringUtil.isNotBlank(user.getDeptId()) && !CommonConstant.DEFAULT_VALUE_MINUS_ONE.equals(user.getDeptId())) {
					// 代理商
					order.setUserDeptId(user.getDeptId());
					order.setInstallUserId(user.getUserId());
					order.setAgentOrInstallType("agent");
				} else {
					// 安装商
					order.setInstallUserId(user.getUserId());
					order.setAgentOrInstallType("install");
				}
			}
			List<OrderVO> orderVOS = baseMapper.selectOrderPage(page, order);
			if (CollectionUtil.isNotEmpty(orderVOS)) {
				// 建站地址
				this.setSiteDetailAddress(orderVOS);
				String currentLanguage = CommonUtil.getCurrentLanguage();
				List<DictBiz> bizList = dictBizClient.getListByLang(DictBizCodeEnum.WF_SCHEDULE.getDictCode(), currentLanguage).getData().stream().filter(dictBiz -> CommonConstant.AGENT_TENANT_ID.equals(dictBiz.getTenantId())).collect(Collectors.toList());
				Map<String, String> bizMap = bizList.stream().collect(HashMap::new, (map, dictBiz) -> map.put(dictBiz.getDictKey(), dictBiz.getDictValue()), HashMap::putAll);
				orderVOS.forEach(p -> {
					p.setWfCurrentStatusName(bizMap.get(p.getWfCurrentStatus()));
				});
				// 业务经理和订单创建人
				this.setUserName(orderVOS);
			}
			return page.setRecords(orderVOS);
		}
		return page.setRecords(new ArrayList<>());
	}

	private void setUserName(List<OrderVO> orderVoList) {
		List<Long> userIdList = orderVoList.stream().map(OrderVO::getOrderCreateUser).distinct().collect(Collectors.toList());
		List<Long> rolloutManagerIdList = orderVoList.stream().filter(p -> p.getOrderEntity().getRolloutManagerId() != null).map(p -> p.getOrderEntity().getRolloutManagerId()).distinct().collect(Collectors.toList());
		userIdList.addAll(rolloutManagerIdList);
		R<List<User>> userResult = userSearchClient.listByUserIds(userIdList);
		if (userResult.getCode() != CommonConstant.REST_FUL_RESULT_SUCCESS || CollectionUtil.isEmpty(userResult.getData())) {
			return;
		}
		Map<Long, User> userMap = userResult.getData().stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
		orderVoList.forEach(p -> {
			if (userMap.containsKey(p.getOrderCreateUser())) {
				User orderUser = userMap.get(p.getOrderCreateUser());
				p.setOrderCreateUserName(orderUser.getRealName());
				p.setOrderCreateUserPhone(orderUser.getPhone());
			}
			if (userMap.containsKey(p.getOrderEntity().getRolloutManagerId())) {
				User orderUser = userMap.get(p.getOrderEntity().getRolloutManagerId());
				p.getOrderEntity().setRolloutManagerName(orderUser.getRealName());
				p.getOrderEntity().setRolloutManagerPhone(orderUser.getPhone());
			}
		});
	}


	/**
	 * @Description: 查询代理商公司
	 * @Param: [orderDTO]
	 * @Return: org.springblade.core.tool.api.R
	 * @Author: baixu
	 * @Date: 2023/11/27 13:37
	 **/
	@Override
	public R<List<AgentCompanyInfoEntity>> selectAgencyCompany(OrderDTO orderDTO) {
		List<AgentCompanyInfoEntity> infoEntityList = new LinkedList<>();
		Boolean inOutIdentification = getInOutIdentification();
		//如果包含创维内部角色，则可查看所有代理商公司
		LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = getQueryWrapper(orderDTO);
		List<AgentCompanyInfoEntity> allAgentCompanyInfoList = iAgentCompanyInfoService.list(queryWrapper);
		if (Boolean.TRUE.equals(inOutIdentification)) {
			//内部标识返回全部代理商公司
			infoEntityList = allAgentCompanyInfoList;
		} else if (Boolean.FALSE.equals(inOutIdentification)) {
			//如果参数有部门id，就根据部门id查询
			if (CollectionUtils.isNotEmpty(orderDTO.getDeptIdList())) {
				//根据部门id查询是否有匹配的代理商公司
				queryWrapper.in(AgentCompanyInfoEntity::getDeptId, orderDTO.getDeptIdList());
				infoEntityList = iAgentCompanyInfoService.list(queryWrapper);
			} else {
				return R.data(infoEntityList);
			}
		}
		return R.data(infoEntityList);
	}


	/**
	 * @Description: 代理商下交付经理用户信息
	 * @Param: [orderDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/11/27 15:13
	 **/
	@Override
	public R<?> selectDeliveryManager(OrderDTO orderDTO) {
		if (ObjectUtils.isEmpty(orderDTO)) {
			throw new BusinessException("agent.createOrder.selectDeliveryManager.orderDTO.notNull");
		}
		//代理商id
		if (ObjectUtils.isEmpty(orderDTO.getAgentId())) {
			throw new BusinessException("agent.createOrder.selectDeliveryManager.agentId.notNull");
		}
		List<User> resultUserList = new LinkedList<>();
		//根据代理商id查询代理商部门id
		AgentCompanyInfoEntity agentCompanyInfoEntity = iAgentCompanyInfoService.getById(orderDTO.getAgentId());
		if (ObjectUtils.isEmpty(agentCompanyInfoEntity)) {
			return R.data(resultUserList);
		}
		//获取代理商部门id
		Long deptId = agentCompanyInfoEntity.getDeptId();
		if (ObjectUtils.isEmpty(deptId)) {
			return R.data(resultUserList);
		}
		//根据角色编码查询角色id
		R<Role> roleInfoByCodeResult = iSysClient.getRoleInfoByCode(BUSINESS_MANAGER_ROLE_CODE);
		if (!roleInfoByCodeResult.isSuccess() || ObjectUtils.isEmpty(roleInfoByCodeResult.getData())) {
			return R.data(resultUserList);
		}
		//根据角色id查询该角色下用户列表
		R<List<User>> userListByRoleId = userSearchClient.listByRole(String.valueOf(roleInfoByCodeResult.getData().getId()));
		// 判断查询结果是否为空
		if (ObjectUtils.isEmpty(userListByRoleId) || ObjectUtils.isEmpty(userListByRoleId.getData())) {
			return R.data(resultUserList);
		}
		//根据部门id筛选
		resultUserList = userListByRoleId.getData().stream().filter(user -> StringUtil.isNotBlank(user.getDeptId()) && user.getDeptId().contains(String.valueOf(deptId))).collect(Collectors.toList());
		if (StringUtil.isNotBlank(orderDTO.getDeliveryManagerName()) && CollectionUtils.isNotEmpty(resultUserList)) {
			//根据交付经理名称筛选
			resultUserList = resultUserList.stream().filter(user -> user.getRealName().toLowerCase().contains(orderDTO.getDeliveryManagerName().toLowerCase())).collect(Collectors.toList());
		}
		return R.data(resultUserList);
	}


	/**
	 * @Description: 新增订单
	 * @Param: [order]
	 * @Return: boolean
	 * @Author: baixu
	 * @Date: 2023/11/24 14:14
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Boolean> insertOrder(OrderEntity order) {
		//获取订单编号
		R<String> getOrderNumberResult = iSysClient.getRedisUniqueId(RedisSeqEnums.ORDER_NO_SEQ);
		if (!getOrderNumberResult.isSuccess() || ObjectUtils.isEmpty(getOrderNumberResult.getData())) {
			throw new BusinessException("agent.createOrder.insertOrder.getOrderNumber.notNull");
		}
		//获取用户角色内外部标识
		LambdaQueryWrapper<AgentCompanyInfoEntity> agentCompanyInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		agentCompanyInfoEntityLambdaQueryWrapper.eq(AgentCompanyInfoEntity::getId, order.getDistributorId());
		agentCompanyInfoEntityLambdaQueryWrapper.select(AgentCompanyInfoEntity::getCompanyAttributes, AgentCompanyInfoEntity::getDeptId);
		AgentCompanyInfoEntity agentCompanyInfoEntity = iAgentCompanyInfoService.getOne(agentCompanyInfoEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(agentCompanyInfoEntity) || ObjectUtils.isEmpty(agentCompanyInfoEntity.getCompanyAttributes())) {
			throw new BusinessException("agent.createOrder.insertOrder.getCompanyAttributes.fail");
		}
		//通过内外部标识拼接订单编号前缀
		String prefixFlag = INTERIOR_TYPE.equals(agentCompanyInfoEntity.getCompanyAttributes()) ? "i" : "e";
		String orderNumber = prefixFlag + getOrderNumberResult.getData();
		order.setOrderNumber(orderNumber);
		//添加代理商部门
		order.setBelongDeptId(agentCompanyInfoEntity.getDeptId());
		//保存和取消后重新提交
		//补充操作信息
		boolean saveFlag = this.save(order);
		if (!saveFlag) {
			throw new BusinessException("agent.createOrder.insertOrder.saveOrderFail");
		}
		//开启审批流程
		R<OrderFlowVO> orderProcess = flowClient.startOrderProcess(String.valueOf(order.getId()));
		if (!orderProcess.isSuccess() || ObjectUtils.isEmpty(orderProcess.getData())) {
			throw new BusinessException("agent.createOrder.insertOrder.startProcessFail");
		}
		OrderFlowVO orderFlowVO = orderProcess.getData();
		//保存审批流相关信息
		OrderWorkFlowEntity orderWorkFlowEntity = getOrderWorkFlowEntity(orderFlowVO);
		orderWorkFlowEntity.setOrderId(order.getId());
		boolean workFlowSaveFlag = iOrderWorkFlowService.save(orderWorkFlowEntity);
		if (!workFlowSaveFlag) {
			throw new BusinessException("agent.createOrder.insertOrder.workFlowSaveFail");
		}

		//发送邮件通知下一个节点处理任务
		R<List<DictBiz>> flowMailRes = dictBizClient.getList("epc_flow_mail");
		List<DictBiz> flowMailList = flowMailRes.getData();
		StringBuilder text = new StringBuilder();
		if(CollectionUtils.isNotEmpty(flowMailList)){
			DictBiz dictBiz = flowMailList.get(0);
			text.append(dictBiz.getDictValue()).append("\n\n")
				.append(dictBiz.getAttribute1().replace("${orderNum}",orderNumber))
				.append("\n\n").append(dictBiz.getAttribute2())
				.append("\n\n").append(dictBiz.getAttribute3());

			//构建接收人地址
			List<DictBiz> dictBizList=dictBizClient.getDataByCodeAndKey("wf_schedule","llInfoConfirmation").getData();
			if(CollectionUtil.isNotEmpty(dictBizList)){
				DictBiz dictBizWf=dictBizList.get(0);
				String toMail=dictBizWf.getAttribute1();
				if(ValidationUtil.isNotEmpty(toMail)){
					Resource resource = new ClassPathResource("QRcode/QRcode.png");
                    try {
                        InputStream in = resource.getInputStream();
                        File file = new File("/opt/data/Qr/QRcode.png");
                        FileUtils.copyToFile(in, file);
                        if (ValidationUtil.isNotEmpty(file)) {
                            List<File> fileList = new ArrayList<>();
                            fileList.add(file);
                            boolean flag = sendMail.sendMailMultiParams(fileList, toMail, null, null, "Skyworth Push Email Notification", text.toString());
                            log.info("send mail examineApprove flag : {}", flag);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
			}
		}
		return R.status(true);
	}

	/**
	 * @Description: 查询订单公共信息
	 * @Param: [order]
	 * @Return: org.springblade.core.tool.api.R
	 * @Author: baixu
	 * @Date: 2023/11/24 15:59
	 **/
	@Override
	public R<?> selectBaseOrderInfo(OrderEntity order) {
		//订单参数校验
		orderParamValid(order);
		OrderVO orderVO = new OrderVO();
		//根据订单id查询订单公共信息
		LambdaQueryWrapper<OrderEntity> orderEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderEntityLambdaQueryWrapper.eq(OrderEntity::getId, order.getId());
		orderEntityLambdaQueryWrapper.select(OrderEntity::getId,OrderEntity::getDistanceRestriction, OrderEntity::getOrderNumber, OrderEntity::getUpdateTime, OrderEntity::getCreateTime, OrderEntity::getDistributorId, OrderEntity::getRolloutManagerId);
		OrderEntity orderEntity = this.getOne(orderEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(orderEntity)) {
			return R.data(orderVO);
		}
		//代理商公司字典转换
		LambdaQueryWrapper<AgentCompanyInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(AgentCompanyInfoEntity::getId, orderEntity.getDistributorId());
		AgentCompanyInfoEntity agentCompany = companyInfoService.getOne(queryWrapper);
		if (ObjectUtils.isNotEmpty(agentCompany)) {
			orderEntity.setDistributorName(agentCompany.getCompanyName());
		}
		//交付经理名称字典转换
		R<List<User>> userListResultInfo = userSearchClient.listByUser(String.valueOf(orderEntity.getRolloutManagerId()));
		if (ObjectUtils.isNotEmpty(userListResultInfo) && CollectionUtils.isNotEmpty(userListResultInfo.getData())) {
			//用户id是唯一的 这里取第一个
			orderEntity.setRolloutManagerName(userListResultInfo.getData().get(0).getRealName());
		}
		orderVO.setOrderEntity(orderEntity);
		//查询订单进度
		LambdaQueryWrapper<OrderWorkFlowEntity> workFlowEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		workFlowEntityLambdaQueryWrapper.eq(OrderWorkFlowEntity::getOrderId, orderEntity.getId());
		OrderWorkFlowEntity orderWorkFlowEntity = iOrderWorkFlowService.getOne(workFlowEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(orderWorkFlowEntity)) {
			return R.data(orderVO);
		}
		//订单进展
		orderVO.setWfCurrentStatus(orderWorkFlowEntity.getWfCurrentStatus());
		//流程实例id
		orderVO.setWfInstanceId(orderWorkFlowEntity.getWfInstanceId());
		return R.data(orderVO);
	}


	/**
	 * @Description: 查询订单基本信息
	 * @Param: [order]
	 * @Return: org.springblade.core.tool.api.R
	 * @Author: baixu
	 * @Date: 2023/11/24 17:20
	 **/
	@Override
	public R<?> selectBasicOrderInfo(OrderEntity order) {
		//订单参数校验
		orderParamValid(order);
		//查询订单信息
		OrderVO orderVO = new OrderVO();
		OrderEntity orderEntity = this.getById(order.getId());
		if (ObjectUtils.isEmpty(orderEntity)) {
			return R.data(orderVO);
		}
		//查询字典省市区字典并赋值字典名称
		orderEntity = findRegionInfoByCode(orderEntity);
		//查询项目类型字典
		Map<String, String> projectTypeDictMap = getDictBizListByName(DictBizCodeEnum.AGENT_PROJECT_TYPE.getDictCode());
		if (MapUtil.isNotEmpty(projectTypeDictMap) && ObjectUtils.isNotEmpty(projectTypeDictMap.get(orderEntity.getProjectType()))) {
			orderEntity.setProjectTypeName(projectTypeDictMap.get(orderEntity.getProjectType()));
		}
		//根据附件key查询附件信息
		if (ObjectUtils.isNotEmpty(orderEntity.getAdditionalDocBizKey())) {
			List<Long> businessIdList = Collections.singletonList(orderEntity.getAdditionalDocBizKey());
			SkyWorthFileEntity skyWorthFileEntity = getFileAttachmentInfo(businessIdList);
			if (ObjectUtils.isNotEmpty(skyWorthFileEntity)) {
				//附件信息
				orderVO.setAttachmentMap(skyWorthFileEntity.getAttachmentMap());
				//附件描述
				orderVO.setImgDescViewMap(skyWorthFileEntity.getImgDescViewMap());
			}
		}
		orderVO.setOrderEntity(orderEntity);
		return R.data(orderVO);
	}


	/**
	 * @Description: 查询字典省市区字典并赋值字典名称
	 * @Param: [orderEntity]
	 * @Return: org.skyworth.ess.createorder.order.entity.OrderEntity
	 * @Author: baixu
	 * @Date: 2023/12/7 14:30
	 **/
	@Override
	public OrderEntity findRegionInfoByCode(OrderEntity orderEntity) {
		//查询字典省市区字典
		List<String> regionIdList = Arrays.asList(orderEntity.getSiteCountryCode(), orderEntity.getSiteProvinceCode(), orderEntity.getSiteCityCode());
		R<List<Region>> regionListResult = iSysClient.getRegionList(regionIdList);
		if (ObjectUtils.isNotEmpty(regionListResult) && CollectionUtils.isNotEmpty(regionListResult.getData())) {
			//转换地域字典信息
			setRegionNameByCode(regionListResult, orderEntity);
		}
		return orderEntity;
	}


	/**
	 * @Description: 转换地域字典信息
	 * @Param: [regionListResult, orderEntity]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/11/30 14:52
	 **/
	private void setRegionNameByCode(R<List<Region>> regionListResult, OrderEntity orderEntity) {
		List<Region> regionList = regionListResult.getData();
		//转换国家字典
		Map<String, String> regionMap = regionList.stream().collect(Collectors.toMap(Region::getCode, Region::getName));
		if (ObjectUtils.isNotEmpty(regionMap.get(orderEntity.getSiteCountryCode()))) {
			orderEntity.setSiteCountryName(regionMap.get(orderEntity.getSiteCountryCode()));
		}
		//转换省字典
		if (ObjectUtils.isNotEmpty(regionMap.get(orderEntity.getSiteProvinceCode()))) {
			orderEntity.setSiteProvinceName(regionMap.get(orderEntity.getSiteProvinceCode()));
		}
		//转换城市字典
		if (ObjectUtils.isNotEmpty(regionMap.get(orderEntity.getSiteCityCode()))) {
			orderEntity.setSiteCityName(regionMap.get(orderEntity.getSiteCityCode()));
		}
	}


	/**
	 * @Description: 根据字典类别名称获取字典
	 * @Param: [dictName]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/11/27 17:18
	 **/
	@Override
	public Map<String, String> getDictBizListByName(String type) {
		R<List<DictBiz>> deviceBatteryMatch = dictBizClient.getListByLang(type, CommonUtil.getCurrentLanguage());
		if (ObjectUtils.isEmpty(deviceBatteryMatch) || CollectionUtils.isEmpty(deviceBatteryMatch.getData())) {
			return new HashMap<>();
		}
		return deviceBatteryMatch.getData().stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (v1, v2) -> v2));
	}

	/**
	 * @Description: 转换工作流信息
	 * @Param: [orderFlowVO]
	 * @Return: org.skyworth.ess.orderworkflow.entity.OrderWorkFlowEntity
	 * @Author: baixu
	 * @Date: 2023/11/24 15:38
	 **/
	private OrderWorkFlowEntity getOrderWorkFlowEntity(OrderFlowVO orderFlowVO) {
		OrderWorkFlowEntity orderWorkFlowEntity = new OrderWorkFlowEntity();
		//流程实例id
		orderWorkFlowEntity.setWfInstanceId(orderFlowVO.getProcessInstanceId());
		//任务id
		orderWorkFlowEntity.setTaskId(orderFlowVO.getTaskId());
		//工作流当前状态/订单进度
		orderWorkFlowEntity.setWfCurrentStatus(orderFlowVO.getTaskName());
		//当前处理节点类型
		orderWorkFlowEntity.setWfCurrentType(OrderStatusConstants.CURRENT_ROLE);
		//当前处理节点角色或人
		orderWorkFlowEntity.setWfCurrentRole(orderFlowVO.getAssignee());
		//当前处理节点角色或人名称
		orderWorkFlowEntity.setWfCurrentRoleName(orderFlowVO.getAssigneeName());
		//上一步审批状态
		orderWorkFlowEntity.setAuditStatus(OrderStatusConstants.AUDIT_STATUS_PASS);
		return orderWorkFlowEntity;
	}


	/**
	 * @Description: 构建代理商公司查询条件
	 * @Param: [orderDTO]
	 * @Return: com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<org.skyworth.ess.company.entity.AgentCompanyInfoEntity>
	 * @Author: baixu
	 * @Date: 2023/12/1 18:14
	 **/
	private LambdaQueryWrapper<AgentCompanyInfoEntity> getQueryWrapper(OrderDTO orderDTO) {
		LambdaQueryWrapper<AgentCompanyInfoEntity> agentCompanyInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		//根据公司名称模糊查询
		if (StringUtil.isNotBlank(orderDTO.getCompanyName())) {
			agentCompanyInfoEntityLambdaQueryWrapper.likeRight(AgentCompanyInfoEntity::getCompanyName, orderDTO.getCompanyName());
		}
		return agentCompanyInfoEntityLambdaQueryWrapper;
	}


	/**
	 * @Description: 获取用户角色内外部标识
	 * @Param: []
	 * @Return: java.lang.Boolean
	 * @Author: baixu
	 * @Date: 2023/12/19 10:58
	 **/
	private Boolean getInOutIdentification() {
		BladeUser user = AuthUtil.getUser();
		if (ObjectUtils.isNotEmpty(user.getDetail()) && ObjectUtils.isNotEmpty(user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG))) {
			return (Boolean) user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
		}
		return null;
	}


	/**
	 * @Description: 订单参数校验
	 * @Param: [order]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/20 16:21
	 **/
	private void orderParamValid(OrderEntity order) {
		if (ObjectUtils.isEmpty(order)) {
			throw new BusinessException("agent.createOrder.selectDeliveryManager.orderDTO.notNull");
		}
		//订单id
		if (ObjectUtils.isEmpty(order.getId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
	}


	/**
	 * @Description: 获取附件信息和相关描述信息
	 * @Param: [orderEntity, orderVO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/11/30 18:44
	 **/
	@Override
	public SkyWorthFileEntity getFileAttachmentInfo(List<Long> businessIdList) {
		SkyWorthFileEntity skyWorthFileEntity = new SkyWorthFileEntity();
		//根据businessId查询附件信息
		R<Map<Long, List<AttachmentInfoEntity>>> byBusinessIdsResult = attachmentInfoService.findByBusinessIds(businessIdList);
		if (ObjectUtils.isNotEmpty(byBusinessIdsResult) && ObjectUtils.isNotEmpty(byBusinessIdsResult.getData())) {
			skyWorthFileEntity.setAttachmentMap(byBusinessIdsResult.getData());
			//根据businessId查询附件描述信息
			Map<Long, String> additionalMap = iAdditionalInfoService.selectAdditionalMapByBusinessIds(businessIdList);
			if (ObjectUtils.isNotEmpty(additionalMap)) {
				skyWorthFileEntity.setImgDescViewMap(additionalMap);
			}
		}
		return skyWorthFileEntity;
	}


	@Override
	public List<OrderExcel> exportOrder(Wrapper<OrderEntity> queryWrapper) {
		return baseMapper.exportOrder(queryWrapper);
	}

	// 设置订单详细地址
	private void setSiteDetailAddress(List<OrderVO> orderVOList) {
		List<String> regionCodeList = new ArrayList<>();
		for (OrderVO orderVO : orderVOList) {
			regionCodeList.add(orderVO.getOrderEntity().getSiteCountryCode());
			regionCodeList.add(orderVO.getOrderEntity().getSiteProvinceCode());
			regionCodeList.add(orderVO.getOrderEntity().getSiteCityCode());
		}
		List<String> regionCodeNotNullList = regionCodeList.stream().filter(StringUtil::isNotBlank).collect(Collectors.toList());
		if (CollectionUtil.isEmpty(regionCodeNotNullList)) {
			return;
		}
		R<List<Region>> regionResult = iSysClient.getRegionList(regionCodeNotNullList);
		List<Region> regionList = regionResult.getData();
		if (CollectionUtil.isEmpty(regionList)) {
			return;
		}
		for (OrderVO order : orderVOList) {
			StringBuilder address = new StringBuilder();
			OrderEntity orderEntity = order.getOrderEntity();
			for (Region region : regionList) {
				if (region.getCode().equalsIgnoreCase(orderEntity.getSiteCountryCode())) {
					address.append(region.getName()).append(" ");
					orderEntity.setSiteCountryName(region.getName());
				}
				if (region.getCode().equalsIgnoreCase(orderEntity.getSiteProvinceCode())) {
					address.append(region.getName()).append(" ");
					orderEntity.setSiteProvinceName(region.getName());
				}
				if (region.getCode().equalsIgnoreCase(orderEntity.getSiteCityCode())) {
					address.append(region.getName()).append(" ");
					orderEntity.setSiteCityName(region.getName());
				}
			}
			orderEntity.setSiteDetailAddress(address.append(" ").append(orderEntity.getSiteAddress() == null ? "" : orderEntity.getSiteAddress()).toString());
		}


	}


}
