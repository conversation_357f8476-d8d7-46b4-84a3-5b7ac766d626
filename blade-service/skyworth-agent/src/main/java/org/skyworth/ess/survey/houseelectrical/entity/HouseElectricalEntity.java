/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.houseelectrical.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.util.Date;

/**
 * 踏勘房屋电气信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("survey_house_electrical")
@ApiModel(value = "HouseElectrical对象", description = "踏勘房屋电气信息")
@EqualsAndHashCode(callSuper = true)
public class HouseElectricalEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 电力基础设施
	 */
	@ApiModelProperty(value = "电力基础设施")
	private String infrastructure;
	/**
	 * 电力基础设施连接类型
	 */
	@ApiModelProperty(value = "电力基础设施连接类型")
	private String infrastructureConnectionType;
	/**
	 * 其它电力基础设施连接类型
	 */
	@ApiModelProperty(value = "其它电力基础设施连接类型")
	private String infrastructureConnectionTypeOther;
	/**
	 * 电力基础设施图片key
	 */
	@ApiModelProperty(value = "电力基础设施图片key")
	private Long infrastructureImgBizKey;
	/**
	 * 备用发电机
	 */
	@ApiModelProperty(value = "备用发电机")
	private String backupGenerator;
	/**
	 * 备用发电机数量
	 */
	@ApiModelProperty(value = "备用发电机数量")
	private String backupGeneratorQty;

	/**
	 * 备用发电机容量
	 */
	@ApiModelProperty(value = "备用发电机容量")
	private String backupGeneratorTotalCapacity;

	/**
	 * 备用发电机图片key
	 */
	@ApiModelProperty(value = "备用发电机图片key")
	private Long backupGeneratorImgBizKey;
	/**
	 * 备用电源
	 */
	@ApiModelProperty(value = "备用电源")
	private String backupPowerSource;

	/**
	 * 备用电源数量
	 */
	@ApiModelProperty(value = "备用电源数量")
	private String backupPowerSourceQty;

	/**
	 * 备用电源容量
	 */
	@ApiModelProperty(value = "备用电源容量")
	private String backupPowerSourceTotalCapacity;


	/**
	 * 备用电源图片key
	 */
	@ApiModelProperty(value = "备用电源图片key")
	private Long backupPowerSourceImgBizKey;
	/**
	 * 电力条件
	 */
	@ApiModelProperty(value = "电力条件")
	private String electricalCondition;

	/**
	 * 电力条件描述
	 */
	@ApiModelProperty(value = "电力条件描述")
	private String electricalConditionRemark;

	/**
	 * 电力条件图片key
	 */
	@ApiModelProperty(value = "电力条件图片key")
	private Long electricalConditionImgBizKey;
	/**
	 * 电线确认
	 */
	@ApiModelProperty(value = "电线确认")
	private String routingConfirmation;

	/**
	 * 电线确认描述
	 */
	@ApiModelProperty(value = "电线确认描述")
	private String routingConfirmationRemark;

	/**
	 * 电线确认图片key
	 */
	@ApiModelProperty(value = "电线确认图片key")
	private Long routingConfirmationImgBizKey;
	/**
	 * 电线草案
	 */
	@ApiModelProperty(value = "电线草案")
	private String routingDraft;
	/**
	 * 电线草案图片key
	 */
	@ApiModelProperty(value = "电线草案图片key")
	private Long routingDraftImgBizKey;
	/**
	 * 检查布线
	 */
	@ApiModelProperty(value = "检查布线")
	private String inspectWiring;
	/**
	 * 检查布线图片key
	 */
	@ApiModelProperty(value = "检查布线图片key")
	private Long inspectWiringImgBizKey;
	/**
	 * 指定升级
	 */
	@ApiModelProperty(value = "指定升级")
	private String upgradeRequired;
	/**
	 * 指定升级图片key
	 */
	@ApiModelProperty(value = "指定升级图片key")
	private Long upgradeRequiredImgBizKey;

	/**
	 * 拟定逆变器安装位置
	 */
	@ApiModelProperty(value = "拟定逆变器安装位置")
	private String proposedInverterInstallationPosition;

	/**
	 * 拟定逆变器安装位置图片key
	 */
	@ApiModelProperty(value = "拟定逆变器安装位置图片key")
	private Long proposedInverterInstallationPositionImgBizKey;

	/**
	 * 月功率
	 */
	@ApiModelProperty(value = "月功率")
	private String  powerMonthly;

	/**
	 * 月功率图片key
	 */
	@ApiModelProperty(value = "月功率图片key")
	private Long powerMonthlyImgBizKey;

	/**
	 * 年功率
	 */
	@ApiModelProperty(value = "年功率")
	private String powerAnnual;

	/**
	 * 年功率图片key
	 */
	@ApiModelProperty(value = "年功率图片key")
	private Long powerAnnualImgBizKey;

	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@TableField(exist = false)
	private Long electricianOwnerImgBizKey;

	@TableField(exist = false)
	private Long electricianOwnerPhotoImgBizKey;

	@TableField(exist = false)
	private Date surveySubmitDate;

}
