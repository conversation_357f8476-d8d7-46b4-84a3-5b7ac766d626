package org.skyworth.ess.survey.constant;

/**
 * 操作状态枚举类
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public enum OperationStatus {
	FINISH(1, "finish"),
	UNFINISH(2, "unfinish");

	private Integer type;
	private String dec;

	OperationStatus(Integer type, String dec) {
		this.type = type;
		this.dec = dec;
	}

	public static String getDecByType(Integer type) {
		for (OperationStatus houseModuleType : OperationStatus.values()) {
			if (houseModuleType.getType().equals(type)) {
				return houseModuleType.getDec();
			}
		}
		return null;
	}


	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDec() {
		return dec;
	}

	public void setDec(String dec) {
		this.dec = dec;
	}
}
