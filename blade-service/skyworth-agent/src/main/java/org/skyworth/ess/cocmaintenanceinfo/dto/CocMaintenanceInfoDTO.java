/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.springblade.system.entity.AttachmentInfoEntity;

import java.util.List;

/**
 * coc和维护信息 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CocMaintenanceInfoDTO extends CocMaintenanceInfoEntity {
	private static final long serialVersionUID = 1L;

	private String bizType;

	/**
	 * temporaryCoc
	 * */
	private List<AttachmentInfoEntity> cocImg;

	/**
	 * fac
	 * */
	private List<AttachmentInfoEntity> facLandlordSignImg;

	private List<AttachmentInfoEntity> facBizKeyImg;

	private List<AttachmentInfoEntity> technicianSignImg;

	private List<AttachmentInfoEntity> technicianBizKeyImg;

	/**
	 * Balance-Payment
	 * */

	private List<AttachmentInfoEntity> docFile;

	/**
	 * finalCoc
	 * */
	private List<AttachmentInfoEntity> finalCocImg;


	/**
	 * QC-verify
	 * */
	private List<OrderNodeSubStatusEntity> content;

	private String type;

	private String remark;


	/**
	 * 客户确认文件
	 */
	private List<AttachmentInfoEntity> clientConfirmationImg;

	/**
	 * 客户验收文件
	 */
	private List<AttachmentInfoEntity> customersAcceptanceFile;




}
