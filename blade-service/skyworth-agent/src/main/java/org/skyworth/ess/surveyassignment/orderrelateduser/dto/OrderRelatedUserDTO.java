/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.surveyassignment.orderrelateduser.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.survey.info.entity.SurveyInfoEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.springblade.flow.core.dto.OrderFlowDTO;

import javax.validation.Valid;

/**
 * 订单关系人 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderRelatedUserDTO extends OrderRelatedUserEntity {
	private static final long serialVersionUID = 1L;

	//订单id
	private Long orderId;

	//探勘信息
	@Valid
	private SurveyInfoEntity infoEntity;


	//施工人员id
	private Long installUserId;

	//电气工程师id
	private Long electricUserId;


	//施工人员名称
	private String installUserName;

	//电气工程师名称
	private String electricUserName;


	//订单审批信息
	private OrderFlowDTO orderFlowDTO;


	//修改状态
	private String modifyStatus;


	//代理商id
	private Long distributorId;


}
