/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.electricalcomponents.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 施工-电器元件信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ElectricalComponentsExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private Long orderId;
	/**
	 * 电缆布线图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("电缆布线图片业务主键")
	private Long verifyCableWiringImgBizKey;
	/**
	 * 确认使用管道图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("确认使用管道图片业务主键")
	private Long confirmUseCondultImgBizKey;
	/**
	 * 密封接头和开口图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("密封接头和开口图片业务主键")
	private Long allSealedJointsImgBizKey;
	/**
	 * 电器箱图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("电器箱图片业务主键")
	private Long electricalBoxImgBizKey;
	/**
	 * 接线盒图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("接线盒图片业务主键")
	private Long junctionBoxInspectionImgBizKey;
	/**
	 * 检查发电机图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("检查发电机图片业务主键")
	private Long checkGeneratorImgBizKey;
	/**
	 * 检查电池系统图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("检查电池系统图片业务主键")
	private Long checkBatterySystemImgBizKey;
	/**
	 * 电表或CT图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("电表或CT图片业务主键")
	private Long currentMeterCtImgBizKey;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}
