/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.cocmaintenanceinfo.dto.BasicOrderDTO;
import org.skyworth.ess.cocmaintenanceinfo.dto.CocMaintenanceInfoDTO;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocFacFileVo;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocMaintenanceInfoVO;
import org.skyworth.ess.cocmaintenanceinfo.excel.CocMaintenanceInfoExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.springblade.core.mp.base.BaseService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * coc和维护信息 服务类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface ICocMaintenanceInfoService extends BaseService<CocMaintenanceInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param cocMaintenanceInfo
	 * @return
	 */
	IPage<CocMaintenanceInfoVO> selectCocMaintenanceInfoPage(IPage<CocMaintenanceInfoVO> page, CocMaintenanceInfoVO cocMaintenanceInfo);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<CocMaintenanceInfoExcel> exportCocMaintenanceInfo(Wrapper<CocMaintenanceInfoEntity> queryWrapper);

	/**
	 * qc审核
	 * @param cocMaintenanceInfoDTO
	 * @return
	 * */
	boolean  qcVerifyAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO);


	/**
	 * qc审核-详情
	 * @param orderId
	 * @return
	 * */
	List<OrderNodeSubStatusEntity> qcVerifyDetail(Long orderId);

	/**
	 * 临时COC-审核
	 * @param cocMaintenanceInfoDTO
	 * @return
	 * */
	boolean temporaryCocAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO);

	/**
	 * 临时COC-详情
	 * @param orderId
	 * @return
	 * */
	CocMaintenanceInfoDTO temporaryCocDetail(long orderId);

	/**
	 * fac-审核
	 * @param cocMaintenanceInfoDTO
	 * @return
	 * */
	boolean facAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO);


	/**
	 * fac-详情
	 * @param orderId
	 * @return
	 * */
	CocMaintenanceInfoDTO facDetail(long orderId);

	/**
	 * balancePay-审核
	 * @param cocMaintenanceInfoDTO
	 * @return
	 * */
	boolean balancePayAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO);

	/**
	 * balancePay-详情
	 * @param orderId
	 * @return
	 * */
	CocMaintenanceInfoDTO balancePayDetail(long orderId);


	/**
	 * finalCoc-审核
	 * @param cocMaintenanceInfoDTO
	 * @return
	 * */
	boolean finalCocAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO);

	/**
	 * finalCoc-详情
	 * @param orderId
	 * @return
	 * */
	CocMaintenanceInfoDTO finalCocDetail(long orderId);


	/**
	 * maintenance-审核
	 * @param cocMaintenanceInfoDTO
	 * @return
	 * */
	boolean maintenanceAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO);


	/**
	 * maintenance-详情
	 * @param orderId
	 * @return
	 * */
	BasicOrderDTO maintenanceDetail(long orderId);

	void exportFacTemplate(HttpServletResponse response, CocFacFileVo cocFacFileVo);
}
