/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.additionalInfo.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.i18n.constant.LangPropertiesSuffixesEnum;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;


/**
 * 图片附加信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("additionalInfo/additionalInfo")
@Api(value = "图片附加信息", tags = "图片附加信息接口")
public class AdditionalInfoController extends BladeController {

	private final IAdditionalInfoService additionalInfoService;

	/**
	 * 图片附加信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入additionalInfo")
	public R save(@Valid @RequestBody AdditionalInfoEntity additionalInfo) {
		return R.status(additionalInfoService.save(additionalInfo));
	}

	/**
	 * 图片附加信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入additionalInfo")
	public R update(@Valid @RequestBody AdditionalInfoEntity additionalInfo) {
		return R.status(additionalInfoService.updateById(additionalInfo));
	}

	/**
	 * 图片附加信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入additionalInfo")
	public R submit(@Valid @RequestBody AdditionalInfoEntity additionalInfo) {
		return R.status(additionalInfoService.saveOrUpdate(additionalInfo));
	}

	/**
	 * 图片附加信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(additionalInfoService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 图片附加信息 删除
	 */
	@GetMapping("/findByBusinessIds")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R findByBusinessIds(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.data(additionalInfoService.selectAdditionalMapByBusinessIds(Func.toLongList(ids)));
	}

	public static void main(String[] args) {
		try {
			String i18nFile = MessageFormat.format("ValidationMessages{0}.properties",
				LangPropertiesSuffixesEnum.getPropertiesSuffixesEnum("en"));
			Resource resource = new ClassPathResource(i18nFile);
			EncodedResource encodedResource = new EncodedResource(resource,"UTF-8");
			Properties prop = PropertiesLoaderUtils.loadProperties(encodedResource);
			List<Object> objects = new LinkedList<>();
			List<Object> vs = new LinkedList<>();
			prop.forEach((key, value) -> {
				objects.add(key);
				vs.add(value);
			});
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
}
