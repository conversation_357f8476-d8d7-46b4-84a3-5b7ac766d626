/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.houseelectricalapply.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.survey.houseelectricalapply.entity.HouseElectricalApplyEntity;
import org.skyworth.ess.survey.houseelectricalapply.excel.HouseElectricalApplyExcel;
import org.skyworth.ess.survey.houseelectricalapply.mapper.HouseElectricalApplyMapper;
import org.skyworth.ess.survey.houseelectricalapply.service.IHouseElectricalApplyService;
import org.skyworth.ess.survey.houseelectricalapply.vo.HouseElectricalApplyVO;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 踏勘房屋电气应用设备 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Service
public class HouseElectricalApplyServiceImpl extends BaseServiceImpl<HouseElectricalApplyMapper, HouseElectricalApplyEntity> implements IHouseElectricalApplyService {

	@Override
	public IPage<HouseElectricalApplyVO> selectHouseElectricalApplyPage(IPage<HouseElectricalApplyVO> page, HouseElectricalApplyVO houseElectricalApply) {
		return page.setRecords(baseMapper.selectHouseElectricalApplyPage(page, houseElectricalApply));
	}


	@Override
	public List<HouseElectricalApplyExcel> exportHouseElectricalApply(Wrapper<HouseElectricalApplyEntity> queryWrapper) {
		List<HouseElectricalApplyExcel> houseElectricalApplyList = baseMapper.exportHouseElectricalApply(queryWrapper);
		//houseElectricalApplyList.forEach(houseElectricalApply -> {
		//	houseElectricalApply.setTypeName(DictCache.getValue(DictEnum.YES_NO, HouseElectricalApply.getType()));
		//});
		return houseElectricalApplyList;
	}

}
