/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;

import java.util.List;

/**
 * 代理商公司信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface AgentCompanyInfoMapper extends BaseMapper<AgentCompanyInfoEntity> {
	/**
	 * 验证代理商是否存在在途申请单
	 *
	 * @param ids 入参
	 * @return String
	 * <AUTHOR>
	 * @since 2024/1/3 16:34
	 **/
	String validAgentHasInTransitOrders(@Param("ids") List<Long> ids);

	/**
	 * 批量删除代理商
	 *
	 * @param updateUser 修改人
	 * @param ids        入参
	 * @return int
	 * <AUTHOR>
	 * @since 2024/3/12 16:27
	 **/
	boolean deleteBatch(@Param("ids") List<Long> ids, @Param("updateUser") Long updateUser);
}
