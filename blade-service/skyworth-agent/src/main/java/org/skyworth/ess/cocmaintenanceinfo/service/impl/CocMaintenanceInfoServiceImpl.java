/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.service.impl;

import com.aspose.words.SaveFormat;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepoove.poi.XWPFTemplate;
import lombok.AllArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.color.PDColor;
import org.apache.pdfbox.pdmodel.graphics.color.PDDeviceGray;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotationWidget;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import org.apache.pdfbox.pdmodel.interactive.form.PDSignatureField;
import org.skyworth.ess.aspect.FileSave;
import org.skyworth.ess.cocmaintenanceinfo.dto.BasicOrderDTO;
import org.skyworth.ess.cocmaintenanceinfo.dto.CocMaintenanceInfoDTO;
import org.skyworth.ess.cocmaintenanceinfo.entity.CocMaintenanceInfoEntity;
import org.skyworth.ess.cocmaintenanceinfo.excel.CocMaintenanceInfoExcel;
import org.skyworth.ess.cocmaintenanceinfo.mapper.CocMaintenanceInfoMapper;
import org.skyworth.ess.cocmaintenanceinfo.service.ICocMaintenanceInfoService;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocFacFileVo;
import org.skyworth.ess.cocmaintenanceinfo.vo.CocMaintenanceInfoVO;
import org.skyworth.ess.constant.OrderStatusConstants;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.ordernodesubstatus.entity.OrderNodeSubStatusEntity;
import org.skyworth.ess.ordernodesubstatus.service.IOrderNodeSubStatusService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.springblade.common.constant.DictNodeBizCodeEnum;
import org.springblade.common.utils.tool.TimeUtils;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * coc和维护信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */

@AllArgsConstructor
@Service
public class CocMaintenanceInfoServiceImpl extends BaseServiceImpl<CocMaintenanceInfoMapper, CocMaintenanceInfoEntity> implements ICocMaintenanceInfoService {

	private IOrderNodeSubStatusService orderNodeSubStatusService;

	private IAttachmentInfoClient attachmentInfoClient;

	private IOrderService orderService;

	private IReviewOrderService reviewOrderService;

	private IOrderRelatedUserService orderRelatedUser;

	@Override
	public IPage<CocMaintenanceInfoVO> selectCocMaintenanceInfoPage(IPage<CocMaintenanceInfoVO> page, CocMaintenanceInfoVO cocMaintenanceInfo) {
		return page.setRecords(baseMapper.selectCocMaintenanceInfoPage(page, cocMaintenanceInfo));
	}


	@Override
	public List<CocMaintenanceInfoExcel> exportCocMaintenanceInfo(Wrapper<CocMaintenanceInfoEntity> queryWrapper) {
		List<CocMaintenanceInfoExcel> cocMaintenanceInfoList = baseMapper.exportCocMaintenanceInfo(queryWrapper);
		//cocMaintenanceInfoList.forEach(cocMaintenanceInfo -> {
		//	cocMaintenanceInfo.setTypeName(DictCache.getValue(DictEnum.YES_NO, cocMaintenanceInfo.getType()));
		//});
		return cocMaintenanceInfoList;
	}


	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean qcVerifyAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		List<OrderNodeSubStatusEntity> contentVoList = cocMaintenanceInfoDTO.getContent();
		String nodeType = "QCVerification";
		String type = cocMaintenanceInfoDTO.getType();
		String remark = cocMaintenanceInfoDTO.getRemark();
		Long orderId = cocMaintenanceInfoDTO.getOrderId();

		contentVoList.stream().parallel().forEach(v -> {
			v.setBusinessType("approve");
			v.setOrderId(orderId);
			v.setNodeName(nodeType);
		});
		//删除子节点旧数据
		orderNodeSubStatusService.deleteByOrderId(orderId, nodeType, "approve");
		orderNodeSubStatusService.saveOrUpdateBatch(contentVoList);

		OrderFlowDTO orderFlowDTO = new OrderFlowDTO();
		Map<String, Object> variables = new HashMap<>();
		if ("1".equals(type)) {
			variables.put("examineApproveType", "1");
		} else {
			variables.put("examineApproveType", "2");
		}
		variables.put("wfCurrentType", "user");
		variables.put("comment", remark);
		orderFlowDTO.setBusinessId(orderId + "");

		getUserInfo(cocMaintenanceInfoDTO, variables);
		orderFlowDTO.setVariables(variables);
		reviewOrderService.examineApprove(orderFlowDTO);
		return true;
	}

	private void getUserInfo(CocMaintenanceInfoDTO cocMaintenanceInfoDTO, Map<String, Object> variables) {
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, cocMaintenanceInfoDTO.getOrderId()).eq(OrderRelatedUserEntity::getNodeType, DictNodeBizCodeEnum.INSTALL_WO_ASSIGNMENT.getDictCode()).
			in(OrderRelatedUserEntity::getUserType, UserTypeEnum.SITE_TECHNICIAN_LEADER.getName(), UserTypeEnum.ELECTRICIAN.getName());
		List<OrderRelatedUserEntity> orderRelatedUserEntityList = orderRelatedUser.list(orderRelatedUserEntityLambdaQueryWrapper);
		//踏勘人员
		List<OrderRelatedUserEntity> surveyUserList = orderRelatedUserEntityList.stream().filter(re -> re.getUserType().equals(UserTypeEnum.SITE_TECHNICIAN_LEADER.getValue())).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(surveyUserList)) {
			variables.put("siteEngineer", surveyUserList.get(0).getUserId());
		}
		//电气工程师
		List<OrderRelatedUserEntity> electricUserList = orderRelatedUserEntityList.stream().filter(re -> re.getUserType().equals(UserTypeEnum.ELECTRICIAN.getValue())).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(electricUserList)) {
			variables.put("electrician", electricUserList.get(0).getUserId());
		}
	}

	@Override
	public List<OrderNodeSubStatusEntity> qcVerifyDetail(Long orderId) {
		return orderNodeSubStatusService.getNodeByOrderId(orderId, "approve");
	}

	@FileSave
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean temporaryCocAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>();
		long orderId = cocMaintenanceInfoDTO.getOrderId();
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		int res;
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (cocMaintenanceInfoEntityList != null && !cocMaintenanceInfoEntityList.isEmpty()) {
			data.put("temporary_coc_img_biz_key", cocMaintenanceInfoDTO.getTemporaryCocImgBizKey());
			data.put("temporary_coc_start_date", cocMaintenanceInfoDTO.getTemporaryCocStartDate());
			res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		} else {
			res = baseMapper.insert(cocMaintenanceInfoDTO);
		}
		Map<String, Object> variables = new HashMap<>();
		getUserInfo(cocMaintenanceInfoDTO, variables);
		variables.put("wfCurrentType", "user");
		//更新工作流状态
		updateFlowWork(orderId, cocMaintenanceInfoDTO.getRemark(), variables);
		return res > 0;
	}

	@Override
	public CocMaintenanceInfoDTO temporaryCocDetail(long orderId) {
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (!cocMaintenanceInfoEntityList.isEmpty()) {
			CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
			CocMaintenanceInfoEntity cocMaintenanceInfo = cocMaintenanceInfoEntityList.get(0);
			BeanUtils.copyProperties(cocMaintenanceInfo, cocMaintenanceInfoDTO);
			List<Long> businessIds = new ArrayList<>();
			Long key = cocMaintenanceInfo.getTemporaryCocImgBizKey();
			if (ValidationUtil.isNotEmpty(key)) {
				businessIds.add(key);
				Map<Long, List<AttachmentInfoEntity>> attachments = attachmentInfoClient.findByBusinessIds(businessIds).getData();
				cocMaintenanceInfoDTO.setCocImg(attachments.get(key));
			}

			return cocMaintenanceInfoDTO;
		}
		return null;
	}

	@FileSave
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean facAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>();
		long orderId = cocMaintenanceInfoDTO.getOrderId();
		data.put("fac_landlord_sign", cocMaintenanceInfoDTO.getFacLandlordSign());
		data.put("fac_technician_sign", cocMaintenanceInfoDTO.getFacTechnicianSign());
		data.put("fac_declaration", cocMaintenanceInfoDTO.getFacDeclaration());
		data.put("customers_acceptance", cocMaintenanceInfoDTO.getCustomersAcceptance());
		data.put("signer_relationship", cocMaintenanceInfoDTO.getSignerRelationship());
		data.put("fac_plant_id", cocMaintenanceInfoDTO.getFacPlantId());
		data.put("fac_plant_name", cocMaintenanceInfoDTO.getFacPlantName());
		data.put("update_time", TimeUtils.getCurrentTime());
		//提交审核
		if ("2".equals(cocMaintenanceInfoDTO.getBizType())) {
			// 获取当前时间
			LocalDateTime now = LocalDateTime.now();
			// 加一个月
			LocalDateTime oneMonthLater = now.plusMonths(1);
			// 加一年
			LocalDateTime oneYearLater = now.plusYears(1);
			// 将时间转换为字符串
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			data.put("free_maintenance_service_date", oneMonthLater.format(formatter));
			data.put("warranty_date", oneYearLater.format(formatter));
			Map<String, Object> variables = new HashMap<>();
			variables.put("wfCurrentType", "role");
			//更新工作流状态
			updateFlowWork(orderId, cocMaintenanceInfoDTO.getRemark(), variables);

		}
		int res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		return res > 0;
	}

	@Override
	public CocMaintenanceInfoDTO facDetail(long orderId) {
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (!cocMaintenanceInfoEntityList.isEmpty()) {
			CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
			CocMaintenanceInfoEntity cocMaintenanceInfo = cocMaintenanceInfoEntityList.get(0);
			BeanUtils.copyProperties(cocMaintenanceInfo, cocMaintenanceInfoDTO);
			List<Long> businessIds = new ArrayList<>();
			Long facLandlordSign = cocMaintenanceInfo.getFacLandlordSign();
			Long facTechnicianSign = cocMaintenanceInfo.getFacTechnicianSign();
			Long customersAcceptance = cocMaintenanceInfo.getCustomersAcceptance();
			if (ValidationUtil.isNotEmpty(facLandlordSign)) {
				businessIds.add(facLandlordSign);
			}
			if (ValidationUtil.isNotEmpty(facTechnicianSign)) {
				businessIds.add(facTechnicianSign);
			}
			if (ValidationUtil.isNotEmpty(customersAcceptance)) {
				businessIds.add(customersAcceptance);
			}
			if (!businessIds.isEmpty()) {
				Map<Long, List<AttachmentInfoEntity>> attachments = attachmentInfoClient.findByBusinessIds(businessIds).getData();
				if (ValidationUtil.isNotEmpty(facLandlordSign)) {
					cocMaintenanceInfoDTO.setFacLandlordSignImg(attachments.get(facLandlordSign));
				}
				if (ValidationUtil.isNotEmpty(facTechnicianSign)) {
					cocMaintenanceInfoDTO.setTechnicianSignImg(attachments.get(facTechnicianSign));
				}
				if (ValidationUtil.isNotEmpty(customersAcceptance)) {
					cocMaintenanceInfoDTO.setCustomersAcceptanceFile(attachments.get(customersAcceptance));
				}
			}

			return cocMaintenanceInfoDTO;
		}
		return null;

	}

	@FileSave
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean balancePayAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>();
		long orderId = cocMaintenanceInfoDTO.getOrderId();
		data.put("balance_payment_confirm_doc_biz_key", cocMaintenanceInfoDTO.getBalancePaymentConfirmDocBizKey());
		int res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		Map<String, Object> variables = new HashMap<>();
		getUserInfo(cocMaintenanceInfoDTO, variables);
		variables.put("wfCurrentType", OrderStatusConstants.CURRENT_USER_ROLE);
		//更新工作流状态
		updateFlowWork(orderId, cocMaintenanceInfoDTO.getRemark(), variables);
		return res > 0;
	}

	@Override
	public CocMaintenanceInfoDTO balancePayDetail(long orderId) {
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (cocMaintenanceInfoEntityList != null && !cocMaintenanceInfoEntityList.isEmpty()) {
			CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
			CocMaintenanceInfoEntity cocMaintenanceInfo = cocMaintenanceInfoEntityList.get(0);
			BeanUtils.copyProperties(cocMaintenanceInfo, cocMaintenanceInfoDTO);
			List<Long> businessIds = new ArrayList<>();
			Long businessId = cocMaintenanceInfo.getBalancePaymentConfirmDocBizKey();
			if (ValidationUtil.isNotEmpty(businessId)) {
				businessIds.add(businessId);
				Map<Long, List<AttachmentInfoEntity>> attachments = attachmentInfoClient.findByBusinessIds(businessIds).getData();
				cocMaintenanceInfoDTO.setDocFile(attachments.get(businessId));
			}
			return cocMaintenanceInfoDTO;
		}
		return null;
	}

	@Override
	@FileSave
	public boolean finalCocAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>();
		long orderId = cocMaintenanceInfoDTO.getOrderId();
		Integer type = cocMaintenanceInfoDTO.getFinalCocType();
		data.put("final_coc_img_biz_key", cocMaintenanceInfoDTO.getFinalCocImgBizKey());
		data.put("final_coc_type", type != null ? type : 0);
		data.put("final_coc_start_date", cocMaintenanceInfoDTO.getFinalCocStartDate());
		data.put("client_confirmation_biz_key", cocMaintenanceInfoDTO.getClientConfirmationBizKey());
		data.put("delivery", cocMaintenanceInfoDTO.getDelivery());
		String deliveryNumber = cocMaintenanceInfoDTO.getDeliveryNumber();
		if (ValidationUtil.isNotEmpty(deliveryNumber)) {
			data.put("delivery_number", deliveryNumber);
		}
		//未选择永久
		if (type != null && type == 0) {
			data.put("final_coc_end_date", cocMaintenanceInfoDTO.getFinalCocEndDate());
		}
		int res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		Map<String, Object> variables = new HashMap<>();
		//更新工作流状态
		updateFlowWork(orderId, cocMaintenanceInfoDTO.getRemark(), variables);
		return res > 0;
	}

	@Override
	public CocMaintenanceInfoDTO finalCocDetail(long orderId) {
		LambdaQueryWrapper<CocMaintenanceInfoEntity> eq = Wrappers.<CocMaintenanceInfoEntity>query().lambda()
			.eq(CocMaintenanceInfoEntity::getOrderId, orderId);
		List<CocMaintenanceInfoEntity> cocMaintenanceInfoEntityList = baseMapper.selectList(eq);
		if (!cocMaintenanceInfoEntityList.isEmpty()) {
			CocMaintenanceInfoDTO cocMaintenanceInfoDTO = new CocMaintenanceInfoDTO();
			CocMaintenanceInfoEntity cocMaintenanceInfo = cocMaintenanceInfoEntityList.get(0);
			BeanUtils.copyProperties(cocMaintenanceInfo, cocMaintenanceInfoDTO);
			List<Long> businessIds = new ArrayList<>();
			Long cocKey = cocMaintenanceInfo.getFinalCocImgBizKey();
			Long clientKey = cocMaintenanceInfo.getClientConfirmationBizKey();
			if (ValidationUtil.isNotEmpty(cocKey)) {
				businessIds.add(cocKey);
			}
			if (ValidationUtil.isNotEmpty(clientKey)) {
				businessIds.add(clientKey);
			}
			if (!businessIds.isEmpty()) {
				Map<Long, List<AttachmentInfoEntity>> attachments = attachmentInfoClient.findByBusinessIds(businessIds).getData();
				if (ValidationUtil.isNotEmpty(cocKey)) {
					cocMaintenanceInfoDTO.setFinalCocImg(attachments.get(cocKey));
				}
				if (ValidationUtil.isNotEmpty(clientKey)) {
					cocMaintenanceInfoDTO.setClientConfirmationImg(attachments.get(clientKey));
				}
			}
			return cocMaintenanceInfoDTO;
		}
		return null;
	}

	@Override
	public boolean maintenanceAudit(CocMaintenanceInfoDTO cocMaintenanceInfoDTO) {
		Map<String, Object> data = new HashMap<>();
		long orderId = cocMaintenanceInfoDTO.getOrderId();
		data.put("free_maintenance_service_date", cocMaintenanceInfoDTO.getFreeMaintenanceServiceDate());
		data.put("warranty_date", cocMaintenanceInfoDTO.getWarrantyDate());
		int res = baseMapper.updateCocMaintenanceByOrderId(data, orderId);
		Map<String, Object> variables = new HashMap<>();
		//更新工作流状态
		updateFlowWork(orderId, cocMaintenanceInfoDTO.getRemark(), variables);
		return res > 0;
	}

	@Override
	public BasicOrderDTO maintenanceDetail(long orderId) {
		List<BasicOrderDTO> basicOrderDTOList = baseMapper.selectBasicOrderInfo(orderId);
		if (ValidationUtil.isNotEmpty(basicOrderDTOList) && !basicOrderDTOList.isEmpty()) {
			BasicOrderDTO basicOrderDTO = basicOrderDTOList.get(0);
			OrderEntity orderEntity = new OrderEntity();
			orderEntity.setSiteCountryCode(basicOrderDTO.getSiteCountryCode());
			orderEntity.setSiteProvinceCode(basicOrderDTO.getSiteProvinceCode());
			orderEntity.setSiteCityCode(basicOrderDTO.getSiteCityCode());
			OrderEntity orderEntityNew = orderService.findRegionInfoByCode(orderEntity);
			if (ValidationUtil.isNotEmpty(orderEntityNew)) {
				basicOrderDTO.setSiteCountryCode(orderEntityNew.getSiteCountryName());
				basicOrderDTO.setSiteProvinceCode(orderEntityNew.getSiteProvinceName());
				basicOrderDTO.setSiteCityCode(orderEntityNew.getSiteCityName());
			}
			return basicOrderDTO;
		}
		return null;
	}

	@Override
	public void exportFacTemplate(HttpServletResponse response, CocFacFileVo cocFacFileVo) {
		this.ConstructFacTemplate(response, cocFacFileVo);
	}

	private void ConstructFacTemplate(HttpServletResponse response, CocFacFileVo cocFacFileVo) {
		String pathPrefix = "";

		// 获取当前项目路径
		String projectDir = pathPrefix + "/opt/data/facfile";
		// 模板文件路径
		String templatePath = pathPrefix + "/opt/data/facfile/SKYWORTH_FAC_WORD_template.docx";

		File existfile = new File(templatePath);
		if (!existfile.exists()) {
			try {
				Resource resource = new ClassPathResource("facfile/SKYWORTH_FAC_WORD_template.docx");

				InputStream in = resource.getInputStream();
				File file = new File(templatePath);
				FileUtils.copyToFile(in, file);
			} catch (IOException e) {
				log.error("facfile template build fail!");
			}
		}

		// 设置响应头信息
		String fileName = "SKYWORTH_FAC_WORD_";

		// 将导出的文件写入响应流
		try {
			Map<String, Object> renderDataMap = getRenderDataMap(cocFacFileVo);
			String fileType = cocFacFileVo.getFileType();
			Long orderId = cocFacFileVo.getOrderId();

			XWPFTemplate template = XWPFTemplate.compile(templatePath).render(renderDataMap);
			if ("word".equals(fileType)) {
				// 设置响应类型为Word文档
				response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
				response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + orderId + ".docx");

				// 将Word文档写入响应输出流
				ServletOutputStream outputStream = response.getOutputStream();
				BufferedOutputStream bos = new BufferedOutputStream(outputStream);
				template.write(bos);
				bos.flush();
				outputStream.flush();
				bos.close();
				outputStream.close();
				template.close();
			} else if ("pdf".equals(fileType)) {
				long outPdfPathId = orderId + 1L;

				// 先将Word文档保存到临时文件
				String tempWordPath = projectDir + "/temp.docx";
				FileOutputStream os = new FileOutputStream(tempWordPath);
				template.write(os);
				os.close();
				template.close();

				// pdf文件目录
				String outPdfPath = projectDir + "/SKYWORTH_FAC_WORD_" + outPdfPathId + ".pdf";
				String signPdfPath = projectDir + "/SKYWORTH_FAC_WORD_" + orderId + ".pdf";


				// 使用Aspose.Words将Word转换为PDF
				InputStream tempWordPathIs = new FileInputStream(tempWordPath);
				com.aspose.words.Document doc = new com.aspose.words.Document(tempWordPathIs);
				FileOutputStream targetFileOs = new FileOutputStream(outPdfPath);
				doc.save(targetFileOs, SaveFormat.PDF);

				//给pdf添加签名区域
				this.createPdfSignature(outPdfPath, signPdfPath);
				tempWordPathIs.close();
				targetFileOs.close();

				// 设置响应类型为PDF文档
				response.setContentType("application/pdf");
				response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("SKYWORTH_FAC_WORD_", StandardCharsets.UTF_8) + orderId + ".pdf");

				// 将PDF文档写入响应输出流
				// 创建输入流并将文件内容写入响应输出流
				FileInputStream fileInputStream = new FileInputStream(signPdfPath);
				OutputStream outputStream = response.getOutputStream();
				byte[] buffer = new byte[4096];
				int bytesRead;
				while ((bytesRead = fileInputStream.read(buffer)) != -1) {
					outputStream.write(buffer, 0, bytesRead);
				}
				fileInputStream.close();
				outputStream.close();

				// 清理临时文件
				new File(outPdfPath).delete();
				new File(signPdfPath).delete();
				new File(tempWordPath).delete();
			}
		} catch (Exception e) {
			log.error("export fac template :", e);
		}
	}


	private Map<String, Object> getRenderDataMap(CocFacFileVo cocFacFileVo) {
		Map<String, Object> renderDataMap = new HashMap<>();
		Long orderId = cocFacFileVo.getOrderId();
		OrderEntity orderEntity = orderService.getById(orderId);
		renderDataMap.put("customerName", orderEntity.getCustomerName());
		renderDataMap.put("installationLocation", orderEntity.getSiteAddress());
		renderDataMap.put("customerWoNo", orderEntity.getOrderNumber());
		renderDataMap.put("name", orderEntity.getCustomerName());
		String capacity = "";
		if (ValidationUtil.isNotEmpty(cocFacFileVo.getCapacity())) {
			capacity = cocFacFileVo.getCapacity();
		}
		renderDataMap.put("capacity", capacity);
		renderDataMap.put("date", LocalDate.now().toString());
		return renderDataMap;
	}


	private void createPdfSignature(String sourceFile, String endFile) {
		try {
			// 加载现有的PDF文档
			PDDocument document = PDDocument.load(new File(sourceFile));

			// 获取文档的第一个页面，或者根据需要创建新页面
			PDPage page = document.getPage(0);

			// 创建一个交互式表单
			PDAcroForm acroForm = new PDAcroForm(document);
			document.getDocumentCatalog().setAcroForm(acroForm);

			// 创建签名字段
			PDField signatureField = new PDSignatureField(acroForm);
			signatureField.setPartialName("Signature");
			signatureField.setAlternateFieldName("Signature");

			// 创建一个注释作为签名字段的容器
			PDAnnotationWidget widget = new PDAnnotationWidget();
			PDRectangle pdRectangle = new PDRectangle(100, 735, 150, 37);
			widget.setRectangle(pdRectangle); // 设置签名区域的位置和大小
			widget.setPage(page);
			widget.setAnnotationName("Signature");
			float gray = 0.5f;
			PDColor color = new PDColor(new float[]{gray}, PDDeviceGray.INSTANCE);
			widget.setColor(color);

			// 将注释添加到页面
			page.getAnnotations().add(widget);

			// 将字段添加到表单
			acroForm.getFields().add(signatureField);

			// 保存修改后的PDF文档
			document.save(endFile);
			document.close();
		} catch (IOException e) {
			log.error("createPdfSignature occur error:", e);
		}
	}


	/**
	 * 更新工作流状态
	 */
	private void updateFlowWork(long orderId, String remark, Map<String, Object> variables) {
		variables.put("examineApproveType", "1");
		variables.put("comment", remark != null ? remark : "");
		OrderFlowDTO orderFlowDTO = new OrderFlowDTO();
		orderFlowDTO.setBusinessId(orderId + "");
		orderFlowDTO.setVariables(variables);
		reviewOrderService.examineApprove(orderFlowDTO);
	}


}
