/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.revieworder.orderworkflow.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.skyworth.ess.additionalInfo.entity.AdditionalInfoEntity;
import org.skyworth.ess.additionalInfo.service.IAdditionalInfoService;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.company.service.IAgentCompanyInfoService;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.mapper.OrderMapper;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.design.designinfo.entity.DesignInfoEntity;
import org.skyworth.ess.design.deviceItem.entity.DeviceItemEntity;
import org.skyworth.ess.design.deviceItem.service.IDeviceItemService;
import org.skyworth.ess.design.service.IDesignService;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.revieworder.orderworkflow.constant.OutCome;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.springblade.common.mail.SendMail;
import org.springblade.common.utils.tool.ValidationUtil;
import org.springblade.common.vo.BatchVO;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.flow.core.feign.IFlowClient;
import org.springblade.flow.core.vo.ApprovalVO;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.ExecutionException;

import static org.skyworth.ess.constant.OrderStatusConstants.*;

/**
 * 订单表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Slf4j
@Service

public class ReviewOrderServiceImpl extends BaseServiceImpl<OrderMapper, OrderEntity> implements IReviewOrderService {
	@Autowired
	private IFlowClient flowClient;
	@Autowired
	private IOrderWorkFlowService iOrderWorkFlowService;
	@Autowired
	private IAttachmentInfoClient attachmentInfoService;
	@Autowired
	private IAdditionalInfoService additionalInfoService;
	@Autowired
	private IOrderService orderService;
	@Autowired
	private IAgentCompanyInfoService iAgentCompanyInfoService;
	@Autowired
	private IDictBizClient dictBizClient;
	@Autowired
	private SendMail sendMail;
	@Lazy
	@Autowired
	private IDeviceItemService deviceItemService;


	/**
	 * @Description: 审核修改订单/取消订单
	 * @Param: [order]
	 * @Return: org.springblade.core.tool.api.R
	 * @Author: baixu
	 * @Date: 2023/11/27 18:29
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> examineEditOrCancelOrder(OrderDTO orderDTO) {
		if (ObjectUtils.isEmpty(orderDTO)) {
			throw new BusinessException("agent.reviewOrder.examineEditOrCancelOrder.orderDTO.notNull");
		}
		//修改订单审核后的订单信息
		OrderEntity orderEntity = new OrderEntity();
		BeanUtil.copyProperties(orderDTO.getOrderEntity(), orderEntity);
		boolean updateOrderInfoFlag = updateOrderInfo(orderEntity, orderDTO);
		//审批流转
		if (updateOrderInfoFlag) {
			R<?> examineApproveResult = examineApprove(orderDTO.getOrderFlowDTO());
			if (!examineApproveResult.isSuccess()) {
				throw new BusinessException("agent.reviewOrder.workFlow.fail");
			}
		}
		return R.status(true);
	}


	/**
	 * @Description: 审批流转
	 * @Param: [orderDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/11/28 16:42
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> examineApprove(OrderFlowDTO orderFlowDTO) {
		//工作流流转前数据处理
		OrderWorkFlowEntity orderWorkFlowEntity = preFlowTreatment(orderFlowDTO);
		//流程流转
		R<ApprovalVO> approvalVoResult = flowClient.completeOrderTask(orderFlowDTO);
		//工作流流转后数据处理
		laterFlowTreatment(approvalVoResult, orderWorkFlowEntity, orderFlowDTO);

		//工作流流转成功之后 则发送邮件
		if (ObjectUtils.isNotEmpty(approvalVoResult) && approvalVoResult.isSuccess()) {
			R<List<DictBiz>> flowMailRes = dictBizClient.getList("epc_flow_mail");
			List<DictBiz> flowMailList = flowMailRes.getData();
			OrderEntity orderEntity = orderService.getById(orderFlowDTO.getBusinessId());
			StringBuilder text = new StringBuilder();
			if (CollectionUtil.isNotEmpty(flowMailList) && ObjectUtils.isNotEmpty(orderEntity)) {
				DictBiz dictBiz = flowMailList.get(0);
				text.append(dictBiz.getDictValue()).append("\n\n")
					.append(dictBiz.getAttribute1().replace("${orderNum}", orderEntity.getOrderNumber()))
					.append("\n\n").append(dictBiz.getAttribute2())
					.append("\n\n").append(dictBiz.getAttribute3());
			}
			//获取当前流程
			String taskName = orderWorkFlowEntity.getWfCurrentStatus();
			//构建接收人地址
			List<DictBiz> dictBizList = dictBizClient.getDataByCodeAndKey("wf_schedule", taskName).getData();
			if (CollectionUtil.isNotEmpty(dictBizList)) {
				DictBiz dictBiz = dictBizList.get(0);
				String toMail = dictBiz.getAttribute1();
				if (ValidationUtil.isNotEmpty(toMail)) {
					Resource resource = new ClassPathResource("QRcode/QRcode.png");
					try {
						InputStream in = resource.getInputStream();
						File file = new File("/opt/data/Qr/QRcode.png");
						FileUtils.copyToFile(in, file);
						if (ValidationUtil.isNotEmpty(file)) {
							List<File> fileList = new ArrayList<>();
							fileList.add(file);
							boolean flag = sendMail.sendMailMultiParams(fileList, toMail, null, null, "Skyworth Push Email Notification", text.toString());
							log.info("send mail examineApprove flag : {}", flag);
						}
					} catch (Exception e) {
						log.error(e.getMessage());
					}
				}
			}
		}

		return R.status(true);
	}


	/**
	 * @Description: 工作流流转前数据处理
	 * @Param: [orderFlowDTO]
	 * @Return: org.springblade.flow.core.dto.OrderFlowDTO
	 * @Author: baixu
	 * @Date: 2023/12/28 15:18
	 **/
	@Transactional(rollbackFor = Exception.class)
	public OrderWorkFlowEntity preFlowTreatment(OrderFlowDTO orderFlowDTO) {
		//流程流转前数据校验
		preProcessTaskCheck(orderFlowDTO);
		//查询审批信息
		LambdaQueryWrapper<OrderWorkFlowEntity> orderWorkFlowEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderWorkFlowEntityLambdaQueryWrapper.eq(OrderWorkFlowEntity::getOrderId, orderFlowDTO.getBusinessId());
		OrderWorkFlowEntity orderWorkFlowEntity = iOrderWorkFlowService.getOne(orderWorkFlowEntityLambdaQueryWrapper);
		//流程实例id
		orderFlowDTO.setProcessInstanceId(orderWorkFlowEntity.getWfInstanceId());
		//任务id
		orderFlowDTO.setTaskId(orderWorkFlowEntity.getTaskId());
		//任务进度
		orderFlowDTO.setTaskName(orderWorkFlowEntity.getWfCurrentStatus());
		//放入工作流参数
		//流程参数
		Map<String, Object> variables = orderFlowDTO.getVariables();
		String examineApproveType = String.valueOf(variables.get("examineApproveType"));
		if (EXAMINE_APPROVE_PASS.equals(examineApproveType)) {
			variables.put("outcome", OutCome.PASS.getDec());
			orderFlowDTO.setFlag(true);
		} else {
			variables.put("outcome", OutCome.REJECT.getDec());
			orderFlowDTO.setFlag(false);
		}
		return orderWorkFlowEntity;
	}


	/**
	 * @Description: 工作流流转后数据处理
	 * @Param: [approvalVoResult, orderWorkFlowEntity]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/28 15:25
	 **/
	@Transactional(rollbackFor = Exception.class)
	public void laterFlowTreatment(R<ApprovalVO> approvalVoResult, OrderWorkFlowEntity orderWorkFlowEntity, OrderFlowDTO orderFlowDTO) {
		if (ObjectUtils.isEmpty(approvalVoResult)) {
			throw new BusinessException("agent.reviewOrder.workFlow.fail");
		}
		//流程参数
		Map<String, Object> variables = orderFlowDTO.getVariables();
		//审批状态
		String examineApproveType = String.valueOf(variables.get("examineApproveType"));
		if (ObjectUtils.isEmpty(approvalVoResult.getData())) {
			//取消和办结节点返回可能为空
			if (!FINAL_NODE.equals(orderWorkFlowEntity.getWfCurrentStatus()) && !EXAMINE_APPROVE_CANCEL.equals(examineApproveType)) {
				throw new BusinessException("agent.reviewOrder.laterFlowTreatment.IncorrectParameter.Transmission");
			}
		}
		//工作流内部错误
		if (ObjectUtils.isNotEmpty(approvalVoResult) && !approvalVoResult.isSuccess()) {
			log.error(StringUtils.isBlank(approvalVoResult.getMsg()) ? "Process flow failure" : approvalVoResult.getMsg());
			throw new BusinessException("agent.reviewOrder.workFlow.fail");
		}
		//处理工作流关系表
		if (FINAL_NODE.equals(orderWorkFlowEntity.getWfCurrentStatus())) {
			//办结节点 特殊节点
			orderCloseCancelEditFlowData(orderWorkFlowEntity, FINAL_NODE);
		} else {
			if (EXAMINE_APPROVE_PASS.equals(examineApproveType) || EXAMINE_APPROVE_NOT_PASS.equals(examineApproveType)) {
				log.info("laterFlowTreatment : {}", orderWorkFlowEntity);
				// 第8步 quotes 拒绝 清空库存管理员审批时间，需要放到 orderPassEditFlowData 方法上面，否则 getWfCurrentStatus变成了 踏勘设计
				if (EXAMINE_APPROVE_NOT_PASS.equals(examineApproveType) && WF_CURRENT_STATUS_QUOTES.equals(orderWorkFlowEntity.getWfCurrentStatus())) {
					iOrderWorkFlowService.updateWarehouseApproveCreateTime(orderWorkFlowEntity);
					orderWorkFlowEntity.setWarehouseApproveCreateTime(null);
					// 清空物料相关价格、删除物料包记录（库存管理员审批通过之前实时取业务字典）
					this.updateDesignItemInfo(orderFlowDTO.getBusinessId());
				}
				//订单审批通过工作流数据处理
				orderPassEditFlowData(approvalVoResult, orderWorkFlowEntity, variables);
			} else {
				//订单取消工作流数据处理
				orderCloseCancelEditFlowData(orderWorkFlowEntity, ORDER_STATUS_CLOSED);
				// 取消订单
				iOrderWorkFlowService.updateWarehouseApproveCreateTime(orderWorkFlowEntity);
			}
		}
		//放入订单id
		boolean orderWorkFlowUpdateFlag = iOrderWorkFlowService.updateById(orderWorkFlowEntity);
		if (!orderWorkFlowUpdateFlag) {
			throw new BusinessException("agent.reviewOrder.laterFlowTreatment.orderWorkFlowUpdate.fail");
		}
		//超时提交时将ehs版本时间置空
		if (ObjectUtils.isNotEmpty(variables.get("timeOutRejectFlag"))) {
			LambdaUpdateWrapper<OrderWorkFlowEntity> orderEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			orderEntityLambdaUpdateWrapper.eq(OrderWorkFlowEntity::getId, orderWorkFlowEntity.getId());
			orderEntityLambdaUpdateWrapper.set(OrderWorkFlowEntity::getEhsVersion, null);
			iOrderWorkFlowService.update(orderEntityLambdaUpdateWrapper);
		}
	}


	private void updateDesignItemInfo(String orderId) {
		UpdateWrapper<DesignInfoEntity> updateWrapper = new UpdateWrapper<>();
		updateWrapper.eq("order_id", orderId);
		// 清空物料包价格
		deviceItemService.updateDesignBaseSkuPrice(Long.parseLong(orderId), null, null);
		// 查询订单下所有的物料信息，包含自定义和物料包
		LambdaQueryWrapper<DeviceItemEntity> queryItem = Wrappers.lambdaQuery();
		queryItem.eq(DeviceItemEntity::getOrderId, Long.parseLong(orderId)).eq(DeviceItemEntity::getIsDeleted, 0);
		List<DeviceItemEntity> allItem = deviceItemService.list(queryItem);
		// 清空自定义物料价格
		List<DeviceItemEntity> customizeList = allItem.stream().filter(p -> StringUtils.isEmpty(p.getItemBasePackage())).collect(Collectors.toList());
		List<DeviceItemEntity> updateCustomizePriceList = new ArrayList<>();
		for (DeviceItemEntity customizeItem : customizeList) {
			DeviceItemEntity update = new DeviceItemEntity();
			update.setId(customizeItem.getId());
			update.setOrderId(customizeItem.getOrderId());
			update.setItemPrice(null);
			updateCustomizePriceList.add(update);
		}
		// 可能没有选择自定义物料
		if (CollectionUtil.isNotEmpty(updateCustomizePriceList)) {
			deviceItemService.batchUpdateSkuInfoPrice(updateCustomizePriceList);
		}
		// 删除物料包信息
		List<DeviceItemEntity> basePackageList = allItem.stream().filter(p -> !StringUtils.isEmpty(p.getItemBasePackage())).collect(Collectors.toList());
		List<Long> baseItemId = basePackageList.stream().map(DeviceItemEntity::getId).collect(Collectors.toList());
		deviceItemService.deleteLogic(baseItemId);
	}

	/**
	 * @Description: 流程流转前数据校验
	 * @Param: [orderFlowDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/28 15:07
	 **/
	@Transactional(rollbackFor = Exception.class)
	public void preProcessTaskCheck(OrderFlowDTO orderFlowDTO) {
		//参数校验
		workFlowParamValid(orderFlowDTO);
		//流程参数
		Map<String, Object> variables = orderFlowDTO.getVariables();
		//审批状态
		List<String> approveTypeList = Arrays.asList(EXAMINE_APPROVE_PASS, EXAMINE_APPROVE_NOT_PASS, EXAMINE_APPROVE_CANCEL);
		String examineApproveType = String.valueOf(variables.get("examineApproveType"));
		if (!approveTypeList.contains(examineApproveType)) {
			throw new BusinessException("agent.reviewOrder.preProcessTaskCheck.examineApproveType.error");
		}
		//查询订单信息
		OrderEntity orderEntity = orderService.getById(orderFlowDTO.getBusinessId());
		if (ObjectUtils.isEmpty(orderEntity)) {
			throw new BusinessException("agent.reviewOrder.preProcessTaskCheck.order.notNull");
		}
		LambdaQueryWrapper<OrderWorkFlowEntity> orderWorkFlowEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderWorkFlowEntityLambdaQueryWrapper.eq(OrderWorkFlowEntity::getOrderId, orderFlowDTO.getBusinessId());
		long orderFlowInfoCount = iOrderWorkFlowService.count(orderWorkFlowEntityLambdaQueryWrapper);
		if (orderFlowInfoCount > 1 || orderFlowInfoCount == 0) {
			throw new BusinessException("agent.reviewOrder.preProcessTaskCheck.orderFlowInfo.error");
		}
	}


	/**
	 * @Description: 审核后修改订单信息
	 * @Param: [orderEntity, orderDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/11/28 17:47
	 **/
	@Transactional(rollbackFor = Exception.class)
	public boolean updateOrderInfo(OrderEntity orderEntity, OrderDTO orderDTO) {
		boolean updateOrderInfoFlag = true;
		//修改管理员添加的订单信息
		if (ObjectUtils.isEmpty(orderEntity.getId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		//参数校验
		LambdaQueryWrapper<AgentCompanyInfoEntity> agentCompanyInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		agentCompanyInfoEntityLambdaQueryWrapper.eq(AgentCompanyInfoEntity::getId, orderEntity.getDistributorId());
		agentCompanyInfoEntityLambdaQueryWrapper.select(AgentCompanyInfoEntity::getCompanyAttributes, AgentCompanyInfoEntity::getDeptId);
		AgentCompanyInfoEntity agentCompanyInfoEntity = iAgentCompanyInfoService.getOne(agentCompanyInfoEntityLambdaQueryWrapper);
		//添加代理商部门
		orderEntity.setBelongDeptId(agentCompanyInfoEntity.getDeptId());
		LambdaUpdateWrapper<OrderEntity> orderEntityLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
		orderEntityLambdaUpdateWrapper.eq(OrderEntity::getId, orderEntity.getId());
		orderEntityLambdaUpdateWrapper.set(OrderEntity::getDistributorId, orderEntity.getDistributorId()).set(OrderEntity::getBelongDeptId, orderEntity.getBelongDeptId()).set(OrderEntity::getRolloutManagerId, orderEntity.getRolloutManagerId()).set(OrderEntity::getCustomerName, orderEntity.getCustomerName()).set(OrderEntity::getCustomerPhone, orderEntity.getCustomerPhone()).set(OrderEntity::getCustomerEmail, orderEntity.getCustomerEmail()).set(OrderEntity::getCustomerCompanyName, orderEntity.getCustomerCompanyName()).set(OrderEntity::getSiteCountryCode, orderEntity.getSiteCountryCode()).set(OrderEntity::getSiteProvinceCode, orderEntity.getSiteProvinceCode()).set(OrderEntity::getSiteCityCode, orderEntity.getSiteCityCode()).set(OrderEntity::getSiteAddress, orderEntity.getSiteAddress()).set(OrderEntity::getInstallReason, orderEntity.getInstallReason()).set(OrderEntity::getInstallBudgets, orderEntity.getInstallBudgets()).set(OrderEntity::getTentativeInstallStartDate, orderEntity.getTentativeInstallStartDate()).set(OrderEntity::getTentativeInstallEndDate, orderEntity.getTentativeInstallEndDate()).set(OrderEntity::getSurveyDate, orderEntity.getSurveyDate()).set(OrderEntity::getAdditionalDocBizKey, orderEntity.getAdditionalDocBizKey()).set(OrderEntity::getSecondContactName, orderEntity.getSecondContactName()).set(OrderEntity::getSecondContactPhone, orderEntity.getSecondContactPhone()).set(OrderEntity::getProjectType, orderEntity.getProjectType()).set(OrderEntity::getProjectTypeOtherRemark, orderEntity.getProjectTypeOtherRemark()).set(OrderEntity::getLongitude, orderEntity.getLongitude()).set(OrderEntity::getLatitude, orderEntity.getLatitude()).set(OrderEntity::getDistanceRestriction, orderEntity.getDistanceRestriction());
		boolean updateOrderFlag = this.update(orderEntityLambdaUpdateWrapper);
		if (!updateOrderFlag) {
			throw new BusinessException("agent.reviewOrder.updateOrderInfo.updateOrder.fail");
		}
		//保存或修改附件信息
		if (ObjectUtils.isNotEmpty(orderDTO.getSkyWorthFileEntity())) {
			SkyWorthFileEntity skyWorthFileEntity = orderDTO.getSkyWorthFileEntity();
			updateOrderInfoFlag = setOrUpdateFileImgInfo(skyWorthFileEntity);
		}
		return updateOrderInfoFlag;
	}


	/**
	 * @Description: 订单审批通过拒绝工作流数据收集
	 * @Param: [bladeFlowDataResult, orderEntity, orderDTO, orderWorkFlowEntity]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/11/28 10:53
	 **/
	private void orderPassEditFlowData(R<ApprovalVO> approvalVoResult, OrderWorkFlowEntity orderWorkFlowEntity, Map<String, Object> variables) {
		//更新订单工作流关系表
		if (ObjectUtils.isNotEmpty(approvalVoResult) && ObjectUtils.isNotEmpty(approvalVoResult.getData())) {
			ApprovalVO approvalVO = approvalVoResult.getData();
			//任务id
			orderWorkFlowEntity.setTaskId(approvalVO.getTaskId());
			//工作流当前状态/订单进度
			orderWorkFlowEntity.setWfCurrentStatus(approvalVO.getTaskName());
			//当前处理节点角色或人
			orderWorkFlowEntity.setWfCurrentRole(approvalVO.getAssignee());
			//当前处理节点角色或人名称
			orderWorkFlowEntity.setWfCurrentRoleName(approvalVO.getAssigneeName());
			//当前处理节点类型
			if (ObjectUtils.isNotEmpty(variables.get("wfCurrentType"))) {
				orderWorkFlowEntity.setWfCurrentType(String.valueOf(variables.get("wfCurrentType")));
			}
			//上一步审批状态
			if (ObjectUtils.isNotEmpty(variables.get("examineApproveType"))) {
				orderWorkFlowEntity.setAuditStatus(Integer.parseInt(variables.get("examineApproveType").toString()));
			}
			//ehs确认节点 需要放入时间标志
			if (ObjectUtils.isNotEmpty(variables.get("ehsVersion"))) {
				orderWorkFlowEntity.setEhsVersion((String) variables.get("ehsVersion"));
			}
		}
	}

	/**
	 * @Description: 保存附件或图片信息
	 * @Param: [skyWorthFileEntity]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/4 13:40
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean setOrUpdateFileImgInfo(SkyWorthFileEntity skyWorthFileEntity) {
		if (ObjectUtils.isNotEmpty(skyWorthFileEntity.getBatchVO())) {
			//保存附件或图片信息
			BatchVO<AttachmentInfoEntity> batchVO = skyWorthFileEntity.getBatchVO();
			if (CollectionUtils.isEmpty(batchVO.getAddList()) && CollectionUtils.isEmpty(batchVO.getUpdateList()) && CollectionUtils.isEmpty(batchVO.getDeleteList())) {
				log.debug("The picture information is empty");
			} else {
				R<?> attachmentInfo = attachmentInfoService.saveAndUpdate(skyWorthFileEntity.getBatchVO());
				if (!attachmentInfo.isSuccess()) {
					throw new BusinessException("agent.reviewOrder.setOrUpdateFileImgInfo.attachmentInfo.fail");
				}
			}
		}
		//保存备注信息
		if (ObjectUtils.isNotEmpty(skyWorthFileEntity.getImgDescOperationList())) {
			List<Map<String, String>> imgDescOperationList = skyWorthFileEntity.getImgDescOperationList();
			List<AdditionalInfoEntity> additionalInfoEntityList = JSON.parseArray(JSON.toJSONString(imgDescOperationList), AdditionalInfoEntity.class);
			//过滤下备注key为空的情况
			long count = additionalInfoEntityList.stream().filter(re -> ObjectUtils.isNotEmpty(re.getImgBizKey())).count();
			if (count > 0) {
				additionalInfoService.saveAdditionalInfoEntityList(additionalInfoEntityList);
			}
		}
		return true;
	}


	/**
	 * @Description: 工作流参数校验
	 * @Param: [orderFlowDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/20 16:13
	 **/
	private void workFlowParamValid(OrderFlowDTO orderFlowDTO) {
		if (ObjectUtils.isEmpty(orderFlowDTO)) {
			throw new BusinessException("agent.ehsSubmission.submitEhsInfoExamine.orderFlowDTO.notNull");
		}
		//业务id
		if (StringUtils.isBlank(orderFlowDTO.getBusinessId())) {
			throw new BusinessException("agent.materialCollection.setWorkFlowData.businessId.notNull");
		}

		if (ObjectUtils.isEmpty(orderFlowDTO.getVariables())) {
			throw new BusinessException("agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.notNull");
		}
		//流程参数
		Map<String, Object> variables = orderFlowDTO.getVariables();
		if (ObjectUtils.isEmpty(variables.get("examineApproveType")) || StringUtils.isBlank(String.valueOf(variables.get("examineApproveType")))) {
			throw new BusinessException("agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.examineApproveType.notNull");
		}
	}


	/**
	 * @Description: 订单审批办结取消工作流数据收集
	 * @Param: [orderWorkFlowEntity]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/11/28 10:56
	 **/
	private void orderCloseCancelEditFlowData(OrderWorkFlowEntity orderWorkFlowEntity, String nodeStatus) {
		//取消订单 修改状态
		orderWorkFlowEntity.setTaskId(ORDER_STATUS_CLOSED_NULL);
		orderWorkFlowEntity.setWfCurrentStatus(nodeStatus.equals(FINAL_NODE) ? MAINTENANCE : ORDER_STATUS_CLOSED);
		orderWorkFlowEntity.setWfCurrentRole(ORDER_STATUS_CLOSED_NULL);
		orderWorkFlowEntity.setWfCurrentRoleName(ORDER_STATUS_CLOSED_NULL);
		orderWorkFlowEntity.setWfCurrentType(ORDER_STATUS_CLOSED_NULL);
		orderWorkFlowEntity.setAuditStatus(AUDIT_STATUS_NO_PASS);
	}


}
