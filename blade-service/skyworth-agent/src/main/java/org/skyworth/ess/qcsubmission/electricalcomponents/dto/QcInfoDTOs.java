package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.system.entity.SkyWorthFileEntity;

/**
 * QCDTOs实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.dto
 *
 * <AUTHOR>
 * @since 2023/12/1 - 12 - 01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QcInfoDTOs extends SkyWorthFileEntity{
	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 节点名称
	 */
	@ApiModelProperty(value = "节点名称")
	private String nodeName;
	/**
	 * 子节点名称及保存状态
	 */
	@ApiModelProperty(value = "子节点名称及保存状态")
	private SubNodeSaveStatusDTO subNodeSaveStatus;
	/**
	 * 逆变器安装
	 */
	@ApiModelProperty(value = "逆变器安装图片信息")
	private InverterInstallationDTO inverterInstallationDTO;
	/**
	 * 太阳能板安装
	 */
	@ApiModelProperty(value = "太阳能板安装图片信息")
	private SolarPanelsDTO solarPanelsDTO;
	/**
	 * 电气组件
	 */
	@ApiModelProperty(value = "电气组件图片信息")
	private ElectricalComponentsDTO electricalComponentsDTO;
	/**
	 * 接地设备
	 */
	@ApiModelProperty(value = "接地设备图片信息")
	private GroundingEquipmentDTO groundingEquipmentDTO;
	/**
	 * 安装机架
	 */
	@ApiModelProperty(value = "安装机架图片信息")
	private MountingAndRackingDTO mountingAndRackingDTO;
	/**
	 * 安装与遵守
	 */
	@ApiModelProperty(value = "安装与遵守图片信息")
	private SafetyAndComplianceDTO safetyAndComplianceDTO;
	/**
	 * 系统测试
	 */
	@ApiModelProperty(value = "系统测试图片信息")
	private SystemTestingDTO systemTestingDTO;
	/**
	 * 最终检验
	 */
	@ApiModelProperty(value = "最终检验图片信息")
	private FinalInspectionDTO finalInspectionDTO;

}
