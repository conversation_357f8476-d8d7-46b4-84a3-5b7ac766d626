/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.ehsinfo.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.MapUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.skyworth.ess.aspect.FileSave;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.ehssubmission.ehsinfo.dto.EhsInfoDTO;
import org.skyworth.ess.ehssubmission.ehsinfo.entity.EhsInfoEntity;
import org.skyworth.ess.ehssubmission.ehsinfo.excel.EhsInfoExcel;
import org.skyworth.ess.ehssubmission.ehsinfo.mapper.EhsInfoMapper;
import org.skyworth.ess.ehssubmission.ehsinfo.service.IEhsInfoService;
import org.skyworth.ess.ehssubmission.ehsinfo.vo.EhsInfoVO;
import org.skyworth.ess.ehssubmission.toolsmachinery.entity.ToolsMachineryEntity;
import org.skyworth.ess.ehssubmission.toolsmachinery.service.IToolsMachineryService;
import org.skyworth.ess.entity.OrderWorkFlowEntity;
import org.skyworth.ess.installwoassignment.constant.UserTypeEnum;
import org.skyworth.ess.qcsubmission.installrelatedInfo.entity.InstallRelatedInfoEntity;
import org.skyworth.ess.qcsubmission.installrelatedInfo.service.IInstallRelatedInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IAttachmentInfoService;
import org.skyworth.ess.revieworder.orderworkflow.service.IOrderWorkFlowService;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.revieworder.orderworkflow.vo.AttachmentDescVO;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.springblade.common.constant.DictBizCodeEnum;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.system.entity.AttachmentInfoEntity;
import org.springblade.system.entity.SkyWorthFileEntity;
import org.springblade.system.entity.User;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ehs信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Service
@AllArgsConstructor
public class EhsInfoServiceImpl extends BaseServiceImpl<EhsInfoMapper, EhsInfoEntity> implements IEhsInfoService {

	private final IReviewOrderService iReviewOrderService;

	private final IToolsMachineryService iToolsMachineryService;

	private final IOrderService iOrderService;

	private final IUserSearchClient userSearchClient;

	private final IInstallRelatedInfoService iInstallRelatedInfoService;

	private final IOrderRelatedUserService iOrderRelatedUserService;

	private final IAttachmentInfoService iAttachmentInfoService;

	private final IOrderWorkFlowService iOrderWorkFlowService;


	/**
	 * @Description: 新增ehs基本信息和相关工具设备信息
	 * @Param: [infoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/7 10:48
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	@FileSave
	public R<?> insertEhsInfo(EhsInfoDTO infoDTO) {
		insertOrUpdateEhsInfo(infoDTO);
		return R.status(true);
	}


	/**
	 * @Description: 提交ehs信息进行审核
	 * @Param: [infoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/7 13:28
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<?> submitEhsInfoExamine(EhsInfoDTO infoDTO) {
		if (ObjectUtils.isEmpty(infoDTO.getOrderFlowDTO())) {
			throw new BusinessException("agent.ehsSubmission.submitEhsInfoExamine.orderFlowDTO.notNull");
		}
		boolean submitEhsInfoResult = true;
		//获取当前日期
		String format = DateUtil.formatDate(new Date());
		//ehs最新版本时间
		if (ObjectUtils.isNotEmpty(infoDTO.getEhsInfoEntity())) {
			infoDTO.getEhsInfoEntity().setEhsVersion(format);
		}
		//ehs最新版本时间
		infoDTO.getAddToolsMachineryEntityList().forEach(re -> re.setEhsVersion(format));
		//保存或修改ehs信息
		insertOrUpdateEhsInfo(infoDTO);
		//保存或修改附件信息
		if (ObjectUtils.isNotEmpty(infoDTO.getSkyWorthFileEntity())) {
			submitEhsInfoResult = iReviewOrderService.setOrUpdateFileImgInfo(infoDTO.getSkyWorthFileEntity());
		}
		//审批流转
		if (submitEhsInfoResult) {
			infoDTO.getOrderFlowDTO().getVariables().put("ehsVersion", infoDTO.getEhsInfoEntity().getEhsVersion());
			R<?> examineApproveResult = iReviewOrderService.examineApprove(infoDTO.getOrderFlowDTO());
			if (!examineApproveResult.isSuccess()) {
				throw new BusinessException("agent.reviewOrder.workFlow.fail");
			}
		}
		return R.status(true);
	}


	/**
	 * @Description: 查询订单信息ehs基本信息和相关工具设备信息
	 * @Param: [infoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2023/12/7 13:42
	 **/
	@Override
	public R<?> selectEhsInfo(EhsInfoDTO infoDTO) {
		if (ObjectUtils.isEmpty(infoDTO)) {
			throw new BusinessException("agent.ehsSubmission.selectEhsInfo.ehsInfoDTO.notNull");
		}
		//订单id
		if (ObjectUtils.isEmpty(infoDTO.getOrderId())) {
			throw new BusinessException("agent.createOrder.orderId.notNull");
		}
		EhsInfoDTO resultEhsInfo = new EhsInfoDTO();
		//如果传值 代表在历史选取版本日期 按传参获取版本
		if (ObjectUtils.isEmpty(infoDTO.getEhsVersion())) {
			//查询流程信息
			OrderWorkFlowEntity orderWorkFlowEntity = getFlowInfo(infoDTO);
			if (ObjectUtils.isNotEmpty(orderWorkFlowEntity)) {
				//如果当前节点在ehs，并且通过goCheck点击进来查看历史数据标志 返回空
				if ("ehsSubmission".equals(orderWorkFlowEntity.getWfCurrentStatus()) && "1".equals(infoDTO.getByGoCheckEnterFlag())) {
					return R.data(resultEhsInfo);
				}
			}
			if (ObjectUtils.isNotEmpty(orderWorkFlowEntity.getEhsVersion())) {
				//如果时间 版本有值 一定是经过流转的
				infoDTO.setEhsVersion(orderWorkFlowEntity.getEhsVersion());
			}
		}
		//查询订单信息
		OrderEntity orderEntity = getOrderInfo(infoDTO);
		if (ObjectUtils.isNotEmpty(orderEntity)) {
			//交付经理名称字典转换
			R<List<User>> userListResultInfo = userSearchClient.listByUser(String.valueOf(orderEntity.getRolloutManagerId()));
			if (ObjectUtils.isNotEmpty(userListResultInfo) && CollectionUtils.isNotEmpty(userListResultInfo.getData())) {
				//用户id是唯一的 这里取第一个
				orderEntity.setRolloutManagerName(userListResultInfo.getData().get(0).getRealName());
			}
			//查询省市区字典
			orderEntity = iOrderService.findRegionInfoByCode(orderEntity);
			//获取施工日期
			setConstructionDate(infoDTO, resultEhsInfo);
			//获取踏勘人员
			setSurveyPerson(infoDTO, resultEhsInfo);
			resultEhsInfo.setOrderEntity(orderEntity);
		}
		//添加查看历史标志
		getGoCheckFlag(infoDTO);
		resultEhsInfo.setGoCheckFlag(infoDTO.isGoCheckFlag());
		//添加ehs总体信息
		getEhsAllInfo(infoDTO, resultEhsInfo);
		return R.data(resultEhsInfo);
	}


	/**
	 * @Description: ehs历史版本下拉列表
	 * @Param: [infoDTO]
	 * @Return: org.springblade.core.tool.api.R<?>
	 * @Author: baixu
	 * @Date: 2024/1/18 17:57
	 **/
	@Override
	public R<?> getEhsHistoryDataList(EhsInfoDTO infoDTO) {
		//查询ehs信息
		LambdaQueryWrapper<EhsInfoEntity> ehsInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		ehsInfoEntityLambdaQueryWrapper.eq(EhsInfoEntity::getOrderId, infoDTO.getOrderId());
		ehsInfoEntityLambdaQueryWrapper.isNotNull(EhsInfoEntity::getEhsVersion);
		List<EhsInfoEntity> ehsInfoEntityList = this.list(ehsInfoEntityLambdaQueryWrapper);
		if (CollectionUtils.isEmpty(ehsInfoEntityList)) {
			return R.data(Collections.emptyList());
		}
		//查询流程信息
		OrderWorkFlowEntity orderWorkFlowEntity = getFlowInfo(infoDTO);
		if (ObjectUtils.isNotEmpty(orderWorkFlowEntity)) {
			//如果当前节点在ehs，并且通过goCheck点击进来查看历史数据标志 返回空
			if ("ehsSubmission".equals(orderWorkFlowEntity.getWfCurrentStatus())) {
				ehsInfoEntityList = ehsInfoEntityList.stream().filter(re -> !re.getEhsVersion().equals(orderWorkFlowEntity.getEhsVersion())).collect(Collectors.toList());
			}
		}
		return R.data(ehsInfoEntityList);
	}


	/**
	 * @Description: 获取ehs总体信息
	 * @Param: [infoDTO, resultEhsInfo]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2024/1/19 11:10
	 **/
	private void getEhsAllInfo(EhsInfoDTO infoDTO, EhsInfoDTO resultEhsInfo) {
		//获取ehs基本信息
		setEhsBasicInfo(infoDTO, resultEhsInfo);
		//获取ehs相关设备图片信息
		setEhsToolsMachineryInfo(infoDTO, resultEhsInfo);
	}


	/**
	 * @Description: 新增或修改ehs基本信息和工具信息
	 * @Param: [infoDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/26 13:36
	 **/
	@Transactional(rollbackFor = Exception.class)
	public void insertOrUpdateEhsInfo(EhsInfoDTO infoDTO) {
		//保存ehs基本信息
		if (ObjectUtils.isNotEmpty(infoDTO.getEhsInfoEntity())) {
			boolean saveFlag = this.saveOrUpdate(infoDTO.getEhsInfoEntity());
			if (!saveFlag) {
				throw new BusinessException("agent.ehsSubmission.insertOrUpdateEhsInfo.saveOrUpdateEhsInfo.fail");
			}
		}
		//先删除工具相关设备信息
		if (CollectionUtils.isNotEmpty(infoDTO.getDeleteToolsMachineryEntityList())) {
			List<Long> toolsIdList = infoDTO.getDeleteToolsMachineryEntityList().stream().map(ToolsMachineryEntity::getId).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(toolsIdList)) {
				//批量删除
				this.iToolsMachineryService.removeBatchByIds(infoDTO.getDeleteToolsMachineryEntityList());
			}
		}
		//保存ehs相关工具设备信息
		if (CollectionUtils.isNotEmpty(infoDTO.getAddToolsMachineryEntityList())) {
			if(infoDTO.getEhsInfoEntity().getId() != null) {
				EhsInfoEntity byId = this.getById(infoDTO.getEhsInfoEntity().getId());
				infoDTO.getAddToolsMachineryEntityList().forEach(p -> p.setEhsVersion(byId.getEhsVersion()));
			}
			this.iToolsMachineryService.saveBatch(infoDTO.getAddToolsMachineryEntityList());
		}
	}


	/**
	 * @Description: 查看历史标志
	 * @Param: [infoDTO]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2024/1/19 11:07
	 **/
	private void getGoCheckFlag(EhsInfoDTO infoDTO) {
		//历史标志
		boolean goCheckFlag = false;
		LambdaQueryWrapper<EhsInfoEntity> ehsInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		ehsInfoEntityLambdaQueryWrapper.eq(EhsInfoEntity::getOrderId, infoDTO.getOrderId()).isNotNull(EhsInfoEntity::getEhsVersion);
		List<EhsInfoEntity> ehsInfoEntityList = this.list(ehsInfoEntityLambdaQueryWrapper);
		if (CollectionUtils.isNotEmpty(ehsInfoEntityList)) {
			//查询流程信息
			OrderWorkFlowEntity orderWorkFlowEntity = getFlowInfo(infoDTO);
			//如果当前节点在Ehs，就过滤掉当前的最新版本
			if ("ehsSubmission".equals(orderWorkFlowEntity.getWfCurrentStatus()) && StringUtils.isNotBlank(orderWorkFlowEntity.getEhsVersion())) {
				ehsInfoEntityList = ehsInfoEntityList.stream().filter(re -> !orderWorkFlowEntity.getEhsVersion().equals(re.getEhsVersion())).collect(Collectors.toList());
			}
			//过滤掉集合不为空 代表有历史更改标志
			if (CollectionUtils.isNotEmpty(ehsInfoEntityList)) {
				goCheckFlag = true;
			}
		}
		//goCheck标志
		infoDTO.setGoCheckFlag(goCheckFlag);
	}


	/**
	 * @Description: 获取订单信息
	 * @Param: [infoDTO]
	 * @Return: org.skyworth.ess.createorder.order.entity.OrderEntity
	 * @Author: baixu
	 * @Date: 2023/12/7 18:37
	 **/
	private OrderEntity getOrderInfo(EhsInfoDTO infoDTO) {
		LambdaQueryWrapper<OrderEntity> orderEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderEntityLambdaQueryWrapper.eq(OrderEntity::getId, infoDTO.getOrderId())
			.select(OrderEntity::getOrderNumber, OrderEntity::getRolloutManagerId, OrderEntity::getSiteCountryCode, OrderEntity::getSiteProvinceCode, OrderEntity::getSiteCityCode, OrderEntity::getSiteAddress, OrderEntity::getLongitude, OrderEntity::getLatitude, OrderEntity::getDistanceRestriction);
		return iOrderService.getOne(orderEntityLambdaQueryWrapper);
	}


	/**
	 * @Description: 获取施工日期
	 * @Param: [infoDTO, resultEhsInfo]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/7 18:40
	 **/
	private void setConstructionDate(EhsInfoDTO infoDTO, EhsInfoDTO resultEhsInfo) {
		LambdaQueryWrapper<InstallRelatedInfoEntity> installRelatedInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		installRelatedInfoEntityLambdaQueryWrapper.eq(InstallRelatedInfoEntity::getOrderId, infoDTO.getOrderId());
		installRelatedInfoEntityLambdaQueryWrapper.select(InstallRelatedInfoEntity::getId, InstallRelatedInfoEntity::getConstructionDate);
		InstallRelatedInfoEntity installRelatedInfoEntity = iInstallRelatedInfoService.getOne(installRelatedInfoEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(installRelatedInfoEntity)) {
			return;
		}
		resultEhsInfo.setConstructionDate(DateUtil.formatDateTime(installRelatedInfoEntity.getConstructionDate()));
	}


	/**
	 * @Description: 获取探勘人员
	 * @Param: [infoDTO, resultEhsInfo]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/7 18:43
	 **/
	private void setSurveyPerson(EhsInfoDTO infoDTO, EhsInfoDTO resultEhsInfo) {
		LambdaQueryWrapper<OrderRelatedUserEntity> orderRelatedUserEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		orderRelatedUserEntityLambdaQueryWrapper.eq(OrderRelatedUserEntity::getOrderId, infoDTO.getOrderId()).eq(OrderRelatedUserEntity::getNodeType, "surveyWoAssign").eq(OrderRelatedUserEntity::getUserType, UserTypeEnum.SURVEY_USER.getValue());
		orderRelatedUserEntityLambdaQueryWrapper.select(OrderRelatedUserEntity::getId, OrderRelatedUserEntity::getUserName);
		OrderRelatedUserEntity surveyUserEntity = this.iOrderRelatedUserService.getOne(orderRelatedUserEntityLambdaQueryWrapper);
		if (ObjectUtils.isEmpty(surveyUserEntity)) {
			return;
		}
		resultEhsInfo.setTechnicianName(surveyUserEntity.getUserName());
	}


	private OrderWorkFlowEntity getFlowInfo(EhsInfoDTO infoDTO) {
		LambdaQueryWrapper<OrderWorkFlowEntity> workFlowEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		workFlowEntityLambdaQueryWrapper.eq(OrderWorkFlowEntity::getOrderId, infoDTO.getOrderId());
		return iOrderWorkFlowService.getOne(workFlowEntityLambdaQueryWrapper);
	}


	/**
	 * @Description: 获取ehs基本信息以及图片信息
	 * @Param: [infoDTO, resultEhsInfo]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/7 18:44
	 **/
	private void setEhsBasicInfo(EhsInfoDTO infoDTO, EhsInfoDTO resultEhsInfo) {
		//查询ehs基本信息
		LambdaQueryWrapper<EhsInfoEntity> ehsInfoEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		ehsInfoEntityLambdaQueryWrapper.eq(EhsInfoEntity::getOrderId, infoDTO.getOrderId());
		if (ObjectUtils.isNotEmpty(infoDTO.getEhsVersion())) {
			//取时间版本的数据
			ehsInfoEntityLambdaQueryWrapper.eq(EhsInfoEntity::getEhsVersion, infoDTO.getEhsVersion());
		} else {
			//取时间版本标志为空的
			ehsInfoEntityLambdaQueryWrapper.isNull(EhsInfoEntity::getEhsVersion);
		}
		List<EhsInfoEntity> ehsInfoEntityList = this.list(ehsInfoEntityLambdaQueryWrapper);
		if (CollectionUtils.isEmpty(ehsInfoEntityList)) {
			return;
		}
		//取修改时间最新的一条
		EhsInfoEntity ehsInfoEntity = ehsInfoEntityList.stream().max(Comparator.comparing(EhsInfoEntity::getUpdateTime)).orElse(null);
		if (ObjectUtils.isEmpty(ehsInfoEntity)) {
			return;
		}
		resultEhsInfo.setEhsInfoEntity(ehsInfoEntity);
		AttachmentDescVO attachmentInfo = iAttachmentInfoService.getAttachmentInfo(ehsInfoEntity);
		//图片描述信息
		if (ObjectUtils.isNotEmpty(attachmentInfo)) {
			resultEhsInfo.setEhsBasicAttachmentInfo(attachmentInfo);
		}
	}


	/**
	 * @Description: 获取ehs相关工具设备信息以及图片信息
	 * @Param: [infoDTO, resultEhsInfo]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/7 18:47
	 **/
	private void setEhsToolsMachineryInfo(EhsInfoDTO infoDTO, EhsInfoDTO resultEhsInfo) {
		LambdaQueryWrapper<ToolsMachineryEntity> toolsMachineryEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
		toolsMachineryEntityLambdaQueryWrapper.eq(ToolsMachineryEntity::getOrderId, infoDTO.getOrderId());
		//ehs历史版本标志
		if (ObjectUtils.isNotEmpty(infoDTO.getEhsVersion())) {
			toolsMachineryEntityLambdaQueryWrapper.eq(ToolsMachineryEntity::getEhsVersion, infoDTO.getEhsVersion());
		} else {
			toolsMachineryEntityLambdaQueryWrapper.isNull(ToolsMachineryEntity::getEhsVersion);
		}
		List<ToolsMachineryEntity> toolsMachineryEntityList = this.iToolsMachineryService.list(toolsMachineryEntityLambdaQueryWrapper);
		if (CollectionUtils.isEmpty(toolsMachineryEntityList)) {
			return;
		}
		//工具设备类型字典取值
		setToolsMachineryDictionaryValue(toolsMachineryEntityList);
		//获取所有的businessId
		List<Long> businessIdList = toolsMachineryEntityList.stream().map(ToolsMachineryEntity::getToolsMachineryImgBizKey).filter(Objects::nonNull).distinct().collect(Collectors.toList());
		//获取图片附件信息
		SkyWorthFileEntity skyWorthFileEntity = this.iOrderService.getFileAttachmentInfo(businessIdList);
		Map<Long, List<AttachmentInfoEntity>> attachmentMap = skyWorthFileEntity.getAttachmentMap();
		Map<Long, String> imgDescViewMap = skyWorthFileEntity.getImgDescViewMap();
		for (ToolsMachineryEntity toolsMachineryEntity : toolsMachineryEntityList) {
			//图片附件信息
			if (ObjectUtils.isNotEmpty(attachmentMap) && ObjectUtils.isNotEmpty(attachmentMap.get(toolsMachineryEntity.getToolsMachineryImgBizKey()))) {
				toolsMachineryEntity.setEhsToolsAttachmentList(attachmentMap.get(toolsMachineryEntity.getToolsMachineryImgBizKey()));
			}
			//描述信息
			if (ObjectUtils.isNotEmpty(imgDescViewMap) && ObjectUtils.isNotEmpty(imgDescViewMap.get(toolsMachineryEntity.getToolsMachineryImgBizKey()))) {
				toolsMachineryEntity.setEhsToolsDesc(imgDescViewMap.get(toolsMachineryEntity.getToolsMachineryImgBizKey()));
			}
		}
		//ehs相关工具设备信息
		resultEhsInfo.setAddToolsMachineryEntityList(toolsMachineryEntityList);
	}


	/**
	 * @Description: 工具设备类型 字典取值
	 * @Param: [toolsMachineryEntityList]
	 * @Return: void
	 * @Author: baixu
	 * @Date: 2023/12/12 13:53
	 **/
	private void setToolsMachineryDictionaryValue(List<ToolsMachineryEntity> toolsMachineryEntityList) {
		Map<String, String> agentEhsToolsMachineryType = iOrderService.getDictBizListByName(DictBizCodeEnum.AGENT_EHS_TOOLS_MACHINERY_TYPE.getDictCode());
		if (MapUtil.isNotEmpty(agentEhsToolsMachineryType)) {
			toolsMachineryEntityList.forEach(re -> {
				if (ObjectUtils.isNotEmpty(re.getToolsMachineryType()) && ObjectUtils.isNotEmpty(agentEhsToolsMachineryType.get(re.getToolsMachineryType()))) {
					re.setToolsMachineryTypeName(agentEhsToolsMachineryType.get(re.getToolsMachineryType()));
				}
			});
		}
	}


	@Override
	public IPage<EhsInfoVO> selectInfoPage(IPage<EhsInfoVO> page, EhsInfoVO info) {
		return page.setRecords(baseMapper.selectInfoPage(page, info));
	}


	@Override
	public List<EhsInfoExcel> exportInfo(Wrapper<EhsInfoEntity> queryWrapper) {
		return baseMapper.exportInfo(queryWrapper);
	}


}
