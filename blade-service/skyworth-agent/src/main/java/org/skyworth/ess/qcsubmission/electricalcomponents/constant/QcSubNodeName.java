package org.skyworth.ess.qcsubmission.electricalcomponents.constant;

import lombok.Getter;

/**
 * 子节点名称
 * org.skyworth.ess.qcsubmission.electricalcomponents.constant
 *
 * <AUTHOR>
 * @since 2023/12/25 - 12 - 25
 */
@Getter
public enum QcSubNodeName {
	/**
	 * 逆变器安装
	 */
	INVERTER("inverter", "逆变器安装"),
	/**
	 * 太阳能板
	 */
	SOLAR_PANELS("solarPanels", "太阳能板"),
	/**
	 * 电气组件
	 */
	ELECTRICAL_COMPONENTS("electricalComponents", "电气组件"),
	/**
	 * 接地设备
	 */
	GROUNDING_EQUIPMENT("groundingEquipment", "接地设备"),
	/**
	 * 安装和支架
	 */
	MOUNTING_AND_RACKING("mountingAndRacking", "安装和支架"),
	/**
	 * 安全和合规
	 */
	SAFETY_AND_COMPLIANCE("safetyAndCompliance", "安全和合规"),
	/**
	 * 系统测试
	 */
	SYSTEM_TESTING("systemTesting", "系统测试"),
	/**
	 * 最终检查
	 */
	FINAL_INSPECTION("finalInspection", "最终检查");

	private final String subNodeName;
	private final String remark;

	QcSubNodeName(String subNodeName, String remark) {
		this.subNodeName = subNodeName;
		this.remark = remark;
	}
}
