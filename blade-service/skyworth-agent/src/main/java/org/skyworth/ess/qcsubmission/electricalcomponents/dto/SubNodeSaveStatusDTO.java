package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * qcsubmission小节点名称
 * org.skyworth.ess.qcsubmission.electricalcomponents.dto
 *
 * <AUTHOR>
 * @since 2023/12/22 - 12 - 22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SubNodeSaveStatusDTO {
	private static final long serialVersionUID = 1L;
	/**
	 * 逆变器安装
	 */
	@ApiModelProperty(value = "逆变器安装")
	private String inverter;
	/**
	 * 太阳能板
	 */
	@ApiModelProperty(value = "太阳能板")
	private String solarPanels;
	/**
	 * 电气组件
	 */
	@ApiModelProperty(value = "电气组件")
	private String electricalComponents;
	/**
	 * 接地设备
	 */
	@ApiModelProperty(value = "接地设备")
	private String groundingEquipment;
	/**
	 * 安装和支架
	 */
	@ApiModelProperty(value = "安装和支架")
	private String mountingAndRacking;
	/**
	 * 安全和合规
	 */
	@ApiModelProperty(value = "安全和合规")
	private String safetyAndCompliance;
	/**
	 * 系统测试
	 */
	@ApiModelProperty(value = "系统测试")
	private String systemTesting;
	/**
	 * 最终检查
	 */
	@ApiModelProperty(value = "最终检查")
	private String finalInspection;
}
