/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.service;

import org.skyworth.ess.company.entity.AgentFileInfoEntity;
import org.springblade.core.mp.base.BaseService;

/**
 * 代理商文件管理 服务类
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public interface IAgentFileInfoService extends BaseService<AgentFileInfoEntity> {

	/**
	 * 删除代理商上传文件
	 *
	 * @param agentId 入参
	 * <AUTHOR>
	 * @since 2023/11/27 17:22
	 **/
	void deleteByAgentId(Long agentId);

}
