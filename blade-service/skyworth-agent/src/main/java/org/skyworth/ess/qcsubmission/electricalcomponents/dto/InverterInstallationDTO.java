package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 逆变器安装 视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InverterInstallationDTO extends SkyWorthEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 逆变器类型
	 */
	@ApiModelProperty(value = "逆变器类型")
	private String inverterType;
	/**
	 * 逆变器类型名称
	 */
	@ApiModelProperty(value = "逆变器类型名称")
	private String inverterTypeName;
	/**
	 * 逆变器选择other的描述
	 */
	@ApiModelProperty(value = "逆变器选择other的描述")
	private String inverterTypeOtherRemark;
	/**
	 * 电池数量
	 */
	@ApiModelProperty(value = "电池数量")
	private String numberOfBatteries;
	/**
	 * 电池数量名称
	 */
	@ApiModelProperty(value = "电池数量名称")
	private String numberOfBatteriesName;
	/**
	 * 电池数量选择other的描述
	 */
	@ApiModelProperty(value = "电池数量选择other的描述")
	private String numberOfBatteriesOtherRemark;
	/**
	 * 逆变器规范安装图片业务主键
	 */
	@ApiModelProperty(value = "逆变器规范安装图片业务主键")
	private Long verificationOfSpecificationModelImgBizKey;
	/**
	 * 逆变器序号图片业务主键
	 */
	@ApiModelProperty(value = "逆变器序号图片业务主键")
	private Long inverterSerialNumberImgBizKey;

}
