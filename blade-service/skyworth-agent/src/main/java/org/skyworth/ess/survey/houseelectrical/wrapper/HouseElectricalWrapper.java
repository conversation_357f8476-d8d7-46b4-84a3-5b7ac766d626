/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.houseelectrical.wrapper;

import org.skyworth.ess.survey.houseelectrical.entity.HouseElectricalEntity;
import org.skyworth.ess.survey.houseelectrical.vo.HouseElectricalVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 踏勘房屋电气信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public class HouseElectricalWrapper extends BaseEntityWrapper<HouseElectricalEntity, HouseElectricalVO>  {

	public static HouseElectricalWrapper build() {
		return new HouseElectricalWrapper();
 	}

	@Override
	public HouseElectricalVO entityVO(HouseElectricalEntity houseElectrical) {
		HouseElectricalVO houseElectricalVO = Objects.requireNonNull(BeanUtil.copy(houseElectrical, HouseElectricalVO.class));

		//User createUser = UserCache.getUser(houseElectrical.getCreateUser());
		//User updateUser = UserCache.getUser(houseElectrical.getUpdateUser());
		//houseElectricalVO.setCreateUserName(createUser.getName());
		//houseElectricalVO.setUpdateUserName(updateUser.getName());

		return houseElectricalVO;
	}


}
