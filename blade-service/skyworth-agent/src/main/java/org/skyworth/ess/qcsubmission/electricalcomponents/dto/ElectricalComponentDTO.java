package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.system.entity.SkyWorthFileEntity;

/**
 *  电力视图实体类
 * org.skyworth.ess.qcsubmission.electricalcomponents.vo
 *
 * <AUTHOR>
 * @since 2023/11/29 - 11 - 29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ElectricalComponentDTO extends SkyWorthEntity {
	private static final long serialVersionUID = 1L;
	/**
	 * 电缆布线图片业务主键
	 */
	@ApiModelProperty(value = "电缆布线图片业务主键")
	private Long verifyCableWiringImgBizKey;
	/**
	 * 确认使用管道图片业务主键
	 */
	@ApiModelProperty(value = "确认使用管道图片业务主键")
	private Long confirmUseCondultImgBizKey;
	/**
	 * 密封接头和开口图片业务主键
	 */
	@ApiModelProperty(value = "密封接头和开口图片业务主键")
	private Long allSealedJointsImgBizKey;
	/**
	 * 电器箱图片业务主键
	 */
	@ApiModelProperty(value = "电器箱图片业务主键")
	private Long electricalBoxImgBizKey;
	/**
	 * 接线盒图片业务主键
	 */
	@ApiModelProperty(value = "接线盒图片业务主键")
	private Long junctionBoxInspectionImgBizKey;
	/**
	 * 检查发电机图片业务主键
	 */
	@ApiModelProperty(value = "检查发电机图片业务主键")
	private Long checkGeneratorImgBizKey;
	/**
	 * 检查电池系统图片业务主键
	 */
	@ApiModelProperty(value = "检查电池系统图片业务主键")
	private Long checkBatterySystemImgBizKey;
	/**
	 * 电表或CT图片业务主键
	 */
	@ApiModelProperty(value = "电表或CT图片业务主键")
	private Long currentMeterCtImgBizKey;
}
