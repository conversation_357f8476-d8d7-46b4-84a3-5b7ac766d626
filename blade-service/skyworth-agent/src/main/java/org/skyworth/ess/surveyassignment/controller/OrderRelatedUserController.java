/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.surveyassignment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.createorder.order.dto.OrderDTO;
import org.skyworth.ess.survey.info.entity.SurveyInfoEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.dto.OrderRelatedUserDTO;
import org.skyworth.ess.surveyassignment.orderrelateduser.entity.OrderRelatedUserEntity;
import org.skyworth.ess.surveyassignment.orderrelateduser.excel.OrderRelatedUserExcel;
import org.skyworth.ess.surveyassignment.orderrelateduser.service.IOrderRelatedUserService;
import org.skyworth.ess.surveyassignment.orderrelateduser.vo.OrderRelatedUserVO;
import org.skyworth.ess.surveyassignment.orderrelateduser.wrapper.OrderRelatedUserWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 订单关系人 控制器
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-orderRelatedUser/orderRelatedUser")
@Api(value = "订单关系人", tags = "订单关系人接口")
public class OrderRelatedUserController extends BladeController {

	private final IOrderRelatedUserService orderRelatedUserService;

	/**
	 * 订单关系人 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入orderRelatedUser")
	public R<OrderRelatedUserVO> detail(OrderRelatedUserEntity orderRelatedUser) {
		OrderRelatedUserEntity detail = orderRelatedUserService.getOne(Condition.getQueryWrapper(orderRelatedUser));
		return R.data(OrderRelatedUserWrapper.build().entityVO(detail));
	}

	/**
	 * 订单关系人 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入orderRelatedUser")
	public R<IPage<OrderRelatedUserVO>> list(@ApiIgnore @RequestParam Map<String, Object> orderRelatedUser, Query query) {
		IPage<OrderRelatedUserEntity> pages = orderRelatedUserService.page(Condition.getPage(query), Condition.getQueryWrapper(orderRelatedUser, OrderRelatedUserEntity.class));
		return R.data(OrderRelatedUserWrapper.build().pageVO(pages));
	}

	/**
	 * 订单关系人 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入orderRelatedUser")
	public R<IPage<OrderRelatedUserVO>> page(OrderRelatedUserVO orderRelatedUser, Query query) {
		IPage<OrderRelatedUserVO> pages = orderRelatedUserService.selectOrderRelatedUserPage(Condition.getPage(query), orderRelatedUser);
		return R.data(pages);
	}

	/**
	 * 订单关系人 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入orderRelatedUser")
	public R save(@Valid @RequestBody OrderRelatedUserEntity orderRelatedUser) {
		return R.status(orderRelatedUserService.save(orderRelatedUser));
	}

	/**
	 * 订单关系人 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入orderRelatedUser")
	public R update(@Valid @RequestBody OrderRelatedUserEntity orderRelatedUser) {
		return R.status(orderRelatedUserService.updateById(orderRelatedUser));
	}

	/**
	 * 订单关系人 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入orderRelatedUser")
	public R submit(@Valid @RequestBody OrderRelatedUserEntity orderRelatedUser) {
		return R.status(orderRelatedUserService.saveOrUpdate(orderRelatedUser));
	}

	/**
	 * 订单关系人 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(orderRelatedUserService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 查询订单探勘时间
	 */
	@PostMapping("/selectExplorationDate")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "查询订单探勘时间", notes = "传入orderDTO")
	public R<?> selectExplorationDate(@RequestBody OrderDTO orderDTO) {
		return orderRelatedUserService.selectExplorationDate(orderDTO);
	}


	/**
	 * 指定踏勘人员和电工
	 */
	@PostMapping("/designatedPerson")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "指定踏勘人员和电工", notes = "传入orderRelatedUserDTO")
	public R<?> designatedPerson(@Valid @RequestBody OrderRelatedUserDTO orderRelatedUserDTO) {
		return orderRelatedUserService.designatedPerson(orderRelatedUserDTO);
	}


	/**
	 * 查询该订单的踏勘人员和电工
	 */
	@PostMapping("/selectDesignatedPerson")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "查询该订单的踏勘人员和电工", notes = "传入infoEntity")
	public R<?> selectDesignatedPerson(@RequestBody SurveyInfoEntity infoEntity) {
		return orderRelatedUserService.selectDesignatedPerson(infoEntity);
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-orderRelatedUser")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "导出数据", notes = "传入orderRelatedUser")
	public void exportOrderRelatedUser(@ApiIgnore @RequestParam Map<String, Object> orderRelatedUser, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<OrderRelatedUserEntity> queryWrapper = Condition.getQueryWrapper(orderRelatedUser, OrderRelatedUserEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(OrderRelatedUser::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(OrderRelatedUserEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<OrderRelatedUserExcel> list = orderRelatedUserService.exportOrderRelatedUser(queryWrapper);
		ExcelUtil.export(response, "订单关系人数据" + DateUtil.time(), "订单关系人数据表", list, OrderRelatedUserExcel.class);
	}

}
