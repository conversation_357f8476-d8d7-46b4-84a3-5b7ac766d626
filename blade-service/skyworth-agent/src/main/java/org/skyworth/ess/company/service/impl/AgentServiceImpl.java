/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.skyworth.ess.company.entity.AgentCompanyInfoEntity;
import org.skyworth.ess.company.entity.AgentEntity;
import org.skyworth.ess.company.entity.AgentFileInfoEntity;
import org.skyworth.ess.company.mapper.AgentMapper;
import org.skyworth.ess.company.service.IAgentCompanyInfoService;
import org.skyworth.ess.company.service.IAgentFileInfoService;
import org.skyworth.ess.company.service.IAgentService;
import org.skyworth.ess.company.service.IAgentUserInfoService;
import org.skyworth.ess.company.vo.AgentListVO;
import org.skyworth.ess.constant.AgentUserTypeEnum;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.skyworth.ess.plant.feign.IPlantClient;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RoleCodeEnum;
import org.springblade.common.vo.UserBatchVO;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.entity.*;
import org.springblade.system.feign.IAttachmentInfoClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.feign.IUserClient;
import org.springblade.system.feign.IUserSearchClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 代理商公司信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@Service
@AllArgsConstructor
public class AgentServiceImpl extends BaseServiceImpl<AgentMapper, AgentEntity>
	implements IAgentService {
	private IAttachmentInfoClient attachmentInfoClient;
	private ISysClient sysClient;
	private IUserSearchClient userSearchClient;
	private IAgentCompanyInfoService agentCompanyInfoService;
	private IAgentFileInfoService agentFileInfoService;
	private IAgentUserInfoService agentUserInfoService;
	private IUserClient userClient;
	private IPlantClient plantClient;

	@Override
	public IPage<AgentListVO> getAgentList(IPage<AgentListVO> page, Integer agentArea, String companyName) {
		IPage<AgentListVO> pageResult;
		BladeUser user = AuthUtil.getUser();
		String deptId = "";
		if (user.getDetail() != null) {
			Boolean roleInnerFlag = (Boolean) user.getDetail().get(CommonConstant.USER_ROLE_INNER_FLAG);
			// 如果包含创维内部角色，则可查看所有订单
			if (roleInnerFlag != null && !roleInnerFlag) {
				deptId = user.getDeptId();
			}
		}
		pageResult = baseMapper.getAgentList(page, companyName, agentArea, deptId);
		// 查询区域信息
		queryAreaInfo(pageResult);
		return pageResult;
	}

	/**
	 * 远程接口查询区域名称
	 *
	 * @param pageResult 入参
	 * <AUTHOR>
	 * @since 2023/11/28 13:53
	 **/
	private void queryAreaInfo(IPage<AgentListVO> pageResult) {
		List<AgentListVO> agentListVOList = pageResult.getRecords();
		List<String> areaCodes = new ArrayList<>();
		// 远程查询区域信息
		Optional.ofNullable(agentListVOList).orElse(new ArrayList<>()).stream().filter(a -> StringUtils.isNotEmpty(a.getAgentArea())).forEach(a -> {
			StringBuilder areaBuilder = new StringBuilder();
			String[][] agentAreaArray = JSON.parseObject(a.getAgentArea(), String[][].class);
			for (String[] strings : agentAreaArray) {
				areaBuilder.append(strings[2]).append(",");
				areaCodes.add(strings[2]);
			}
			a.setAgentArea(areaBuilder.deleteCharAt(areaBuilder.lastIndexOf(",")).toString());
		});
		if (CollectionUtils.isEmpty(areaCodes)) {
			return;
		}
		List<Region> regions = sysClient.getRegionList(areaCodes).getData();
		Map<String, Region> regionMap = Optional.ofNullable(regions).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(Region::getCode, Function.identity(), (a, b) -> a));
		Optional.ofNullable(agentListVOList).orElse(new ArrayList<>()).forEach(a -> {
			String agentAreaCode = a.getAgentArea();
			if (StringUtils.isNotEmpty(agentAreaCode)) {
				StringBuilder agentAreaName = new StringBuilder();
				for (String code : agentAreaCode.split(",")) {
					if (regionMap.containsKey(code)) {
						Region region = regionMap.get(code);
						agentAreaName.append(region.getName()).append(",");
					}
				}
				a.setAgentArea(agentAreaName.deleteCharAt(agentAreaName.lastIndexOf(",")).toString());
			}
		});
	}

	/**
	 * 新增供应商
	 *
	 * @param agentEntity 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/11/9 18:33
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveAgent(AgentEntity agentEntity) {
		AgentCompanyInfoEntity agentCompanyInfo = agentEntity.getCompanyInfo();
		// 判断是否代理商名称重复
		validCompanyNameRepeat(agentCompanyInfo, true);
		// 生成代理商编号
		String agentNumber = sysClient.getRedisUniqueId(RedisSeqEnums.AGENT_NO_SEQ).getData();
		agentCompanyInfo.setAgentNumber(agentNumber);
		// 新增部门信息
		addOrUpdateDept(agentEntity, true);
		// 保存代理商公司信息
		agentCompanyInfoService.save(agentCompanyInfo);
		// 保存代理商其他信息：附件+施工人员+电工
		saveAndUpdateOtherInfo(agentEntity, true, agentCompanyInfo.getId());
		return true;
	}

	/**
	 * 保存代理商其他信息：附件+施工人员+电工
	 *
	 * @param agentEntity 入参
	 * <AUTHOR>
	 * @since 2023/11/27 16:36
	 **/
	private void saveAndUpdateOtherInfo(AgentEntity agentEntity, boolean isCreate, Long agentId) {
		saveAgentFiles(agentEntity, isCreate, agentId);
		saveAgentUsers(agentEntity, isCreate, agentId);
	}

	private void saveAgentFiles(AgentEntity agentEntity, boolean isCreate, Long agentId) {
		// 保存代理商附件信息
		List<AgentFileInfoEntity> agentFileInfoEntityList = agentEntity.getFileInfoEntityList();
		if (CollectionUtils.isNotEmpty(agentFileInfoEntityList)) {
			if (!isCreate) {
				agentFileInfoService.deleteByAgentId(agentId);
			}
			// 设置代理商id
			agentFileInfoEntityList.forEach(a -> {
				a.setAgentId(agentId);
				a.setId(null);
			});
			agentFileInfoService.saveBatch(agentFileInfoEntityList);
		} else {
			if (!isCreate) {
				agentFileInfoService.deleteByAgentId(agentId);
			}
		}
	}

	private void saveAgentUsers(AgentEntity agentEntity, boolean isCreate, Long agentId) {
		// 保存代理商施工人员信息+保存代理商
		List<AgentUserInfoEntity> constructorList = agentEntity.getConstructorList();
		List<AgentUserInfoEntity> electricianList = agentEntity.getElectricianList();
		List<AgentUserInfoEntity> rolloutManagerDistributorList = agentEntity.getRolloutManagerDistributorList();
		List<AgentUserInfoEntity> allUserInfoList = new ArrayList<>();
		// 查询代理商下所有管理人员
		// 同步用户信息到client
		synchronizeUserInfo(agentEntity, agentId, constructorList, electricianList, rolloutManagerDistributorList);
		// 施工人员
		if (CollectionUtils.isNotEmpty(constructorList)) {
			constructorList.forEach(a -> {
				a.setUserType(AgentUserTypeEnum.CONSTRUCTOR.getCode());
				a.setAgentId(agentId);
				a.setId(null);
			});
			allUserInfoList.addAll(constructorList);
		}
		// 电工
		if (CollectionUtils.isNotEmpty(electricianList)) {
			electricianList.forEach(a -> {
				a.setUserType(AgentUserTypeEnum.ELECTRICIAN.getCode());
				a.setAgentId(agentId);
				a.setId(null);
			});
			allUserInfoList.addAll(electricianList);
		}
		// 交付经理
		if (CollectionUtils.isNotEmpty(rolloutManagerDistributorList)) {
			rolloutManagerDistributorList.forEach(a -> {
				a.setUserType(AgentUserTypeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getCode());
				a.setAgentId(agentId);
				a.setId(null);
			});
			allUserInfoList.addAll(rolloutManagerDistributorList);
		}
		// 施工人员+电工数据+交付经理数据保存
		if (!isCreate) {
			agentUserInfoService.deleteByAgentId(agentId);
		}
		if (CollectionUtils.isNotEmpty(allUserInfoList)) {
			agentUserInfoService.saveBatch(allUserInfoList);
		}
	}

	/**
	 * 同步用户信息到client
	 *
	 * @param agentEntity                   代理商信息
	 * @param agentId                       代理商id
	 * @param constructorList               施工人员
	 * @param electricianList               电气工程师
	 * @param rolloutManagerDistributorList 代理商交付经理
	 * <AUTHOR>
	 * @since 2024/3/11 17:02
	 **/
	private void synchronizeUserInfo(AgentEntity agentEntity, Long agentId, List<AgentUserInfoEntity> constructorList, List<AgentUserInfoEntity> electricianList, List<AgentUserInfoEntity> rolloutManagerDistributorList) {
		// 获取修改前的用户列表
		List<AgentUserInfoEntity> userModifyBefore = agentUserInfoService.list(Wrappers.<AgentUserInfoEntity>lambdaQuery().eq(AgentUserInfoEntity::getAgentId, agentId));
		// 获取修改后当前用户在代理商下的角色数量
		List<AgentUserInfoEntity> allUserList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(constructorList)) {
			allUserList.addAll(constructorList);
		}
		if (CollectionUtils.isNotEmpty(electricianList)) {
			allUserList.addAll(electricianList);
		}
		if (CollectionUtils.isNotEmpty(rolloutManagerDistributorList)) {
			allUserList.addAll(rolloutManagerDistributorList);
		}
		// 获取修改后用户在当前代理商下的用户id
		Set<Long> afterUserRoleCountMap = allUserList.stream().map(AgentUserInfoEntity::getUserId)
			.collect(Collectors.toSet());
		// 新增的用户集合
		Set<Long> afterUserIdAddList = allUserList.stream().filter(a -> a.getId() == null).map(AgentUserInfoEntity::getUserId).collect(Collectors.toSet());
		// 获取删除的用户
		Set<Long> deleteUserIdList = userModifyBefore.stream().map(AgentUserInfoEntity::getUserId).filter(userId -> !afterUserRoleCountMap.contains(userId)).collect(Collectors.toSet());
		// 获取修改前的交付经理
		List<Long> rolloutManagerDistributorBefore = Optional.of(userModifyBefore).orElse(new ArrayList<>()).stream().filter(a -> (AgentUserTypeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getCode()).equalsIgnoreCase(a.getUserType())).map(AgentUserInfoEntity::getUserId).collect(Collectors.toList());
		// 修改后的交付经理
		List<Long> rolloutManagerDistributorAfter = Optional.ofNullable(rolloutManagerDistributorList).orElse(new ArrayList<>()).stream().map(AgentUserInfoEntity::getUserId).collect(Collectors.toList());
		// 获取删除的交付经理记录
		Set<Long> deleteRolloutManagerList = Optional.of(rolloutManagerDistributorBefore).orElse(new ArrayList<>()).stream().filter(a -> !rolloutManagerDistributorAfter.contains(a)).collect(Collectors.toSet());
		// 封装删除用户
		List<User> addUserList = new ArrayList<>();
		List<User> deleteUserList = new ArrayList<>();
		Long deptId = agentEntity.getCompanyInfo().getDeptId();
		deleteUserIdList.forEach(a -> {
			User user = new User();
			user.setId(a);
			user.setDeptId(deptId.toString());
			if (deleteRolloutManagerList.contains(a)) {
				user.setRoleCode(RoleCodeEnum.CLIENT_ROLLOUT_MANAGER_DISTRIBUTOR.getRoleCode());
			}
			deleteUserList.add(user);
		});
		afterUserIdAddList.forEach(a -> {
			User user = new User();
			user.setId(a);
			user.setDeptId(deptId.toString());
			addUserList.add(user);
		});
		// 删除或者新增用户
		if (CollectionUtils.isNotEmpty(addUserList) || CollectionUtils.isNotEmpty(deleteUserList)) {
			// 封装批量操作对象
			UserBatchVO<User> userUserBatchVO = new UserBatchVO<>();
			userUserBatchVO.setAddList(addUserList);
			userUserBatchVO.setDeleteList(deleteUserList);
			// 同步人员到client
			userClient.synchronousUserList(userUserBatchVO);
		}
		// 删除人员的时候，同步删除站点维护的运维人员
		if (CollectionUtils.isNotEmpty(deleteUserIdList)) {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("deptIds", deptId);
			jsonObject.put("sourceUserIds", deleteUserIdList.stream()
				.map(String::valueOf)
				.collect(Collectors.joining(",")));
			jsonObject.put("type", BizConstant.NUMBER_ZERO);
			plantClient.cleanPlantDeptIdOrUsers(jsonObject);
		}
	}


	/**
	 * 填充用户信息
	 *
	 * @param userIdList  用户ids
	 * @param map         user信息
	 * @param roleMap     角色MAP
	 * @param deptId      部门id
	 * @param addUserList 新增用户对象
	 * @param roleCode    角色编码
	 * <AUTHOR>
	 * @since 2024/3/11 17:04
	 **/
	private void fillUserInfo(List<Long> userIdList, Map<Long, User> map, Map<String, Long> roleMap, Long deptId, List<User> addUserList, String roleCode) {
		userIdList.forEach(a -> {
			User user = map.get(a);
			user.setRoleId(roleMap.get(roleCode).toString());
			user.setRoleCode(roleCode);
			user.setDeptId(deptId.toString());
			addUserList.add(user);
		});
	}

	/**
	 * 验证代理商名称是否重复
	 *
	 * @param agentCompanyInfo 代理商公司信息
	 * @param isCreate         入参true/false：新增/修改
	 * <AUTHOR>
	 * @since 2023/11/14 11:33
	 **/
	private void validCompanyNameRepeat(AgentCompanyInfoEntity agentCompanyInfo, boolean isCreate) {
		Wrapper<AgentCompanyInfoEntity> wrapper;
		String companyName = agentCompanyInfo.getCompanyName();
		Long id = agentCompanyInfo.getId();
		if (isCreate) {
			wrapper = Wrappers.<AgentCompanyInfoEntity>lambdaQuery().eq(AgentCompanyInfoEntity::getCompanyName, companyName);
		} else {
			wrapper = Wrappers.<AgentCompanyInfoEntity>lambdaQuery().eq(AgentCompanyInfoEntity::getCompanyName, companyName).ne(
				AgentCompanyInfoEntity::getId, id);
		}
		long count = agentCompanyInfoService.count(wrapper);
		if (count != 0) {
			throw new BusinessException("agent.company.name.repeat");
		}
	}

	/**
	 * 生成部门信息
	 *
	 * @param agentEntity 入参
	 * <AUTHOR>
	 * @since 2023/11/10 15:31
	 **/
	private void addOrUpdateDept(AgentEntity agentEntity, boolean isCreate) {
		// 生成deptId
		Dept dept = new Dept();
		AgentCompanyInfoEntity afterCompanyInfo = agentEntity.getCompanyInfo();
		String afterCompanyName = afterCompanyInfo.getCompanyName();
		if (isCreate) {
			Long deptId = IdWorker.getId();
			dept.setId(deptId);
			dept.setParentId(0L);
			dept.setAncestors(BizConstant.CHAR_ZERO);
			dept.setSort(100);
			dept.setDeptCategory(BizConstant.NUMBER_ONE);
			dept.setTenantId(CommonConstant.AGENT_TENANT_ID);
			// 设置部门id
			afterCompanyInfo.setDeptId(deptId);
			dept.setDeptName(afterCompanyInfo.getCompanyName());
			dept.setFullName(afterCompanyInfo.getCompanyName());
			sysClient.submit(dept);
		} else {
			// 判断公司名称是否发生变化
			dept.setId(afterCompanyInfo.getDeptId());
			AgentCompanyInfoEntity beforeCompanyInfo = agentCompanyInfoService.getById(afterCompanyInfo.getId());
			if (!beforeCompanyInfo.getCompanyName().equals(afterCompanyName)) {
				dept.setDeptName(afterCompanyName);
				dept.setFullName(afterCompanyName);
				sysClient.submit(dept);
			}
		}
	}

	/**
	 * 修改供应商
	 *
	 * @param agentEntity 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/11/9 18:33
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean update(AgentEntity agentEntity) {
		AgentCompanyInfoEntity agentCompanyInfo = agentEntity.getCompanyInfo();
		// 修改部门信息
		addOrUpdateDept(agentEntity, false);
		// 判断是否代理商名称重复
		validCompanyNameRepeat(agentCompanyInfo, false);
		// 数据修改
		agentCompanyInfoService.updateById(agentCompanyInfo);
		// 修改施工人员+附件+电工
		saveAndUpdateOtherInfo(agentEntity, false, agentCompanyInfo.getId());
		return true;
	}

	/**
	 * 删除供应商
	 *
	 * @param ids 入参
	 * @return Boolean
	 * <AUTHOR>
	 * @since 2023/11/9 18:33
	 **/
	@Override
	public Boolean deleteBatch(List<Long> ids) {
		// 验证订单是否在途
		String agentNames = agentCompanyInfoService.validAgentHasInTransitOrders(ids);
		if (StringUtils.isNotEmpty(agentNames)) {
			throw new BusinessException("agent.company.has.intransitive.order", agentNames);
		}
		// 查询部门信息
		List<AgentCompanyInfoEntity> agentCompanyInfoEntityList = agentCompanyInfoService.list(Wrappers.<AgentCompanyInfoEntity>lambdaQuery().in(AgentCompanyInfoEntity::getId, ids));
		// 查询删除人员映射
		List<AgentUserInfoEntity> allUserList = agentUserInfoService.queryAgentUserList(ids);
		// 删除部门+用户+附件信息
		agentCompanyInfoService.deleteBatch(ids);
		String deptIds = agentCompanyInfoEntityList.stream().map(a -> Func.toStr(a.getDeptId())).filter(Objects::nonNull).collect(Collectors.joining(","));
		sysClient.removeDept(deptIds);
		// 处理人员和代理商之间的角色关系
		if (CollectionUtils.isNotEmpty(allUserList)) {
			// 获取删除人员id和部门信息
			Set<String> deleteUserSet = allUserList.stream().map(a -> a.getUserId() + "@" + a.getDeptId()).collect(Collectors.toSet());
			// 获取创维交付经理的人员id和部门信息
			Set<String> deleteRolloutManagerSet = allUserList.stream().filter(a -> AgentUserTypeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getCode().equals(a.getUserType())).map(a -> a.getUserId() + "@" + a.getDeptId()).collect(Collectors.toSet());
			// 封装删除对象
			List<User> deleteUserList = new ArrayList<>();
			deleteUserSet.forEach(a -> {
				String[] value = a.split("@");
				User user = new User();
				user.setId(Long.valueOf(value[0]));
				user.setDeptId(value[1]);
				if (deleteRolloutManagerSet.contains(a)) {
					user.setRoleCode(AgentUserTypeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getCode());
				}
				deleteUserList.add(user);
			});
			// 推送删除记录
			if (CollectionUtils.isNotEmpty(deleteUserList)) {
				UserBatchVO<User> userBatchVO = new UserBatchVO<>();
				userBatchVO.setDeleteList(deleteUserList);
				userClient.synchronousUserList(userBatchVO);
				// 同步清理站点的代理商和运维人员
				JSONObject jsonObject = new JSONObject();
				jsonObject.put("deptIds", deptIds);
				jsonObject.put("type", BizConstant.NUMBER_ONE);
				plantClient.cleanPlantDeptIdOrUsers(jsonObject);
			}
		}
		return true;
	}


	@NotNull
	private List<Long> filterUserByType(List<AgentUserInfoEntity> userList, AgentUserTypeEnum constructor) {
		return Optional.ofNullable(userList).orElse(new ArrayList<>()).stream().filter(a -> (constructor.getCode()).equalsIgnoreCase(a.getUserType())).map(AgentUserInfoEntity::getUserId).collect(Collectors.toList());
	}

	/**
	 * 查询详情
	 *
	 * @param id 入参
	 * @return AgentEntity
	 * <AUTHOR>
	 * @since 2023/11/10 14:54
	 **/
	@Override
	public AgentEntity detail(Long id) {
		AgentEntity agentEntity = new AgentEntity();
		AgentCompanyInfoEntity agentCompanyInfo = agentCompanyInfoService.getById(id);
		List<AgentFileInfoEntity> fileInfoEntityList = agentFileInfoService.list(Wrappers.<AgentFileInfoEntity>lambdaQuery().eq(AgentFileInfoEntity::getAgentId, id).eq(AgentFileInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		List<AgentUserInfoEntity> userInfoEntityList = agentUserInfoService.list(Wrappers.<AgentUserInfoEntity>lambdaQuery().eq(AgentUserInfoEntity::getAgentId, id).eq(AgentUserInfoEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED));
		agentEntity.setCompanyInfo(agentCompanyInfo);
		// 设置附件
		agentEntity.setFileInfoEntityList(fileInfoEntityList);
		agentEntity.setAttachmentMap(queryAttachment(fileInfoEntityList, userInfoEntityList));
		// 查询代理商人员姓名
		queryAgentUserInfo(userInfoEntityList);
		agentEntity.setConstructorList(userInfoEntityList.stream().filter(a -> a.getUserType().equalsIgnoreCase(AgentUserTypeEnum.CONSTRUCTOR.getCode())).collect(Collectors.toList()));
		agentEntity.setElectricianList(userInfoEntityList.stream().filter(a -> a.getUserType().equalsIgnoreCase(AgentUserTypeEnum.ELECTRICIAN.getCode())).collect(Collectors.toList()));
		agentEntity.setRolloutManagerDistributorList(userInfoEntityList.stream().filter(a -> a.getUserType().equalsIgnoreCase(AgentUserTypeEnum.ROLLOUT_MANAGER_DISTRIBUTOR.getCode())).collect(Collectors.toList()));
		// 设置用户信息
		return agentEntity;
	}

	/**
	 * 查询用户名称
	 *
	 * @param userInfoEntityList 入参
	 * <AUTHOR>
	 * @since 2023/11/28 13:48
	 **/
	private void queryAgentUserInfo(List<AgentUserInfoEntity> userInfoEntityList) {
		if (CollectionUtils.isEmpty(userInfoEntityList)) {
			return;
		}
		List<Long> userIds = userInfoEntityList.stream().map(AgentUserInfoEntity::getUserId).collect(Collectors.toList());
		List<User> userList = userSearchClient.listByUserIds(userIds).getData();
		if (CollectionUtils.isEmpty(userList)) {
			return;
		}
		Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
		userInfoEntityList.forEach(a -> {
			if (userMap.containsKey(a.getUserId())) {
				User user = userMap.get(a.getUserId());
				a.setUserName(user.getRealName());
				a.setPhone(user.getPhone());
				a.setEmail(user.getEmail());
			}
		});
	}

	/**
	 * 查询代理商信息
	 *
	 * @param fileInfoEntityList 文件信息
	 * @return Map<Long, List < AttachmentInfoEntity>>
	 * <AUTHOR>
	 * @since 2023/11/10 15:27
	 **/
	private Map<Long, List<AttachmentInfoEntity>> queryAttachment(List<AgentFileInfoEntity> fileInfoEntityList, List<AgentUserInfoEntity> agentUserInfoEntityList) {
		List<Long> businessIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(fileInfoEntityList)) {
			businessIds.addAll(fileInfoEntityList.stream().map(AgentFileInfoEntity::getFileDocBizKey).filter(Objects::nonNull).collect(Collectors.toList()));
		}
		if (CollectionUtils.isNotEmpty(agentUserInfoEntityList)) {
			businessIds.addAll(agentUserInfoEntityList.stream().map(AgentUserInfoEntity::getUserImgBizKey).filter(Objects::nonNull).collect(Collectors.toList()));
		}
		if (!CollectionUtils.isEmpty(businessIds)) {
			return attachmentInfoClient.findByBusinessIds(businessIds).getData();
		}
		return new HashMap<>(BizConstant.NUMBER_ZERO);
	}
}
