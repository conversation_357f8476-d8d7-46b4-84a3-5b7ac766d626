/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.createorder.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.createorder.order.excel.OrderExcel;
import org.skyworth.ess.createorder.order.service.IOrderService;
import org.skyworth.ess.createorder.order.vo.OrderVO;
import org.skyworth.ess.createorder.order.wrapper.OrderWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 订单表 控制器
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-order/order")
@Api(value = "订单表", tags = "订单表接口")
public class OrderController extends BladeController {
	private final IOrderService orderService;

	/**
	 * 订单表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入order")
	@PreAuth("hasPermission('agent:blade-order:order:detail')")
	public R<OrderVO> detail(OrderEntity order) {
		OrderEntity detail = orderService.getOne(Condition.getQueryWrapper(order));
		return R.data(OrderWrapper.build().entityVO(detail));
	}

	/**
	 * 订单表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入order")
	@PreAuth("hasPermission('agent:blade-order:order:list')")
	public R<IPage<OrderVO>> list(@ApiIgnore @RequestParam Map<String, Object> order, Query query) {
		IPage<OrderEntity> pages = orderService.page(Condition.getPage(query), Condition.getQueryWrapper(order, OrderEntity.class));
		return R.data(OrderWrapper.build().pageVO(pages));
	}

	/**
	 * 订单表 自定义分页
	 */
	@PostMapping("/page/{size}/{current}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入order")
	@PreAuth("hasPermission('agent:blade-order:order:page')")
	public R<IPage<OrderVO>> page(@RequestBody OrderVO order, Query query) {
		IPage<OrderVO> pages = orderService.selectOrderPage(Condition.getPage(query), order);
		return R.data(pages);
	}

	/**
	 * 订单表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入order")
	@PreAuth("hasPermission('agent:blade-order:order:save')")
	public R<Boolean> save(@Valid @RequestBody OrderEntity order) {
		return R.status(orderService.save(order));
	}

	/**
	 * 订单表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入order")
	@PreAuth("hasPermission('agent:blade-order:order:update')")
	public R<Boolean> update(@Valid @RequestBody OrderEntity order) {
		return R.status(orderService.updateById(order));
	}

	/**
	 * 订单表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入order")
	@PreAuth("hasPermission('agent:blade-order:order:submit')")
	public R<Boolean> submit(@Valid @RequestBody OrderEntity order) {
		return R.status(orderService.saveOrUpdate(order));
	}

	/**
	 * 订单表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth("hasPermission('agent:blade-order:order:remove')")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(orderService.deleteLogic(Func.toLongList(ids)));
	}





	/**
	 * 导出数据
	 */
	@GetMapping("/export-order")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "导出数据", notes = "传入order")
	@PreAuth("hasPermission('agent:blade-order:order:export-order')")
	public void exportOrder(@ApiIgnore @RequestParam Map<String, Object> order, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<OrderEntity> queryWrapper = Condition.getQueryWrapper(order, OrderEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Order::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(OrderEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<OrderExcel> list = orderService.exportOrder(queryWrapper);
		ExcelUtil.export(response, "订单表数据" + DateUtil.time(), "订单表数据表", list, OrderExcel.class);
	}

}
