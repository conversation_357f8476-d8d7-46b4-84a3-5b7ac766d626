/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.housestructure.wrapper;

import org.skyworth.ess.survey.housestructure.entity.HouseStructureEntity;
import org.skyworth.ess.survey.housestructure.vo.HouseStructureVO;
import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;

import java.util.Objects;

/**
 * 踏勘房屋结构信息 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public class HouseStructureWrapper extends BaseEntityWrapper<HouseStructureEntity, HouseStructureVO>  {

	public static HouseStructureWrapper build() {
		return new HouseStructureWrapper();
 	}

	@Override
	public HouseStructureVO entityVO(HouseStructureEntity houseStructure) {
		HouseStructureVO houseStructureVO = Objects.requireNonNull(BeanUtil.copy(houseStructure, HouseStructureVO.class));

		//User createUser = UserCache.getUser(houseStructure.getCreateUser());
		//User updateUser = UserCache.getUser(houseStructure.getUpdateUser());
		//houseStructureVO.setCreateUserName(createUser.getName());
		//houseStructureVO.setUpdateUserName(updateUser.getName());

		return houseStructureVO;
	}


}
