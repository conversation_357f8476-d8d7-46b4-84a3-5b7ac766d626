/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.qcotherInformation.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 施工-其他信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class QcOtherInformationExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单id")
	private Long orderId;
	/**
	 * 业主签名图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("业主签名图片业务主键")
	private Long lvLandlordVerificationImgBizKey;
	/**
	 * 接地设备图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("接地设备图片业务主键")
	private Long geGroundingEquimentImgBizKey;
	/**
	 * 接地连续性图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("接地连续性图片业务主键")
	private Long geGroundContinuityImgBizKey;
	/**
	 * 警告标识图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("警告标识图片业务主键")
	private Long scWarningSignsImgBizKey;
	/**
	 * 符合当地电气规范标准图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("符合当地电气规范标准图片业务主键")
	private Long scElectricalStandardsImgBiz;
	/**
	 * 逆变器状态图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("逆变器状态图片业务主键")
	private Long stConfirmInverterStatusImgBizKey;
	/**
	 * 确认电压，电流，功率图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("确认电压，电流，功率图片业务主键")
	private Long stVerifyPowerOutImgBizKey;
	/**
	 * 调试测试图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("调试测试图片业务主键")
	private Long stPerformTestImgBizKey;
	/**
	 * 安装完成图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("安装完成图片业务主键")
	private Long fiCompletedInstallationImgBizKey;
	/**
	 * 环境清洁图片业务主键
	 */
	@ColumnWidth(20)
	@ExcelProperty("环境清洁图片业务主键")
	private Long fiEnvironCleanImgBizKey;
	/**
	 * 建议
	 */
	@ColumnWidth(20)
	@ExcelProperty("建议")
	private String fiRecommendations;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;
	/**
	 * 租户id
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户id")
	private String tenantId;

}
