/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.skyworth.ess.survey.houseelectrical.entity.HouseElectricalEntity;
import org.skyworth.ess.survey.houseelectrical.excel.HouseElectricalExcel;
import org.skyworth.ess.survey.houseelectrical.service.IHouseElectricalService;
import org.skyworth.ess.survey.houseelectrical.vo.HouseElectricalVO;
import org.skyworth.ess.survey.houseelectrical.wrapper.HouseElectricalWrapper;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 踏勘房屋电气信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("blade-houseElectrical/houseElectrical")
@Api(value = "踏勘房屋电气信息", tags = "踏勘房屋电气信息接口")
public class HouseElectricalController extends BladeController {

	private final IHouseElectricalService houseElectricalService;

	/**
	 * 踏勘房屋电气信息 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入houseElectrical")
	public R<HouseElectricalVO> detail(HouseElectricalEntity houseElectrical) {
		HouseElectricalEntity detail = houseElectricalService.getOne(Condition.getQueryWrapper(houseElectrical));
		return R.data(HouseElectricalWrapper.build().entityVO(detail));
	}
	/**
	 * 踏勘房屋电气信息 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入houseElectrical")
	public R<IPage<HouseElectricalVO>> list(@ApiIgnore @RequestParam Map<String, Object> houseElectrical, Query query) {
		IPage<HouseElectricalEntity> pages = houseElectricalService.page(Condition.getPage(query), Condition.getQueryWrapper(houseElectrical, HouseElectricalEntity.class));
		return R.data(HouseElectricalWrapper.build().pageVO(pages));
	}

	/**
	 * 踏勘房屋电气信息 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入houseElectrical")
	public R<IPage<HouseElectricalVO>> page(HouseElectricalVO houseElectrical, Query query) {
		IPage<HouseElectricalVO> pages = houseElectricalService.selectHouseElectricalPage(Condition.getPage(query), houseElectrical);
		return R.data(pages);
	}

	/**
	 * 踏勘房屋电气信息 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入houseElectrical")
	public R save(@Valid @RequestBody HouseElectricalEntity houseElectrical) {
		return R.status(houseElectricalService.save(houseElectrical));
	}

	/**
	 * 踏勘房屋电气信息 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入houseElectrical")
	public R update(@Valid @RequestBody HouseElectricalEntity houseElectrical) {
		return R.status(houseElectricalService.updateById(houseElectrical));
	}

	/**
	 * 踏勘房屋电气信息 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入houseElectrical")
	public R submit(@Valid @RequestBody HouseElectricalEntity houseElectrical) {
		return R.status(houseElectricalService.saveOrUpdate(houseElectrical));
	}

	/**
	 * 踏勘房屋电气信息 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(houseElectricalService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-houseElectrical")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出数据", notes = "传入houseElectrical")
	public void exportHouseElectrical(@ApiIgnore @RequestParam Map<String, Object> houseElectrical, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<HouseElectricalEntity> queryWrapper = Condition.getQueryWrapper(houseElectrical, HouseElectricalEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(HouseElectrical::getTenantId, bladeUser.getTenantId());
		//}
		queryWrapper.lambda().eq(HouseElectricalEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<HouseElectricalExcel> list = houseElectricalService.exportHouseElectrical(queryWrapper);
		ExcelUtil.export(response, "踏勘房屋电气信息数据" + DateUtil.time(), "踏勘房屋电气信息数据表", list, HouseElectricalExcel.class);
	}

}
