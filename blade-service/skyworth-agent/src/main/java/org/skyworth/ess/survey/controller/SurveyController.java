/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.revieworder.orderworkflow.service.IReviewOrderService;
import org.skyworth.ess.survey.dto.*;
import org.skyworth.ess.survey.service.ISurveyService;
import org.skyworth.ess.survey.vo.SurveyInfoVO;
import org.skyworth.ess.survey.vo.SurveyPhotoVO;
import org.skyworth.ess.survey.vo.SurveySignVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 踏勘信息 控制器
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@RestController
@AllArgsConstructor
@RequestMapping("survey")
@Api(value = "踏勘信息", tags = "踏勘信息接口")
public class SurveyController extends BladeController {

	private final ISurveyService surveyService;

	private final IReviewOrderService reviewOrderService;


	/**
	 * 保存Or修改踏勘信息
	 */
	@PostMapping("/saveOrUpdate")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存Or修改踏勘信息", notes = "传入surveyInfoDTO")
	public R<Boolean> save(@Valid @RequestBody SurveyInfoDTO surveyInfoDTO) {
		return R.status(surveyService.saveOrUpdateSurvey(surveyInfoDTO));
	}

	/**
	 * 踏勘详情展示
	 */
	@PostMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "踏勘详情展示", notes = "传入surveyDetailDTO")
	public R<SurveyInfoVO> detail(@Valid @RequestBody SurveyDetailDTO surveyDetailDTO) {
		return R.data(surveyService.surveyDetail(surveyDetailDTO));
	}

	/**
	 * 踏勘签名
	 */
	@PostMapping("/sign")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "踏勘签名", notes = "传入surveySignDTO")
	public R<Boolean> surveySign(@Valid @RequestBody SurveySignDTO surveySignDTO) {
		return R.status(surveyService.surveySign(surveySignDTO));
	}

	/**
	 * 踏勘签名详情展示
	 */
	@GetMapping("/surveySignDetail")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "踏勘签名详情展示", notes = "传入orderId")
	public R<SurveySignVO> surveySignDetail(String orderId) {
		return R.data(surveyService.surveySignDetail(orderId));
	}

	/**
	 * 拍照上传
	 */
	@PostMapping("/ownerPhoto")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "拍照上传", notes = "传入ownerPhotoDTO")
	public R<Boolean> ownerPhoto(@Valid @RequestBody OwnerPhotoDTO ownerPhotoDTO) {
		return R.status(surveyService.ownerPhoto(ownerPhotoDTO));
	}

	/**
	 * 拍照详情展示
	 */
	@GetMapping("/ownerPhotoDetail")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "拍照详情展示", notes = "传入orderId")
	public R<SurveyPhotoVO> ownerPhotoDetail(String orderId) {
		return R.data(surveyService.ownerPhotoDetail(orderId));
	}

	/**
	 * 踏勘信息填写完成提交
	 *
	 * @param surveySubmitDTO 提交参数
	 * @return 是否提交成功
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "踏勘信息填写完成提交", notes = "传入orderFlowDTO")
	public R<Boolean> submit(@RequestBody SurveySubmitDTO surveySubmitDTO) {
		return R.status(surveyService.submit(surveySubmitDTO,getRequest().getHeader("User-Type")));
	}

	/**
	 * 踏勘信息驳回
	 */
	@PostMapping("/reject")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "踏勘信息驳回", notes = "传入orderFlowDTO")
	public R<?> reject(@RequestBody OrderFlowDTO orderFlowDTO) {
		return reviewOrderService.examineApprove(orderFlowDTO);
	}

}
