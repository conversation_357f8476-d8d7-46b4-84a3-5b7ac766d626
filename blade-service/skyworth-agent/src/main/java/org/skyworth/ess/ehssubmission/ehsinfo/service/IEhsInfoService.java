/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.ehsinfo.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import org.skyworth.ess.ehssubmission.ehsinfo.dto.EhsInfoDTO;
import org.skyworth.ess.ehssubmission.ehsinfo.entity.EhsInfoEntity;
import org.skyworth.ess.ehssubmission.ehsinfo.excel.EhsInfoExcel;
import org.skyworth.ess.ehssubmission.ehsinfo.vo.EhsInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.api.R;

import java.util.List;

/**
 * ehs信息 服务类
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
public interface IEhsInfoService extends BaseService<EhsInfoEntity> {
	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param info
	 * @return
	 */
	IPage<EhsInfoVO> selectInfoPage(IPage<EhsInfoVO> page, EhsInfoVO info);


	/**
	 * 导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<EhsInfoExcel> exportInfo(Wrapper<EhsInfoEntity> queryWrapper);


	R<?> insertEhsInfo(EhsInfoDTO infoDTO);



	R<?> submitEhsInfoExamine(EhsInfoDTO infoDTO);



	R<?> selectEhsInfo(EhsInfoDTO infoDTO);


	R<?> getEhsHistoryDataList(EhsInfoDTO infoDTO);
}
