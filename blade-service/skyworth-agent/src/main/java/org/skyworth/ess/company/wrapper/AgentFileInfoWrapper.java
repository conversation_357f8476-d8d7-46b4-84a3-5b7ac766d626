/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.company.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.company.entity.AgentFileInfoEntity;
import org.skyworth.ess.company.vo.AgentFileInfoVO;
import java.util.Objects;

/**
 * 代理商文件管理 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public class AgentFileInfoWrapper extends BaseEntityWrapper<AgentFileInfoEntity, AgentFileInfoVO>  {

	public static AgentFileInfoWrapper build() {
		return new AgentFileInfoWrapper();
 	}

	@Override
	public AgentFileInfoVO entityVO(AgentFileInfoEntity agentFileInfo) {
		AgentFileInfoVO agentFileInfoVO = Objects.requireNonNull(BeanUtil.copy(agentFileInfo, AgentFileInfoVO.class));

		//User createUser = UserCache.getUser(agentFileInfo.getCreateUser());
		//User updateUser = UserCache.getUser(agentFileInfo.getUpdateUser());
		//agentFileInfoVO.setCreateUserName(createUser.getName());
		//agentFileInfoVO.setUpdateUserName(updateUser.getName());

		return agentFileInfoVO;
	}


}
