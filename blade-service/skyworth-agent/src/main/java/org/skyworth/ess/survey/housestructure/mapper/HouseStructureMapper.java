/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.survey.housestructure.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.survey.housestructure.entity.HouseStructureEntity;
import org.skyworth.ess.survey.housestructure.excel.HouseStructureExcel;
import org.skyworth.ess.survey.housestructure.vo.HouseStructureVO;

import java.util.List;

/**
 * 踏勘房屋结构信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface HouseStructureMapper extends BaseMapper<HouseStructureEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param houseStructure
	 * @return
	 */
	List<HouseStructureVO> selectHouseStructurePage(IPage page, HouseStructureVO houseStructure);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<HouseStructureExcel> exportHouseStructure(@Param("ew") Wrapper<HouseStructureEntity> queryWrapper);

}
