/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.surveyassignment.orderrelateduser.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 订单关系人 实体类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Data
@TableName("business_order_related_user")
@ApiModel(value = "OrderRelatedUser对象", description = "订单关系人")
@EqualsAndHashCode(callSuper = true)
public class OrderRelatedUserEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 节点类型（业务字典agent_business_order_node_type）
	 */
	@ApiModelProperty(value = "节点类型（业务字典agent_business_order_node_type）")
	private String nodeType;
	/**
	 * 人员类别
	 */
	@ApiModelProperty(value = "人员类别")
	private String userType;
	/**
	 * 人员id
	 */
	@ApiModelProperty(value = "人员id")
	private Long userId;
	/**
	 * 人员名称
	 */
	@ApiModelProperty(value = "人员名称")
	private String userName;
	/**
	 * 图片key
	 */
	@ApiModelProperty(value = "图片key")
	private Long imgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}
