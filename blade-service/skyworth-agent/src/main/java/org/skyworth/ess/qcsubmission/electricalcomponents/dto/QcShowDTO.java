package org.skyworth.ess.qcsubmission.electricalcomponents.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.system.entity.SkyWorthFileEntity;

/**
 * QC分段展示判断
 * org.skyworth.ess.qcsubmission.electricalcomponents.dto
 *
 * <AUTHOR>
 * @since 2023/11/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QcShowDTO extends SkyWorthFileEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private String orderId;

	/**
	 * 是否显示业主签名
	 */
	@ApiModelProperty(value = "是否显示业主签名")
	private Boolean displayLandlordVerification;
	/**
	 * 是否显示逆变器安装
	 */
	@ApiModelProperty(value = "是否显示逆变器安装")
	private Boolean displayInverterInstallation;
	/**
	 * 是否显示太阳能板安装
	 */
	@ApiModelProperty(value = "是否显示太阳能板安装")
	private Boolean displaySolarPanels;
	/**
	 * 是否显示电器元件
	 */
	@ApiModelProperty(value = "是否显示电器元件")
	private Boolean displayElectricalComponents;
	/**
	 * 是否显示接地设备
	 */
	@ApiModelProperty(value = "是否显示接地设备")
	private Boolean displayGroundingEquipment;
	/**
	 * 是否显示安装机架
	 */
	@ApiModelProperty(value = "是否显示安装机架")
	private Boolean displayMountingAndRacking;
	/**
	 * 是否显示安装与遵守
	 */
	@ApiModelProperty(value = "是否显示安装与遵守")
	private Boolean displaySafetyAndCompliance;
	/**
	 * 是否显示系统测试
	 */
	@ApiModelProperty(value = "是否显示系统测试")
	private Boolean displaySystemTesting;
	/**
	 * 是否显示最终检验
	 */
	@ApiModelProperty(value = "是否显示最终检验")
	private Boolean displayFinalInspection;

}
