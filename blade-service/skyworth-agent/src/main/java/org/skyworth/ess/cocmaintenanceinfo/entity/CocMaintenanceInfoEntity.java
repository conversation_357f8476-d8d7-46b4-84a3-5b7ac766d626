/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.cocmaintenanceinfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.system.entity.SkyWorthFileEntity;

/**
 * coc和维护信息 实体类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Data
@TableName("coc_maintenance_info")
@ApiModel(value = "cocMaintenanceInfo对象", description = "coc和维护信息")
@EqualsAndHashCode(callSuper = true)
public class CocMaintenanceInfoEntity extends SkyWorthFileEntity {

	/**
	 * 订单
	 */
	@ApiModelProperty(value = "订单")
	private Long orderId;
	/**
	 * 临时coc图片key
	 */
	@ApiModelProperty(value = "临时coc图片key")
	private Long temporaryCocImgBizKey;
	/**
	 * 临时coc开始时间
	 */
	@ApiModelProperty(value = "临时coc开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String temporaryCocStartDate;
//	/**
//	 * 临时coc结束时间
//	 */
//	@ApiModelProperty(value = "临时coc结束时间")
//	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//	private String temporaryCocEndDate;
	/**
	 * 房东签名图片key
	 */
	@ApiModelProperty(value = "房东签名图片key")
	private Long facLandlordSign;
	/**
	 * 房东文件图片key
	 */
	@ApiModelProperty(value = "房东文件图片key")
	private Long facLandlordDocImgBizKey;

	/**
	 * 站点id
	 */
	@ApiModelProperty(value = "站点id")
	private Long facPlantId;
	/**
	 * 站点名称
	 */
	@ApiModelProperty(value = "站点名称")
	private String facPlantName;
	/**
	 * fac技术工程师签名图片key
	 */
	@ApiModelProperty(value = "fac技术工程师签名图片key")
	private Long facTechnicianSign;
	/**
	 * fac技术工程师文件图片key
	 */
	@ApiModelProperty(value = "fac技术工程师文件图片key")
	private Long facTechnicianImgBizKey;
	/**
	 * 声明（1勾选）
	 */
	@ApiModelProperty(value = "声明（1勾选）")
	private Integer facDeclaration;
	/**
	 * 余额确认文件key
	 */
	@ApiModelProperty(value = "余额确认文件key")
	private Long balancePaymentConfirmDocBizKey;
	/**
	 * 最终coc图片key
	 */
	@ApiModelProperty(value = "最终coc图片key")
	private Long finalCocImgBizKey;
	/**
	 * 最终coc类型（1永久）
	 */
	@ApiModelProperty(value = "最终coc类型（1永久）")
	private Integer finalCocType;
	/**
	 * 最终coc开始时间
	 */
	@ApiModelProperty(value = "最终coc开始时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String finalCocStartDate;
	/**
	 * 最终coc结束时间
	 */
	@ApiModelProperty(value = "最终coc结束时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String finalCocEndDate;
	/**
	 * 免费维修时间
	 */
	@ApiModelProperty(value = "免费维修时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String freeMaintenanceServiceDate;
	/**
	 * 保修时间
	 */
	@ApiModelProperty(value = "保修时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String warrantyDate;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	/**
	 * 客户确认
	 */
	@ApiModelProperty(value = "客户确认")
	private Long clientConfirmationBizKey;

	/**
	 * 交付编号
	 */
	@ApiModelProperty(value = "交付编号")
	private String deliveryNumber;

	/**
	 * 交付方式
	 */
	@ApiModelProperty(value = "交付方式")
	private String delivery;

	/**
	 * 客户验收
	 */
	@ApiModelProperty(value = "客户验收")
	private Long customersAcceptance;

	@ApiModelProperty(value = "签名人与户主关系")
	private String signerRelationship;
}
