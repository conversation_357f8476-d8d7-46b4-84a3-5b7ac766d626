package org.skyworth.ess.survey.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.survey.dto.FnanceDTO;
import org.skyworth.ess.survey.dto.PaymentConfirmSendEmailDTO;
import org.skyworth.ess.survey.dto.SendEmailDTO;
import org.skyworth.ess.survey.service.ISurveyService;
import org.skyworth.ess.survey.vo.EmailVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 财务相关控制器
 *
 * <AUTHOR>
 * @since 2023-12-7
 */
@RestController
@AllArgsConstructor
@RequestMapping("finance")
@Api(value = "财务相关接口", tags = "财务相关接口")
public class FnanceController extends BladeController {
	private final ISurveyService surveyService;

	/**
	 * 财务审核通过
	 *
	 * @param fnanceDTO
	 * @return 是否审批成功
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "审核通过保存邮件信息", notes = "邮件地址与邮件内容")
	public R<Boolean> submit(@RequestBody FnanceDTO fnanceDTO) {
		return R.status(surveyService.financeSubmit(fnanceDTO.getOrderFlowDTO(),fnanceDTO.getSkyWorthFileEntity()));
	}

	/**
	 * 发送邮件
	 *
	 * @param multipartFile 邮件附件
	 * @return 是否发送成功
	 */
	@PostMapping("/sendMail")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "发送邮件", notes = "邮件附件与业务ID")
	public R<Boolean> sendMail(@RequestPart(value = "file", required = false) MultipartFile[] file, SendEmailDTO sendEmailDTO) {
		return R.status(surveyService.sendMail(file, sendEmailDTO));
	}

	/**
	 * 邮件详情
	 *
	 * @param businessId 订单业务ID
	 * @return 邮件详情
	 */
	@GetMapping("/emailDetail")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "邮件详情", notes = "业务ID")
	public R<EmailVO> emailDetail(String businessId) {
		return R.data(surveyService.emailDetail(businessId));
	}
	/**
	 * 发送邮件
	 *
	 * @param multipartFile 邮件附件
	 * @param sendEmailDTO 邮件对象
	 * @return 是否发送成功
	 */
	@PostMapping("/paymentConfirmSendMail")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "发送邮件", notes = "邮件附件与业务ID")
	public R<Boolean> paymentConfirmSendMail(@RequestPart("file") MultipartFile[] multipartFile, PaymentConfirmSendEmailDTO sendEmailDTO) {
		return R.status(surveyService.paymentConfirmSendMail(multipartFile,sendEmailDTO));
	}

	@GetMapping("/paymentEmailDetail")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "邮件详情", notes = "业务ID")
	public R<EmailVO> paymentEmailDetail(String orderId) {
		return R.data(surveyService.paymentEmailDetail(orderId));
	}
}
