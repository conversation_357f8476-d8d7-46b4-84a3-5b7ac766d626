/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.ehssubmission.ehsinfo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.createorder.order.entity.OrderEntity;
import org.skyworth.ess.ehssubmission.ehsinfo.entity.EhsInfoEntity;
import org.skyworth.ess.ehssubmission.toolsmachinery.entity.ToolsMachineryEntity;
import org.skyworth.ess.revieworder.orderworkflow.vo.AttachmentDescVO;
import org.springblade.flow.core.dto.OrderFlowDTO;
import org.springblade.system.entity.SkyWorthFileEntity;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * ehs信息 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EhsInfoDTO extends SkyWorthFileEntity {
	private static final long serialVersionUID = 1L;

	//订单id
	private Long orderId;

	//ehs基本信息
	@Valid
	private EhsInfoEntity ehsInfoEntity;

	//订单信息
	private OrderEntity orderEntity;

	//审批信息
	private OrderFlowDTO orderFlowDTO;

	//施工日期
	private String constructionDate;


	//踏勘人员名称
	private String technicianName;


	//ehs新增的相关工具设备信息
	private List<ToolsMachineryEntity> addToolsMachineryEntityList;

	//ehs要删除的相关工具设备信息
	private List<ToolsMachineryEntity> deleteToolsMachineryEntityList;


	//ehs基本信息图片信息
	private AttachmentDescVO ehsBasicAttachmentInfo;

	//附件信息
	private SkyWorthFileEntity skyWorthFileEntity;


	//历史标志 true：有历史流程；false：无历史流程
	private boolean goCheckFlag;


	//ehs最新版本时间
	private String ehsVersion;


	//通过goCheck点击进来查看历史数据标志
	private String byGoCheckEnterFlag;

}
