/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.qcsubmission.inverterinstallation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 施工-逆变器信息; 实体类
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Data
@TableName("qc_inverter_installation")
@ApiModel(value = "InverterInstallation对象", description = "施工-逆变器信息;")
@EqualsAndHashCode(callSuper = true)
public class InverterInstallationEntity extends TenantEntity {

	/**
	 * 订单id
	 */
	@ApiModelProperty(value = "订单id")
	private Long orderId;
	/**
	 * 逆变器类型
	 */
	@ApiModelProperty(value = "逆变器类型")
	private String inverterType;
	/**
	 * 逆变器选择other的描述
	 */
	@ApiModelProperty(value = "逆变器选择other的描述")
	private String inverterTypeOtherRemark;
	/**
	 * 电池数量
	 */
	@ApiModelProperty(value = "电池数量")
	private String numberOfBatteries;
	/**
	 * 电池数量选择other的描述
	 */
	@ApiModelProperty(value = "电池数量选择other的描述")
	private String numberOfBatteriesOtherRemark;
	/**
	 * 逆变器规范安装图片业务主键
	 */
	@ApiModelProperty(value = "逆变器规范安装图片业务主键")
	private Long verificationOfSpecificationModelImgBizKey;
	/**
	 * 逆变器序号图片业务主键
	 */
	@ApiModelProperty(value = "逆变器序号图片业务主键")
	private Long inverterSerialNumberImgBizKey;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}
