agent.createOrder.selectDeliveryManager.agentId.notNull=Die Distributor-ID kann nicht leer sein
design.productList.notNull=Lagerbestand kann nicht leer sein
agent.reviewOrder.tentativeInstallStartDate.notNull=Das vorl\u00E4ufige Installationsstartdatum darf nicht leer sein
agent.qcSubmission.loadSystemTestingVO.exception=Speichern der Systemtestdaten fehlgeschlagen
agent.qcSubmission.submitQcInfo.qcInfoDtos.notEmpty=QC-Info-Datenanzeigeobjekt kann nicht leer sein
agent.installationDate.updateInstallInfo.saveInstallRelatedInfo.fail=Speichern der Installationsinformationen fehlgeschlagen
agent.reviewOrder.workFlow.fail=Arbeitsablauf fehlgeschlagen
agent.reviewOrder.siteAddress.notNull=Adresse der Energiestation kann nicht leer sein
agent.installationDate.installRelatedInfoEntity.notNull=Installationsdatumsmodul darf nicht leer sein
agent.reviewOrder.surveyDate.notNull=Erhebungsdatum kann nicht leer sein
agent.quoteInfo.updateQuoteInfo.updateQuoteInfo.fail=Bearbeiten der Reihenfolge fehlgeschlagen
agent.materialCollection.setWorkFlowData.businessId.notNull=Business ID kann nicht leer sein
survey.submit.houseStructureEmpty=Techniker-Haus Info kann nicht leer sein
agent.ehsSubmission.riskList.notNull=Risikoliste kann nicht leer sein
agent.reviewOrder.laterFlowTreatment.IncorrectParameter.Transmission=Der Parameter konnte nicht \u00FCbertragen werden
agent.createOrder.projectType.notNull=Projekttyp kann nicht leer sein
agent.qcSubmission.submitQcInfo.orderFlowDTO.notEmpty=Audit bestellen Workflow-Anzeigeobjekt kann nicht leer sein
agent.createOrder.insertOrder.workFlowSaveFail=Speichern des Arbeitsablaufs fehlgeschlagen
agent.installationDate.constructionDate.notNull=Installationsdatum kann nicht leer sein
agent.reviewOrder.examineEditOrCancelOrder.orderDTO.notNull=Die Auftragszusatzinfo darf nicht leer sein
agent.reviewOrder.preProcessTaskCheck.order.notNull=Der Arbeitsauftrag existiert nicht
agent.companyName.notNull=Firmenname kann nicht leer sein
agent.surveyAssignment.designatedPerson.orderId.notNull=OrderId kann nicht null sein
agent.createOrder.rolloutManagerId.notNull=Rollout Manager ID kann nicht leer sein
agent.installWoAssignment.validParamStatus.orderRelatedUserDTO.modifyStatus.error=Die Bestellung kann an niemanden verteilt werden
agent.qcSubmission.getBasicInfo.exception=Abruf der QC-Info-Daten fehlgeschlagen
agent.ehsSubmission.declaration.notNull=Die pr\u00E4sentation kann nicht Leer sein
agent.reviewOrder.longitude.notNull=Der L\u00E4ngengrad darf nicht leer sein
agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.orderId.notEmpty=Bestell-ID kann nicht leer sein
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.notNull=Arbeitsauftrags parameter kann nicht leer sein
agent.qcSubmission.saveOrUpdateMountingAndRacking.MountingAndRacking.notExist=Die Mounting- und Racking-Informationen dieser ID%s existieren nicht
agent.ehsSubmission.insertOrUpdateEhsInfo.saveOrUpdateEhsInfo.fail=Speichern der EHS-Informationen fehlgeschlagen
agent.surveyAssignment.electricianName.notNull=Umfrage Elektriker kann nicht leer sein
survey.submit.LandlordSignEmpty=Unterschrift ist nicht abgeschlossen
agent.createOrder.customerName.notNull=Kundenname kann nicht leer sein
agent.ehsVerification.ehsNodeName.notNull=EHS-Statusname kann nicht leer sein
agent.reviewOrder.siteProvinceCode.notNull=Provinzcode der Energiestation kann nicht leer sein
email.content.notNull=E-Mail-Inhalt kann nicht leer sein
agent.qcSubmission.submitQcInfo.clientType.notEmpty=Der Clienttyp kann nicht leer sein
email.sender.limit=Die Anzahl der E-Mail-Empf\u00E4nger darf %s nicht \u00FCberschreiten
agent.createOrder.orderId.notNull=Bestell-ID kann nicht leer sein
agent.ehsVerification.ehsInfoFlag.notNull=EHS-Statustyp kann nicht leer sein
agent.ehsSubmission.selectEhsInfo.ehsInfoDTO.notNull=Die ehs-informationen sind nicht Leer
agent.qcSubmission.loadSafetyAndComplianceVO.exception=Die Sicherheits- und Compliance-Daten konnten nicht abgerufen werden
agent.qcSubmission.submitQcInfo.subNodeSaveStatus.unFinish=Das folgende Teilmodul wurde nicht abgeschlossen:%s
email.address.notNull=E-Mail-Adresse kann nicht leer sein
agent.qcSubmission.saveOrUpdateQcInfo.qcInfoDTO.notEmpty=QC Info Datenanzeige Objekt ist leer
agent.createOrder.insertOrder.getOrderNumber.notNull=Der Arbeitsauftrag kann nicht gefunden werden
agent.qcSubmission.getQcInfoDetail.qcShowDTO.notEmpty=QC-Info-Datenanzeigeobjekt ist leer oder Auftrags-ID ist leer
email.content.invalid=E-Mail nicht verf\u00FCgbar:%s
design.save.designInfoAlreadySaved=designInfo ist gespeichert. Bitte laden Sie die designInfo-ID hoch, wenn Sie sie bearbeiten m\u00FCssen
agent.sku.base.info.save.sku.exist=der sku-Code existiert.
agent.companyInfo.notNull=Die firmeninformationen d\u00FCrfen nicht Leer sein
agent.reviewOrder.laterFlowTreatment.orderWorkFlowUpdate.fail=Bearbeiten des Workflows fehlgeschlagen
agent.qcSubmission.loadInverterInstallationVO.exception=Die Installationsdaten des Wechselrichters konnten nicht abgerufen werden
agent.installationDate.selectInstallDateInfo.installRelatedInfoDTO.notNull=Die entsprechenden informationen k\u00F6nnen nicht gel\u00F6scht werden
agent.materialCollection.setWorkFlowData.getRelatedUser.notNull=Site Techniker Informationen sind leer
survey.save.houseElectricalEmpty=Elektriker-Haus Info kann nicht leer sein
agent.sku.base.info.save.can.not.empty=Der SKU-Name, der Ger\u00E4tetyp, das Unternehmen, die Standards, die Einheit und der Preis d\u00FCrfen nicht leer sein.
agent.qcSubmission.getInstallRelatedInfoEntity.orderId.notExist=Zugeh\u00F6rige Informationen zu dieser ID %s sind nicht vorhanden
agent.createOrder.customerEmail.notNull=Kunden-E-Mail kann nicht leer sein
survey.submit.houseStructureAlreadySaved=Techniker-Hausinformationen werden gespeichert. Bitte laden Sie die Hausinformations-ID hoch, wenn Sie sie bearbeiten m\u00FCssen
agent.company.attributes.notNull=Das Attribut Distributor kann nicht leer sein
agent.company.has.intransitive.order=Die zwischenh\u00E4ndler k\u00F6nnen nicht gel\u00F6scht werden (%s), da die bestellungen noch aktiv sind
agent.reviewOrder.updateOrderInfo.updateOrder.fail=Bearbeitung des Produktionsauftrags fehlgeschlagen
survey.submit.electricianEmpty=Electrician-House Info nicht abgeschlossen
email.recipients.limit=Die Anzahl der E-Mail-Empf\u00E4nger darf %s nicht \u00FCberschreiten
agent.createOrder.distributorId.notNull=Verteiler-ID kann nicht leer sein
agent.createOrder.insertOrder.saveOrderFail=Speichern der Bestellung fehlgeschlagen
agent.createOrder.customerPhone.notNull=Telefonnummer des Kunden darf nicht leer sein
agent.parameter.invalid=Der Verteilerparameter ist ung\u00FCltig
survey.submit.houseInfoEmpty=Hausinfo nicht abgeschlossen
agent.qcSubmission.getOrderEntity.orderId.notExist=Bestellinformationen dieser ID%s existieren nicht
agent.qcSubmission.submitQcInfo.clientType.notSupported=Der Clienttyp wird nicht unterst\u00FCtzt
agent.createOrder.insertOrder.startProcessFail=Der Workflow konnte nicht aktiviert werden
agent.qcSubmission.getBasicInfo.orderId.notEmpty=Bestell-ID kann nicht leer sein
agent.installWoAssignment.selectAssignedConstructInfo.orderRelatedUserDTO.notNull=Angaben \u00FCber die betreffende person sind nicht verf\u00FCgbar
agent.installWoAssignment.validParamStatus.orderRelatedUserPersonName.notNull=Der Name des Standorttechnikers und Elektrikers darf nicht leer sein
survey.surveyPhoto.imgBizKey.notNull=Das Foto des Besitzers des Technikers/Elektrikers ist nicht vollst\u00E4ndig
system.error=Systemfehler
agent.qcSubmission.loadSolarPanelsVO.exception=Die Daten der Solarmodule konnten nicht abgerufen werden
stock.deviceItem.notNull=Vorrat ist leer
agent.quoteInfo.updateQuoteInfo.quoteInfoEntity.notNull=Angebotsinfo darf nicht leer sein
agent.surveyAssignment.technicianId.notNull=Umfragetechniker-ID darf nicht leer sein
agent.reviewOrder.preProcessTaskCheck.examineApproveType.error=Der Audit-Status konnte nicht \u00FCbertragen werden
agent.reviewOrder.latitude.notNull=Breitengrad kann nicht leer sein
agent.surveyAssignment.electricianId.notNull=Umfrage Elektriker-ID kann nicht leer sein
survey.save.houseElectricalAlreadySaved=Elektriker-Haus Info ist gespeichert
agent.ehsSubmission.submitEhsInfoExamine.orderFlowDTO.notNull=EHS-Audit-Workflow-Info kann nicht leer sein
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.comment.notNull=Die Rezensionsmeinung darf nicht leer sein
agent.company.name.repeat=Der Name der Verteilerfirma ist belegt
stock.surplusQuantity.invalid=skuCode:%s Nicht gen\u00FCgend Lagerbestand
agent.qcSubmission.loadMountingAndRackingVO.exception=Die Montage- und Regaldaten konnten nicht abgerufen werden
agent.surveyAssignment.technicianName.notNull=Vermessungstechniker kann nicht leer sein
agent.qcSubmission.saveOrUpdateSolarPanels.SolarPanels.notExist=Die Solarpaneelinformation dieser ID%s existiert nicht
agent.reviewOrder.setOrUpdateFileImgInfo.attachmentInfo.fail=Bearbeitung der Anlage fehlgeschlagen
agent.qcSubmission.saveOrUpdateQcInfo.inverterType.notEmpty=Der Wechselrichtertyp kann nicht leer sein, wenn der Status abgeschlossen ist
design.attachmentInfo.notNull=Anlage kann nicht leer sein
agent.reviewOrder.tentativeInstallEndDate.notNull=Das vorl\u00E4ufige Enddatum der Installation darf nicht leer sein
agent.createOrder.insertOrder.getCompanyAttributes.fail=Das Verteilerattribut kann nicht gefiltert werden
survey.submit.finish=Umfrageinformationen wurden \u00FCbermittelt. Bitte aktualisieren Sie die Seite
agent.installWoAssignment.validParamStatus.modifyStatus.notNull=Der knopf darf nicht Leer sein
agent.parameter.notEmpty=Der Verteilerparameter kann nicht leer sein
agent.ehsSubmission.riskSolution.notNull=Die l\u00F6sungen f\u00FCr risiken d\u00FCrfen nicht Leer sein
agent.installWoAssignment.getButtonStatus.orderWorkFlowEntity.notNull=Prozesse l\u00E4sst sich nicht verfeinern
agent.qcSubmission.loadElectricalComponentsVO.exception=Die Daten der elektrischen Komponenten konnten nicht abgerufen werden
agent.reviewOrder.preProcessTaskCheck.orderFlowInfo.error=Arbeitsablauf ist falsch, wiederholt oder existiert nicht
agent.materialCollection.examineMaterialCollection.deviceItemEntityList.notNull=Info zur Materialerfassung kann nicht leer sein
survey.submit.allEmpty=Hausinfo und Elektroinfo nicht abgeschlossen
agent.qcSubmission.loadFinalInspectionVO.exception=Abruf der Endkontrolldaten fehlgeschlagen
survey.notNull=Umfrageinformationen existieren nicht
agent.materialCollection.updateMaterialCollectionInfo.updateDeviceItem.fail=Aktualisierung der Materialerfassungsdaten fehlgeschlagen
agent.qcSubmission.loadAttachmentDescVO.exception=Der Anhang oder die Bildbeschreibung konnte nicht abgerufen werden
email.content.sendMail=Langsames Laden, bitte warten Sie und versuchen Sie es sp\uFFFDter
survey.surveySign.imgBizKey.notNull=Die Unterschrift des Technikers/Elektrikers/LL ist nicht ausgef\u00FCllt
agent.reviewOrder.siteCityCode.notNull=Der Ortscode der Energiestation kann nicht leer sein
agent.createOrder.selectDeliveryManager.orderDTO.notNull=Daten \u00FCber die bestellung sind nicht Leer
agent.qcSubmission.getHouseElectricalEntity.orderId.notExist=Elektrische Info dieses Auftrags %s existiert nicht
agent.qcSubmission.saveOrUpdateInverterInstallation.InverterInstallation.notExist=Die Wechselrichter-Installationsdaten dieser ID %s sind nicht vorhanden
agent.reviewOrder.installBudgets.notNull=Das Budget f\u00FCr die Installation darf nicht leer sein
agent.qcSubmission.getHouseStructureEntity.orderId.notExist=Hausstruktur-Information dieser ID %s existiert nicht
agent.qcSubmission.loadGroundingEquipmentVO.exception=Die Daten der Erdungsausr\u00FCstung konnten nicht abgerufen werden
agent.ehsSubmission.memberSafetyChecklist.notNull=Die Sicherheitscheckliste des Mitglieds kann nicht leer sein
agent.qcSubmission.saveOrUpdateElectricalComponent.ElectricalComponents.notExist=Die elektrischen Komponenteninformationen dieser ID %s sind nicht vorhanden
agent.reviewOrder.siteCountryCode.notNull=Der L\u00E4ndercode der Energiestation darf nicht leer sein
agent.reviewOrder.workFlowParamValid.OrderFlowDTO.variables.examineApproveType.notNull=Der Audit-Status kann nicht leer sein
agent.qcSubmission.submitQcInfo.orderDTO.businessId.notEmpty=Workflow Business ID kann nicht leer sein
agent.installWoAssignment.validParamStatus.orderRelatedUserPerson.notNull=Die IDs f\u00FCr Standorttechniker und Elektriker d\u00FCrfen nicht leer sein
email.send.fail=Das Senden der E-Mail ist fehlgeschlagen. Bitte versuchen Sie es sp\u00E4ter noch einmal
agent.reviewOrder.installReason.notNull=Installationsgrund darf nicht leer sein
agent.surveyAssignment.designatedPerson.SurveyInfoEntity.notNull=Die Informationen zur Umfrageaufgabe d\u00FCrfen nicht leer sein
design.productList.invalid=%s wiederholen
agent.blob.get.exception=Ausnahme beim Abrufen von BlobContainer, Fehler: %s
agent.sastoken.get.error=SasToken abrufen, Fehler: %s
