package org.skyworth.ess.exceptionlog.excel;


import lombok.Data;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;


/**
 * 异常日志表 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ExceptionLogExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备ID")
	private Long deviceInfoId;
	/**
	 * 设备SN号
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备SN号")
	private String serialNumber;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 异常消息
	 */
	@ColumnWidth(20)
	@ExcelProperty("异常消息")
	private String exceptionMessage;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
