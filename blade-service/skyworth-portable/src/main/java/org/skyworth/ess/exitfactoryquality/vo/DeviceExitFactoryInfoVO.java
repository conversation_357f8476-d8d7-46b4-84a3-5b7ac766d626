/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exitfactoryquality.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity;

import java.time.LocalDate;

/**
 * 设备出厂信息 视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceExitFactoryInfoVO extends DeviceExitFactoryInfoEntity {
	private static final long serialVersionUID = 1L;

	private String deviceTypeName;
	private String companyName;
	private String buyerUserName;
	private String buyerUserPhone;
	private String buyChannelName;
	private LocalDate qualityGuaranteeEndDate;
}
