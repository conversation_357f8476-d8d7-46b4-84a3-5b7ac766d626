package org.skyworth.ess.deviceinfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;

/**
 * 设备信息表 实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@TableName("device_info")
@ApiModel(value = "DeviceInfo对象", description = "设备信息表")
@EqualsAndHashCode(callSuper = true)
public class DeviceInfoEntity extends TenantEntity {

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String deviceName;
	/**
	 * 国家
	 */
	@ApiModelProperty(value = "国家")
	private String countryCode;
	/**
	 * 省
	 */
	@ApiModelProperty(value = "省")
	private String provinceCode;
	/**
	 * 城市
	 */
	@ApiModelProperty(value = "城市")
	private String cityCode;
	/**
	 * 区县
	 */
	@ApiModelProperty(value = "区县")
	private String countyCode;
	/**
	 * 便携电源SN
	 */
	@ApiModelProperty(value = "便携电源SN")
	private String powerSupplySn;
	/**
	 * 剩余电量百分比，手机端上报
	 */
	@ApiModelProperty(value = "剩余电量百分比，手机端上报")
	private BigDecimal remainingBatteryPercentage;
	/**
	 * 设备型号
	 */
	@ApiModelProperty(value = "设备型号")
	private String equipmentModel;
	/**
	 * 电池循环次数
	 */
	@ApiModelProperty(value = "电池循环次数")
	private Integer numberOfBatteryCycles;
	/**
	 * 电池健康度
	 */
	@ApiModelProperty(value = "电池健康度")
	private Integer batterySoh;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 * 设备状态
	 */
	@ApiModelProperty(value = "设备状态")
	private String deviceStatus;



}
