
package org.skyworth.ess.exitfactoryquality.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity;

/**
 * 设备出厂信息 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceExitFactoryInfoDTO extends DeviceExitFactoryInfoEntity {
	private static final long serialVersionUID = 1L;

}
