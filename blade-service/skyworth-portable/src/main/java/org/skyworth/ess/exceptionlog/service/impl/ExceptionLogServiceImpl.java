package org.skyworth.ess.exceptionlog.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import org.skyworth.ess.exceptionlog.entity.ExceptionLogEntity;
import org.skyworth.ess.exceptionlog.mapper.ExceptionLogMapper;
import org.skyworth.ess.exceptionlog.service.IExceptionLogService;
import org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 异常日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Service
@AllArgsConstructor
public class ExceptionLogServiceImpl extends BaseServiceImpl<ExceptionLogMapper, ExceptionLogEntity> implements IExceptionLogService {
	@Override
	public List<JSONObject> queryNumberOfDailyAnomalies(JSONObject jsonObject) {
		return baseMapper.queryNumberOfDailyAnomalies(jsonObject);
	}

	@Override
	public List<DeviceActivationAndAlarmExcel> queryNumberOfDailyAnomaliesExcelInfo(JSONObject jsonObject) {
		return baseMapper.queryNumberOfDailyAnomaliesExcelInfo(jsonObject);
	}
}
