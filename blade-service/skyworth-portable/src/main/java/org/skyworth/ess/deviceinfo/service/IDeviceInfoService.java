package org.skyworth.ess.deviceinfo.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.skyworth.ess.deviceinfo.entity.DeviceInfoEntity;
import org.skyworth.ess.deviceinfo.vo.DeviceInfoVO;
import org.springblade.core.mp.base.BaseService;

import java.util.Map;

/**
 * 设备信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
public interface IDeviceInfoService extends BaseService<DeviceInfoEntity> {
	/**
	 * 分页用户设备信息
	 *
	 * @param page       分页参数
	 * @param deviceInfo 入参
	 * @return IPage<DeviceInfoEntity>
	 * <AUTHOR>
	 * @since 2024/1/23 15:03
	 **/
	IPage<DeviceInfoVO> queryPage(IPage<DeviceInfoVO> page, Map<String, Object> deviceInfo);

	/**
	 * 分页统计
	 *
	 * @param deviceInfo 入参
	 * @return JSONObject
	 * <AUTHOR>
	 * @since 2024/1/24 16:26
	 **/
	JSONObject queryPageStatistics(Map<String, Object> deviceInfo);

	/**
	 * 查询详情
	 *
	 * @param id 入参
	 * @return DeviceInfoEntity
	 * <AUTHOR>
	 * @since 2024/1/23 15:12
	 **/
	JSONObject detail(Long id);

	/**
	 * 新增设备信息
	 *
	 * @param deviceInfoEntity 入参
	 * @return boolean
	 * <AUTHOR>
	 * @since 2024/1/23 17:33
	 **/
	Long saveDevice(DeviceInfoEntity deviceInfoEntity);

	/**
	 * app端查询我的设备
	 *
	 * @param page       分页
	 * @param deviceInfo 入参
	 * @return IPage<DeviceInfoVO>
	 * <AUTHOR>
	 * @since 2024/1/25 13:59
	 **/
	IPage<DeviceInfoVO> appQueryPage(IPage<DeviceInfoVO> page, Map<String, Object> deviceInfo);

	/**
	 * 修改人员行政区划信息，同步刷新设备表行政区划信息
	 *
	 * @param jsonObject 入参
	 * <AUTHOR>
	 * @since 2024/1/26 10:24
	 **/
	void updateDeviceDistrict(JSONObject jsonObject);
}
