/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exitfactoryquality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.valid.ValidGroups;
import org.springblade.core.tenant.mp.SkyWorthEntity;
import org.springblade.core.tenant.mp.TenantEntity;
import org.springblade.system.entity.SkyWorthFileEntity;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 设备出厂信息 实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@TableName("device_exit_factory_info")
@ApiModel(value = "deviceExitFactoryInfo对象", description = "设备出厂信息")
@EqualsAndHashCode(callSuper = true)
public class DeviceExitFactoryInfoEntity extends SkyWorthFileEntity {

	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	@NotBlank(message = "{portable.device.exit.factory.deviceSerialNumber.notNull}",groups = {ValidGroups.AddGroup.class,ValidGroups.EditGroup.class})
	private String deviceSerialNumber;
	/**
	 * 设备/逆变器型号:数据字典portable_device_type
	 */
	@ApiModelProperty(value = "设备/逆变器型号:数据字典portable_device_type")
	@NotBlank(message = "{portable.device.exit.factory.deviceType.notNull}",groups = ValidGroups.AddGroup.class)
	private String deviceType;
	/**
	 * 厂家：数据字典portable_company
	 */
	@ApiModelProperty(value = "厂家：数据字典portable_company")
	@NotBlank(message = "{portable.device.exit.factory.company.notNull}",groups = ValidGroups.AddGroup.class)
	private String company;
	/**
	 * 质保年限
	 */
	@ApiModelProperty(value = "质保年限")
	@NotNull(message = "{portable.device.exit.factory.qualityGuaranteeYear.notNull}",groups = ValidGroups.AddGroup.class)
	private Integer qualityGuaranteeYear;
	/**
	 * 出厂日期
	 */
	@ApiModelProperty(value = "出厂日期")
	@NotNull(message = "{portable.device.exit.factory.exitFactoryDate.notNull}",groups = ValidGroups.AddGroup.class)
	private LocalDate exitFactoryDate;
	/**
	 * 激活日期
	 */
	@ApiModelProperty(value = "激活日期")
	private LocalDate activationDate;
	/**
	 * 质保开始日期
	 */
	@ApiModelProperty(value = "质保开始日期")
	private LocalDate qualityGuaranteeBeginDate;
	/**
	 * 电池额定容量kwh
	 */
	@ApiModelProperty(value = "电池额定容量kwh")
	@NotNull(message = "{portable.device.exit.factory.batteryRatedCapacity.notNull}",groups = ValidGroups.AddGroup.class)
	private Long batteryRatedCapacity;
	/**
	 * 电池额定交流放电总功率w
	 */
	@ApiModelProperty(value = "电池额定交流放电总功率w")
	@NotNull(message = "{portable.device.exit.factory.batteryRatedAcDischargeCapacity.notNull}",groups = ValidGroups.AddGroup.class)
	private Long batteryRatedAcDischargeCapacity;
	/**
	 * 电池额定放电总功率w
	 */
	@ApiModelProperty(value = "电池额定放电总功率w")
	@NotNull(message = "{portable.device.exit.factory.batteryRatedDischargeCapacity.notNull}",groups = ValidGroups.AddGroup.class)
	private Long batteryRatedDischargeCapacity;
	/**
	 * 电池额定直流放电总功率w
	 */
	@ApiModelProperty(value = "电池额定直流放电总功率w")
	@NotNull(message = "{portable.device.exit.factory.batteryRatedDcDischargeCapacity.notNull}",groups = ValidGroups.AddGroup.class)
	private Long batteryRatedDcDischargeCapacity;
	/**
	 * 电池额定充电总功率w
	 */
	@ApiModelProperty(value = "电池额定充电总功率w")
	@NotNull(message = "{portable.device.exit.factory.batteryRatedChargeCapacity.notNull}",groups = ValidGroups.AddGroup.class)
	private Long batteryRatedChargeCapacity;
	/**
	 * 电池额定安德森接口充电总功率w
	 */
	@ApiModelProperty(value = "电池额定安德森接口充电总功率w")
	@NotNull(message = "{portable.device.exit.factory.batteryRatedAndersonChargeCapacity.notNull}",groups = ValidGroups.AddGroup.class)
	private Long batteryRatedAndersonChargeCapacity;
	/**
	 * 蓝牙协议版本
	 */
	@ApiModelProperty(value = "蓝牙协议版本")
	@NotBlank(message = "{portable.device.exit.factory.bluetoothProtocolVersion.notNull}",groups = ValidGroups.AddGroup.class)
	private String bluetoothProtocolVersion;
	/**
	 * 主板硬件版本
	 */
	@ApiModelProperty(value = "主板硬件版本")
	@NotBlank(message = "{portable.device.exit.factory.motherboardHardwareVersion.notNull}",groups = ValidGroups.AddGroup.class)
	private String motherboardHardwareVersion;
	/**
	 * 销售渠道：数据字典 portable_buy_channel
	 */
	@ApiModelProperty(value = "销售渠道：数据字典 portable_buy_channel")
	@NotBlank(message = "{portable.device.exit.factory.buyChannel.notNull}",groups = ValidGroups.EditGroup.class)
	private String buyChannel;
	/**
	 * 购买日期
	 */
	@ApiModelProperty(value = "购买日期")
	@NotNull(message = "{portable.device.exit.factory.buyDate.notNull}",groups = ValidGroups.EditGroup.class)
	private LocalDate buyDate;
	/**
	 * 订单编号
	 */
	@ApiModelProperty(value = "订单编号")
	@NotBlank(message = "{portable.device.exit.factory.buyOrderNumber.notNull}",groups = ValidGroups.EditGroup.class)
	private String buyOrderNumber;
	/**
	 * 购买凭证
	 */
	@ApiModelProperty(value = "购买凭证")
	@NotNull(message = "{portable.device.exit.factory.buyCertificateImgBizKey.notNull}",groups = ValidGroups.EditGroup.class)
	private Long buyCertificateImgBizKey;
	/**
	 * 首次上传凭证人id，一个SN只有一次
	 */
	@ApiModelProperty(value = "首次上传凭证人id，一个SN只有一次")
	private Long buyerUserId;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

	@TableField(exist = false)
	private String deviceTypeName;
	@TableField(exist = false)
	private String companyName;
	@TableField(exist = false)
	private String buyChannelName;
	@TableField(exist = false)
	private String buyerUserName;
	@TableField(exist = false)
	private String buyerUserPhone;
	// 质保截止日期
	@TableField(exist = false)
	private LocalDate qualityGuaranteeEndDate;
}
