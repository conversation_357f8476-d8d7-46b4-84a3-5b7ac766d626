package org.skyworth.ess.exitfactoryquality.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.*;

/**
 * <AUTHOR>
 */
@Getter
public enum ExitFactoryBaseColumnEnum {
    deviceSerialNumber(ExitFactorySheetEnum.base,"Device SN","设备SN"),
    deviceTypeName(ExitFactorySheetEnum.base,"Device type","设备类型"),
    companyName(ExitFactorySheetEnum.base,"Company","厂家"),
    qualityGuaranteeYear(ExitFactorySheetEnum.base,"Warranty period","质保年限"),
    exitFactoryDate(ExitFactorySheetEnum.base,"Date of production","出厂日期"),
    batteryRatedCapacity(ExitFactorySheetEnum.base,"Battery rated capacity(kwh)","电池额定容量(kwh)"),
    batteryRatedAcDischargeCapacity(ExitFactorySheetEnum.base,"Battery rated Ac-Discharge capacity(w)","电池额定交流放电总功率(w)"),
    batteryRatedDischargeCapacity(ExitFactorySheetEnum.base,"Battery rated discharge capacity(w)","电池额定放电总功率(w)"),
    batteryRatedDcDischargeCapacity(ExitFactorySheetEnum.base,"Battery rated Dc-Discharge capacity(w)","电池额定直流放电总功率(w)"),
    batteryRatedChargeCapacity(ExitFactorySheetEnum.base,"Battery rated charge capacity(w)","电池额定充电总功率(w)"),
    batteryRatedAndersonChargeCapacity(ExitFactorySheetEnum.base,"Battery rated Anderson charge capacity(w)","电池额定安德森接口充电总功率(w)"),
    bluetoothProtocolVersion(ExitFactorySheetEnum.base,"Bluetooth protocol version","蓝牙协议版本"),
    motherboardHardwareVersion(ExitFactorySheetEnum.base,"Motherboard hardware version","主板硬件版本"),
    ;
    private final ExitFactorySheetEnum sheetEnum;
    private final String columnEn;
    private final String columnCn;
    ExitFactoryBaseColumnEnum(ExitFactorySheetEnum sheetEnum,String columnEn, String columnCn) {
        this.sheetEnum = sheetEnum;
        this.columnEn = columnEn;
        this.columnCn = columnCn;
    }

    public static Set<String> getColumn(String language) {
        Set<String> result = new LinkedHashSet<>();
        for(ExitFactoryBaseColumnEnum item : ExitFactoryBaseColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.add(item.columnCn);
            } else {
                result.add(item.columnEn);
            }
        }
        return result;
    }

    public static Map<String,ExitFactorySheetEnum> getColumnAndSheetEnum(String language) {
        Map<String,ExitFactorySheetEnum> result = new LinkedHashMap<>();
        for(ExitFactoryBaseColumnEnum item : ExitFactoryBaseColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.put(item.getColumnCn(),item.getSheetEnum());
            } else {
                result.put(item.getColumnEn(),item.getSheetEnum());
            }
        }
        return result;
    }

    public static List<List<String>> getMultipleHeader(String language) {
        List<List<String>> result = new ArrayList<>();
        List<String> header0 = new ArrayList<>();
        List<String> header1 = new ArrayList<>();
        for(ExitFactoryBaseColumnEnum item : ExitFactoryBaseColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                header0.add(item.getSheetEnum().getColumnCn());
                header1.add(item.getColumnCn());
            } else {
                header0.add(item.getSheetEnum().getColumnEn());
                header1.add(item.getColumnEn());
            }
        }
        result.add(header0);
        result.add(header1);
        return result;
    }


}
