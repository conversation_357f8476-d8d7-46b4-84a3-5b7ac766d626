package org.skyworth.ess.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum DictBizCodeEnum {
	PORTABLE_DEVICE_TYPE("portable_device_type","设备类型"),
	PORTABLE_COMPANY("portable_company","公司"),
	PORTABLE_BUY_CHANNEL("portable_buy_channel","销售渠道")
	;
	final String code;
	final String comment;
	DictBizCodeEnum(String code,String comment) {
		this.code = code;
		this.comment = comment;
	}
}
