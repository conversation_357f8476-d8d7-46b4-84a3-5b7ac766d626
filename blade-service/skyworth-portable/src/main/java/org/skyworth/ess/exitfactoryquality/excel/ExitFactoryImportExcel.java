package org.skyworth.ess.exitfactoryquality.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import org.springblade.common.excel.ExcelBusinessUniqueValidate;
import org.springblade.common.excel.ExcelNotNullValidate;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ExitFactoryImportExcel {

    @ColumnWidth(50)
    @ExcelProperty(index =0)
    @ExcelNotNullValidate(message = "inverter Serial Number")
    @ExcelBusinessUniqueValidate(uniqueFlag = true)
    private String deviceSerialNumber;
    @ColumnWidth(30)
    @ExcelProperty(index = 1)
    private String deviceType;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","厂家"})  如果指定了value，则会按照 value和 excel中的列对应映射属性值，如果没设置value，也可以指定index，则按照excel中第一列对应对象的第一个属性
    private String company;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","质保年限"})
    private Integer qualityGuaranteeYear;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","出厂日期"})
    private Date exitFactoryDate;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","电池额定容量(kwh)"})
    private Long batteryRatedCapacity;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","电池额定交流放电总功率(w)"})
    private Long batteryRatedAcDischargeCapacity;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","电池额定放电总功率(w)"})
    private Long batteryRatedDischargeCapacity;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","电池额定直流放电总功率(w)"})
    private Long batteryRatedDcDischargeCapacity;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","电池额定充电总功率(w)"})
    private Long batteryRatedChargeCapacity;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","电池额定安德森接口充电总功率(w)"})
    private Long batteryRatedAndersonChargeCapacity;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","蓝牙协议版本"})
    private String bluetoothProtocolVersion;
    @ColumnWidth(20)
//    @ExcelProperty(value={"基本信息","主板硬件版本"})
    private String motherboardHardwareVersion;
}
