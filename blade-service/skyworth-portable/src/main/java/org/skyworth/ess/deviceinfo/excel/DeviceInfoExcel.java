package org.skyworth.ess.deviceinfo.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 设备信息表 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class DeviceInfoExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备名称")
	private String deviceName;
	/**
	 * 国家
	 */
	@ColumnWidth(20)
	@ExcelProperty("国家")
	private String countryCode;
	/**
	 * 省
	 */
	@ColumnWidth(20)
	@ExcelProperty("省")
	private String provinceCode;
	/**
	 * 城市
	 */
	@ColumnWidth(20)
	@ExcelProperty("城市")
	private String cityCode;
	/**
	 * 区县
	 */
	@ColumnWidth(20)
	@ExcelProperty("区县")
	private String countyCode;
	/**
	 * 详细地址
	 */
	@ColumnWidth(20)
	@ExcelProperty("详细地址")
	private String detailAddress;
	/**
	 * 便携电源SN
	 */
	@ColumnWidth(20)
	@ExcelProperty("便携电源SN")
	private String powerSupplySn;
	/**
	 * 剩余电量百分比，手机端上报
	 */
	@ColumnWidth(20)
	@ExcelProperty("剩余电量百分比，手机端上报")
	private BigDecimal remainingBatteryPercentage;
	/**
	 * 设备型号
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备型号")
	private String equipmentModel;
	/**
	 * 电池循环次数
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池循环次数")
	private Integer numberOfBatteryCycles;
	/**
	 * 电池健康度
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池健康度")
	private Integer batterySoh;
	/**
	 * 创建人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ColumnWidth(20)
	@ExcelProperty("更新人账号")
	private String updateUserAccount;
	/**
	 * 设备状态
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备状态")
	private String deviceStatus;
	/**
	 * 租户ID
	 */
	@ColumnWidth(20)
	@ExcelProperty("租户ID")
	private String tenantId;
	/**
	 * 逻辑删除
	 */
	@ColumnWidth(20)
	@ExcelProperty("逻辑删除")
	private Integer isDeleted;

}
