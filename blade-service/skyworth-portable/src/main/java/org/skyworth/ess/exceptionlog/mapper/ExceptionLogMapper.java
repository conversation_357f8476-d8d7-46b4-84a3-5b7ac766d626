package org.skyworth.ess.exceptionlog.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.exceptionlog.entity.ExceptionLogEntity;
import org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel;

import java.util.List;

/**
 * 异常日志表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
public interface ExceptionLogMapper extends BaseMapper<ExceptionLogEntity> {
	/**
	 * 异常日志每日统计
	 *
	 * @param jsonObject 入参
	 * @return List<JSONObject>
	 * <AUTHOR>
	 * @since 2024/1/26 15:54
	 **/
	List<JSONObject> queryNumberOfDailyAnomalies(@Param("ew") JSONObject jsonObject);

	/**
	 * 查询单日异常设备数量详情
	 *
	 * @param jsonObject 入参
	 * @return List<DeviceActivationAndAlarmExcel>
	 * <AUTHOR>
	 * @since 2024/2/1 13:59
	 **/
	List<DeviceActivationAndAlarmExcel> queryNumberOfDailyAnomaliesExcelInfo(@Param("ew") JSONObject jsonObject);
}
