/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exitfactoryquality.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;


/**
 * 设备出厂信息 Excel实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ExitFactoryBaseExcel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备/逆变器SN
	 */
	@ColumnWidth(20)
//	@ExcelProperty(value ={"基本信息","设备/逆变器SN"})
	@ExcelProperty("设备SN")
	private String deviceSerialNumber;
	@ColumnWidth(20)
//	@ExcelProperty(value ={"基本信息","设备类型"})
	@ExcelProperty("设备类型")
	private String deviceTypeName;
	@ColumnWidth(20)
	@ExcelProperty("厂家")
	private String companyName;
	/**
	 * 质保年限
	 */
	@ColumnWidth(20)
	@ExcelProperty("质保年限")
	private Integer qualityGuaranteeYear;
	/**
	 * 出厂日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("出厂日期")
	private String exitFactoryDate;
	/**
//	 * 激活日期
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("激活日期")
//	private String activationDate;
//	/**
//	 * 质保开始日期
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("质保开始日期")
//	private String qualityGuaranteeBeginDate;
//	@ColumnWidth(20)
//	@ExcelProperty("质保截止日期")
//	private String qualityGuaranteeEndDate;
//	@ColumnWidth(20)
//	@ExcelProperty("销售渠道")
//	private String buyChannelName;
//	/**
//	 * 购买日期
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("购买日期")
//	private String buyDate;
//	/**
//	 * 订单编号
//	 */
//	@ColumnWidth(20)
//	@ExcelProperty("订单编号")
//	private String buyOrderNumber;
//
//
//	@ColumnWidth(20)
//	@ExcelProperty("用户名")
//	private String buyerUserName;
//	@ColumnWidth(20)
//	@ExcelProperty("用户手机")
//	private String buyerUserPhone;

	/**
	 * 电池额定容量kwh
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池额定容量kwh")
	private Long batteryRatedCapacity;
	/**
	 * 电池额定交流放电总功率w
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池额定交流放电总功率w")
	private Long batteryRatedAcDischargeCapacity;
	/**
	 * 电池额定放电总功率w
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池额定放电总功率w")
	private Long batteryRatedDischargeCapacity;
	/**
	 * 电池额定直流放电总功率w
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池额定直流放电总功率w")
	private Long batteryRatedDcDischargeCapacity;
	/**
	 * 电池额定充电总功率w
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池额定充电总功率w")
	private Long batteryRatedChargeCapacity;
	/**
	 * 电池额定安德森接口充电总功率w
	 */
	@ColumnWidth(20)
	@ExcelProperty("电池额定安德森接口充电总功率w")
	private Long batteryRatedAndersonChargeCapacity;
	/**
	 * 蓝牙协议版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("蓝牙协议版本")
	private String bluetoothProtocolVersion;
	/**
	 * 主板硬件版本
	 */
	@ColumnWidth(20)
	@ExcelProperty("主板硬件版本")
	private String motherboardHardwareVersion;
//	/**
//	 * 销售渠道：数据字典 portable_buy_channel
//	 */
//	private String buyChannel;



}
