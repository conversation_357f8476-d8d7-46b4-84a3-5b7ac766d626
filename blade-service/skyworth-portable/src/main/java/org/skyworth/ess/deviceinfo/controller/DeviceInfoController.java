package org.skyworth.ess.deviceinfo.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.deviceinfo.service.IDeviceInfoService;
import org.skyworth.ess.deviceinfo.vo.DeviceInfoVO;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

/**
 * 设备信息表 控制器
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("deviceInfo")
@Api(value = "设备信息表", tags = "设备信息表接口")
public class DeviceInfoController extends BladeController {

	private final IDeviceInfoService deviceInfoService;

	/**
	 * 设备信息表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入deviceInfo")
	public R<JSONObject> detail(@RequestParam("id") Long id) {
		if (id == null) {
			throw new BusinessException("parameter.notEmpty");
		}
		return R.data(deviceInfoService.detail(id));
	}

	/**
	 * 设备信息表 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入deviceInfo")
	public R<IPage<DeviceInfoVO>> queryPage(@ApiIgnore @RequestParam Map<String, Object> deviceInfo, Query query) {
		IPage<DeviceInfoVO> pages = deviceInfoService.queryPage(Condition.getPage(query), deviceInfo);
		return R.data(pages);
	}

	/**
	 * 分页统计
	 */
	@GetMapping("/pageStatistics")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入deviceInfo")
	public R<JSONObject> pageStatistics(@ApiIgnore @RequestParam Map<String, Object> deviceInfo) {
		return R.data(deviceInfoService.queryPageStatistics(deviceInfo));
	}
}
