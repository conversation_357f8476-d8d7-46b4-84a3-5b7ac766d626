package org.skyworth.ess.report.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-02-01 17:43
 **/
@Data
public class BaseColumn {
	/**
	 * 国家
	 */
	@ColumnWidth(30)
	@ExcelProperty(value = "countryName",index = 2)
	private String countryName;
	/**
	 * 一级行政区划
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "provinceName",index = 3)
	private String provinceName;
	/**
	 * 二级行政区划
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "cityName",index = 4)
	private String cityName;

	/**
	 * 分组唯一键
	 */
	@ExcelIgnore
	private String groupUniqueKey;

	/**
	 * 统计日期
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "statisticalDate",index = 5)
	private String statisticalDate;
}
