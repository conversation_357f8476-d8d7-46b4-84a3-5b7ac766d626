/*
 * Copyright (c) 2023. skyworth All rights reserved
 */

package org.skyworth.ess.feigin;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.skyworth.ess.deviceinfo.service.IDeviceInfoService;
import org.skyworth.ess.feign.IPotableClient;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 设备端feign
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-09-22 14:19
 **/
@ApiIgnore
@RestController
@AllArgsConstructor
@Slf4j
public class PortableClient implements IPotableClient {
	private final IDeviceInfoService deviceInfoService;

	@PostMapping(PORTABLE_DEVICE_POSITION)
	@Override
	public R<Boolean> maintenanceDeviceLocation(@RequestBody JSONObject jsonObject) {
		Long userId = jsonObject.getLong("userId");
		if (userId == null) {
			jsonObject.put("userId", AuthUtil.getUserId());
		}
		deviceInfoService.updateDeviceDistrict(jsonObject);
		return R.status(true);
	}
}
