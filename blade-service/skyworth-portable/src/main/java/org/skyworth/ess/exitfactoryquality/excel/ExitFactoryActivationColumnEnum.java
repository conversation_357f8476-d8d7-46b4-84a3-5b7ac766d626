package org.skyworth.ess.exitfactoryquality.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.*;

/**
 * <AUTHOR>
 */

@Getter
public enum ExitFactoryActivationColumnEnum {
    deviceSerialNumber(ExitFactorySheetEnum.base,"Device SN","设备SN"),
    deviceTypeName(ExitFactorySheetEnum.base,"Device type","设备型号"),
    activationDate(ExitFactorySheetEnum.activation,"Activation date","激活日期"),
    qualityGuaranteeBeginDate(ExitFactorySheetEnum.activation,"Warranty start date","质保开始日期"),
    qualityGuaranteeEndDate(ExitFactorySheetEnum.activation,"Warranty deadline","质保截止日期"),
    ;
    private final ExitFactorySheetEnum sheetEnum;
    private final String columnEn;
    private final String columnCn;
    ExitFactoryActivationColumnEnum(ExitFactorySheetEnum sheetEnum,String columnEn, String columnCn) {
        this.sheetEnum = sheetEnum;
        this.columnEn = columnEn;
        this.columnCn = columnCn;
    }

    public static Set<String> getColumn(String language) {
        Set<String> result = new LinkedHashSet<>();
        for(ExitFactoryActivationColumnEnum item : ExitFactoryActivationColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.add(item.columnCn);
            } else {
                result.add(item.columnEn);
            }
        }
        return result;
    }

    public static Map<String,ExitFactorySheetEnum> getColumnAndSheetEnum(String language) {
        Map<String,ExitFactorySheetEnum> result = new LinkedHashMap<>();
        for(ExitFactoryActivationColumnEnum item : ExitFactoryActivationColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.put(item.getColumnCn(),item.getSheetEnum());
            } else {
                result.put(item.getColumnEn(),item.getSheetEnum());
            }
        }
        return result;
    }
}
