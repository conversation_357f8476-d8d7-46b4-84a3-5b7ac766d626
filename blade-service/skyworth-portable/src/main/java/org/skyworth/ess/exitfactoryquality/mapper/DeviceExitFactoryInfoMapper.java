/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.exitfactoryquality.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity;
import org.skyworth.ess.exitfactoryquality.vo.DeviceExitFactoryInfoVO;
import org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel;

import java.util.List;

/**
 * 设备出厂信息 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
public interface DeviceExitFactoryInfoMapper extends BaseMapper<DeviceExitFactoryInfoEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page                  分页
	 * @param deviceExitFactoryInfo 查询对象
	 * @return 集合
	 */
	List<DeviceExitFactoryInfoVO> selectDeviceExitFactoryInfoPage(IPage page, DeviceExitFactoryInfoVO deviceExitFactoryInfo);


	/**
	 * 获取导出数据
	 *
	 * @param queryWrapper 查询对象
	 * @return 集合
	 */
	List<DeviceExitFactoryInfoEntity> exportDeviceExitFactoryInfo(@Param("ew") Wrapper<DeviceExitFactoryInfoEntity> queryWrapper);

	/**
	 * 查询每日激活数
	 *
	 * @param jsonObject 入参
	 * @return List<JSONObject>
	 * <AUTHOR>
	 * @since 2024/1/26 13:46
	 **/
	List<JSONObject> queryDeviceActivationsInfo(@Param("ew") JSONObject jsonObject);

	List<DeviceExitFactoryInfoEntity> queryByDeviceSerialNumbers(@Param("list") List<String> list);

	int updateBatchBySn(@Param("list") List<DeviceExitFactoryInfoEntity> list);

	/**
	 * 查询每日激活数详情
	 *
	 * @param jsonObject 入参
	 * @return List<JSONObject>
	 * <AUTHOR>
	 * @since 2024/1/26 13:46
	 **/
	List<DeviceActivationAndAlarmExcel> queryDeviceActivationsExcelInfo(@Param("ew") JSONObject jsonObject);
	List<DeviceExitFactoryInfoVO> queryQualityGuaranteeListBySelf(@Param("vo")DeviceExitFactoryInfoVO vo);
}
