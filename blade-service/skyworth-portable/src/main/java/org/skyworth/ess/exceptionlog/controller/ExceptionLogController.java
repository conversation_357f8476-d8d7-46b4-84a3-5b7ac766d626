package org.skyworth.ess.exceptionlog.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.exceptionlog.entity.ExceptionLogEntity;
import org.skyworth.ess.exceptionlog.service.IExceptionLogService;
import org.skyworth.ess.exceptionlog.vo.ExceptionLogVO;
import org.skyworth.ess.exceptionlog.wrapper.ExceptionLogWrapper;
import org.springblade.common.utils.CollectionUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.log.exception.BusinessException;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 异常日志表 控制器
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("exceptionLog")
@Api(value = "异常日志表", tags = "异常日志表接口")
public class ExceptionLogController extends BladeController {

	private final IExceptionLogService exceptionLogService;


	/**
	 * 异常日志表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入exceptionLog")
	public R<IPage<ExceptionLogVO>> list(@ApiIgnore @RequestParam Map<String, Object> exceptionLog, Query query) {
		IPage<ExceptionLogEntity> pages = exceptionLogService.page(Condition.getPage(query), Condition.getQueryWrapper(exceptionLog, ExceptionLogEntity.class).orderByDesc("create_time"));
		return R.data(ExceptionLogWrapper.build().pageVO(pages));
	}




	/**
	 * 异常日志表 新增
	 */
	@PostMapping("/saveBatch")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "批量新增", notes = "传入exceptionLog集合")
	public R saveBatch(@Valid @RequestBody List<ExceptionLogEntity> exceptionLogEntityList) {
		if (CollectionUtils.isNullOrEmpty(exceptionLogEntityList)) {
			throw new BusinessException("parameter.notEmpty");
		}
		return R.status(exceptionLogService.saveBatch(exceptionLogEntityList));
	}
}
