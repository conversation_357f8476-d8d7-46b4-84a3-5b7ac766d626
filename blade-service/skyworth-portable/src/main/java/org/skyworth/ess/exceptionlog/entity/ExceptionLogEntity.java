package org.skyworth.ess.exceptionlog.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

/**
 * 异常日志表 实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@TableName("device_exception_log")
@ApiModel(value = "ExceptionLog对象", description = "异常日志表")
@EqualsAndHashCode(callSuper = true)
public class ExceptionLogEntity extends TenantEntity {

	/**
	 * 设备ID
	 */
	@ApiModelProperty(value = "设备ID")
	private Long deviceInfoId;
	/**
	 * 设备SN号
	 */
	@ApiModelProperty(value = "设备SN号")
	private String serialNumber;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;
	/**
	 * 异常消息
	 */
	@ApiModelProperty(value = "异常消息")
	private String exceptionMessage;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;

}
