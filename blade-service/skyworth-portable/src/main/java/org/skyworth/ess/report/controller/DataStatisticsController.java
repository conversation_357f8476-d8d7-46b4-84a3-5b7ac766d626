package org.skyworth.ess.report.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.skyworth.ess.report.service.IDataStatisticsService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * 数据统计
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("dataStatistics")
@Api(value = "异常日志表", tags = "异常日志表接口")
public class DataStatisticsController extends BladeController {
	private final IDataStatisticsService dataStatisticsService;

	/**
	 * 设备激活报表
	 */
	@PostMapping("/deviceReport")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "设备报表", notes = "传入jsonObject")
	public R<JSONObject> deviceReport(@RequestBody JSONObject jsonObject) {
		return R.data(dataStatisticsService.deviceReport(jsonObject));
	}

	/**
	 * 设备告警报表
	 */
	@PostMapping("/userReport")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "用户报表", notes = "传入jsonObject")
	public R<JSONObject> userReport(@RequestBody JSONObject jsonObject) {
		return R.data(dataStatisticsService.userReport(jsonObject));
	}

	/**
	 * 导出激活数据
	 */
	@GetMapping("/export-deviceReport")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "导出设备激活数据+告警数据", notes = "传入jsonObject")
	public void exportDeviceReport(@ApiIgnore @RequestParam Map<String, Object> map, HttpServletResponse response) throws Exception {
		dataStatisticsService.exportDeviceReport(map, response);
	}

	/**
	 * 导出用户注册数据
	 */
	@GetMapping("/export-userReport")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "导出用户注册数据", notes = "传入jsonObject")
	public void exportUserReport(@ApiIgnore @RequestParam Map<String, Object> map, HttpServletResponse response) throws IOException {
		dataStatisticsService.exportUserReport(map, response);
	}


}
