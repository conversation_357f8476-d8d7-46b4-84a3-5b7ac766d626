<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.deviceinfo.mapper.DeviceInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceInfoResultMap" type="org.skyworth.ess.deviceinfo.entity.DeviceInfoEntity">
        <result column="id" property="id"/>
        <result column="device_name" property="deviceName"/>
        <result column="country_code" property="countryCode"/>
        <result column="province_code" property="provinceCode"/>
        <result column="city_code" property="cityCode"/>
        <result column="county_code" property="countyCode"/>
        <result column="power_supply_sn" property="powerSupplySn"/>
        <result column="remaining_battery_percentage" property="remainingBatteryPercentage"/>
        <result column="equipment_model" property="equipmentModel"/>
        <result column="number_of_battery_cycles" property="numberOfBatteryCycles"/>
        <result column="battery_soh" property="batterySoh"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="device_status" property="deviceStatus"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="queryPage" resultType="org.skyworth.ess.deviceinfo.vo.DeviceInfoVO">
        select
        di.id,
        di.power_supply_sn as powerSupplySn,
        di.country_code as countryCode,
        di.province_code as provinceCode,
        di.city_code as cityCode,
        if(defi.quality_guarantee_begin_date is null, 'no', 'yes') as warrantyActivationStatus,
        defi.battery_rated_capacity as batteryRatedCapacity,
        di.create_user as createUser
        <include refid="page_query_condition"/>
        order by di.id desc
    </select>

    <sql id="page_query_condition">
        from
        device_info di
        left join device_exit_factory_info defi on
        di.power_supply_sn = defi.device_serial_number
        where
        di.is_deleted = 0
        and defi.is_deleted = 0
        <if test="deviceMap.countryCode !='' and deviceMap.countryCode !=null">
            and di.country_code = #{deviceMap.countryCode}
        </if>
        <if test="deviceMap.provinceCode !='' and deviceMap.provinceCode !=null">
            and di.province_code = #{deviceMap.provinceCode}
        </if>
        <if test="deviceMap.cityCode !='' and deviceMap.cityCode !=null">
            and di.city_code = #{deviceMap.cityCode}
        </if>
        <if test="deviceMap.batteryRatedCapacity !='' and deviceMap.batteryRatedCapacity !=null">
            and defi.battery_rated_capacity = #{deviceMap.batteryRatedCapacity}
        </if>
        <if test="deviceMap.powerSupplySn !='' and deviceMap.powerSupplySn !=null">
            and di.power_supply_sn like concat('%', #{deviceMap.powerSupplySn},'%')
        </if>
    </sql>

    <select id="queryPageStatistics" resultType="com.alibaba.fastjson.JSONObject">
        select
        sum(a.batteryRatedCapacity) as sumBatteryRatedCapacity,
        sum(num) as activatedDevicesNums
        from
        (
        select
        ifnull(defi.battery_rated_capacity, 0) as batteryRatedCapacity,
        1 as num
        <include refid="page_query_condition"/>
        and defi.quality_guarantee_begin_date is not null
        )a
    </select>

    <select id="selectDeviceByUserAndSn" resultMap="deviceInfoResultMap">
        select di.id,di.is_deleted from device_info di
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="userId != null">
                and di.create_user = #{userId}
            </if>
            <if test="powerSupplySn != null and powerSupplySn != ''">
                and di.power_supply_sn = #{powerSupplySn}
            </if>
        </trim>
    </select>

    <select id="appQueryPage" resultType="org.skyworth.ess.deviceinfo.vo.DeviceInfoVO">
        select di.id,
               di.device_name                  as deviceName,
               di.equipment_model              as equipmentModel,
               di.remaining_battery_percentage as remainingBatteryPercentage,
               di.power_supply_sn              as powerSupplySn,
               di.create_user                  as createUser
        from device_info di
        where di.is_deleted = 0
          and di.create_user = #{deviceMap.userId}
    </select>

    <update id="updateDeviceDistrict">
        update
            device_info di
        set
            di.country_code = #{json.countryCode},
            di.province_code = #{json.provinceCode},
            di.city_code = #{json.cityCode},
            di.update_time = now()
        where
            di.create_user = #{json.userId}
    </update>

    <update id="stateRestoration">
        update
        device_info
        set
        is_deleted = 0,
        update_time = now()
        where
        is_deleted = 1
        and id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>
