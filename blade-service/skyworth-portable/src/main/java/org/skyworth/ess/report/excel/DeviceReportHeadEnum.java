package org.skyworth.ess.report.excel;

import lombok.Getter;
import org.skyworth.ess.exitfactoryquality.excel.ExitFactorySheetEnum;
import org.springblade.common.constant.CommonConstant;

import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum DeviceReportHeadEnum {
	/**
	 * 设备型号
	 */
	equipmentModel("Equipment model", "设备型号"),
	/**
	 * 国家
	 */
	countryName("Country", "国家"),
	/**
	 * 一级行政区域
	 */
	provinceName("First level administrative region", "一级行政区域"),
	/**
	 * 二级行政区域
	 */
	cityName("Secondary administrative region", "二级行政区域"),
	/**
	 * 日期
	 */
	statisticalDate("Date", "日期"),
	/**
	 * 设备激活数
	 */
	numberOfActivations("Number of device activations", "设备激活数"),
	/**
	 * 设备告警数
	 */
	numberOfFaults("Number of device alarms", "设备告警数");
	private final String columnEn;
	private final String columnCn;

	DeviceReportHeadEnum(String columnEn, String columnCn) {
		this.columnEn = columnEn;
		this.columnCn = columnCn;
	}

	public static Set<String> getColumn(String language) {
		Set<String> result = new LinkedHashSet<>();
		for (DeviceReportHeadEnum item : DeviceReportHeadEnum.values()) {
			if (CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
				result.add(item.columnCn);
			} else {
				result.add(item.columnEn);
			}
		}
		return result;
	}
}
