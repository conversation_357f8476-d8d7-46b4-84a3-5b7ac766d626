/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.report.vo;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 物理地址映射属性名称 Excel实体类
 *
 * <AUTHOR>
 * @since 2023-09-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ColumnWidth(35)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class UserRegistrationExcel extends BaseColumn implements Serializable {

	private static final long serialVersionUID = 1L;


	@ColumnWidth(40)
	@ExcelProperty(value="numberOfAppUserRegistrations",index = 6)
	private Integer numberOfAppUserRegistrations = 0;


}
