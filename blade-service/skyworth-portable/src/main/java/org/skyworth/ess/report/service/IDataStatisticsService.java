package org.skyworth.ess.report.service;

import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * @description:
 * @author: SDT50545
 * @since: 2024-01-26 11:08
 **/
public interface IDataStatisticsService {
	/**
	 * 设备激活数/累计设备激活数
	 * 设备告警数/累计设备告警数
	 *
	 * @param jsonObject 入参
	 * @return JSONObject
	 * <AUTHOR>
	 * @since 2024/1/26 11:30
	 **/
	JSONObject deviceReport(JSONObject jsonObject);

	/**
	 * app注册数/app累计用户
	 *
	 * @param jsonObject 入参
	 * @return JSONObject
	 * <AUTHOR>
	 * @since 2024/1/26 11:30
	 **/
	JSONObject userReport(JSONObject jsonObject);

	/**
	 * 导出激活数据
	 *
	 * @param map      入参
	 * @param response 入参
	 * @throws Exception  异常
	 * <AUTHOR>
	 * @since 2024/1/31 11:06
	 **/
	void exportDeviceReport(Map<String, Object> map, HttpServletResponse response) throws Exception;

	/**
	 * 导出用户注册数据
	 *
	 * @param map      入参
	 * @param response 入参
	 * @throws IOException 异常
	 * <AUTHOR>
	 * @since 2024/1/31 11:06
	 **/
	void exportUserReport(Map<String, Object> map, HttpServletResponse response) throws IOException;
}
