package org.skyworth.ess.deviceinfo.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.deviceinfo.entity.DeviceInfoEntity;
import org.skyworth.ess.deviceinfo.vo.DeviceInfoVO;
import java.util.Objects;

/**
 * 设备信息表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
public class DeviceInfoWrapper extends BaseEntityWrapper<DeviceInfoEntity, DeviceInfoVO>  {

	public static DeviceInfoWrapper build() {
		return new DeviceInfoWrapper();
 	}

	@Override
	public DeviceInfoVO entityVO(DeviceInfoEntity deviceInfo) {
		DeviceInfoVO deviceInfoVO = Objects.requireNonNull(BeanUtil.copy(deviceInfo, DeviceInfoVO.class));

		//User createUser = UserCache.getUser(deviceInfo.getCreateUser());
		//User updateUser = UserCache.getUser(deviceInfo.getUpdateUser());
		//deviceInfoVO.setCreateUserName(createUser.getName());
		//deviceInfoVO.setUpdateUserName(updateUser.getName());

		return deviceInfoVO;
	}


}
