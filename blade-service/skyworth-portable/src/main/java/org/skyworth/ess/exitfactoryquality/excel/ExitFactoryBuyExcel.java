package org.skyworth.ess.exitfactoryquality.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExitFactoryBuyExcel {
//	@ExcelIgnore
    private String deviceSerialNumber;
    private String deviceTypeName;
    @ExcelProperty("销售渠道")
	private String buyChannelName;
	/**
	 * 购买日期
	 */
	@ColumnWidth(20)
	@ExcelProperty("购买日期")
	private String buyDate;
	/**
	 * 订单编号
	 */
	@ColumnWidth(20)
	@ExcelProperty("订单编号")
	private String buyOrderNumber;


	@ColumnWidth(20)
	@ExcelProperty("用户名")
	private String buyerUserName;
	@ColumnWidth(20)
	@ExcelProperty("用户手机")
	private String buyerUserPhone;
}
