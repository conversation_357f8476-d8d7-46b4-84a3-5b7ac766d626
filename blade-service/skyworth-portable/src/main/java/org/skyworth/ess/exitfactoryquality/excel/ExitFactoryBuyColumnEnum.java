package org.skyworth.ess.exitfactoryquality.excel;

import lombok.Getter;
import org.springblade.common.constant.CommonConstant;

import java.util.*;

/**
 * <AUTHOR>
 */

@Getter
public enum ExitFactoryBuyColumnEnum {
    deviceSerialNumber(ExitFactorySheetEnum.base,"Device SN","设备SN"),
    deviceTypeName(ExitFactorySheetEnum.base,"Device type","设备类型"),
    buyChannelName(ExitFactorySheetEnum.buy,"Distribution channel","销售渠道"),
    buyDate(ExitFactorySheetEnum.buy,"Purchasing  date","购买日期"),
    buyOrderNumber(ExitFactorySheetEnum.buy,"Order number","订单编号"),
    buyerUserName(ExitFactorySheetEnum.buy,"User name","用户名"),
    buyerUserPhone(ExitFactorySheetEnum.buy,"User phone","用户手机"),


    ;
    private final ExitFactorySheetEnum sheetEnum;
    private final String columnEn;
    private final String columnCn;
    ExitFactoryBuyColumnEnum(ExitFactorySheetEnum sheetEnum,String columnEn, String columnCn) {
        this.sheetEnum = sheetEnum;
        this.columnEn = columnEn;
        this.columnCn = columnCn;
    }

    public static Set<String> getColumn(String language) {
        Set<String> result = new LinkedHashSet<>();
        for(ExitFactoryBuyColumnEnum item : ExitFactoryBuyColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.add(item.columnCn);
            } else {
                result.add(item.columnEn);
            }
        }
        return result;
    }

    public static Map<String,ExitFactorySheetEnum> getColumnAndSheetEnum(String language) {
        Map<String,ExitFactorySheetEnum> result = new LinkedHashMap<>();
        for(ExitFactoryBuyColumnEnum item : ExitFactoryBuyColumnEnum.values()) {
            if(CommonConstant.CURRENT_LANGUAGE_ZH.equalsIgnoreCase(language)) {
                result.put(item.getColumnCn(),item.getSheetEnum());
            } else {
                result.put(item.getColumnEn(),item.getSheetEnum());
            }
        }
        return result;
    }
}
