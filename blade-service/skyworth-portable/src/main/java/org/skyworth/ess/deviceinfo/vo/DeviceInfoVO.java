package org.skyworth.ess.deviceinfo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.skyworth.ess.deviceinfo.entity.DeviceInfoEntity;

/**
 * 设备信息表 视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceInfoVO extends DeviceInfoEntity {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "质保激活状态")
	private String warrantyActivationStatus;

	@ApiModelProperty(value = "所属地区")
	private String district;

	@ApiModelProperty(value = "电池额定容量")
	private Long batteryRatedCapacity;

	@ApiModelProperty(value = "用户姓名")
	private String userName;

	@ApiModelProperty(value = "用户手机号")
	private String userPhone;

}
