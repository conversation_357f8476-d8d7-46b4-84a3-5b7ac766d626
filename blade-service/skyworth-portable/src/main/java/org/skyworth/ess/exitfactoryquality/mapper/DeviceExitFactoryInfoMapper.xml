<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.skyworth.ess.exitfactoryquality.mapper.DeviceExitFactoryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceExitFactoryInfoResultMap" type="org.skyworth.ess.exitfactoryquality.entity.DeviceExitFactoryInfoEntity">
        <result column="id" property="id"/>
        <result column="device_serial_number" property="deviceSerialNumber"/>
        <result column="device_type" property="deviceType"/>
        <result column="company" property="company"/>
        <result column="quality_guarantee_year" property="qualityGuaranteeYear"/>
        <result column="exit_factory_date" property="exitFactoryDate"/>
        <result column="activation_date" property="activationDate"/>
        <result column="quality_guarantee_begin_date" property="qualityGuaranteeBeginDate"/>
        <result column="battery_rated_capacity" property="batteryRatedCapacity"/>
        <result column="battery_rated_ac_discharge_capacity" property="batteryRatedAcDischargeCapacity"/>
        <result column="battery_rated_discharge_capacity" property="batteryRatedDischargeCapacity"/>
        <result column="battery_rated_dc_discharge_capacity" property="batteryRatedDcDischargeCapacity"/>
        <result column="battery_rated_charge_capacity" property="batteryRatedChargeCapacity"/>
        <result column="battery_rated_anderson_charge_capacity" property="batteryRatedAndersonChargeCapacity"/>
        <result column="bluetooth_protocol_version" property="bluetoothProtocolVersion"/>
        <result column="motherboard_hardware_version" property="motherboardHardwareVersion"/>
        <result column="buy_channel" property="buyChannel"/>
        <result column="buy_date" property="buyDate"/>
        <result column="buy_order_number" property="buyOrderNumber"/>
        <result column="buy_certificate_img_biz_key" property="buyCertificateImgBizKey"/>
        <result column="buyer_user_id" property="buyerUserId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_account" property="createUserAccount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_account" property="updateUserAccount"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="selectDeviceExitFactoryInfoPage" resultMap="voResultMap">
        select * from device_exit_factory_info where is_deleted = 0
        <if test="param2.deviceSerialNumber != null and param2.deviceSerialNumber != ''">
            and device_serial_number  like CONCAT('%', #{param2.deviceSerialNumber},'%')
        </if>
        <if test="param2.company != null and param2.company != ''">
            and company  = #{param2.company}
        </if>
        <if test="param2.deviceType != null and param2.deviceType != ''">
            and device_type  = #{param2.deviceType}
        </if>
    </select>


    <select id="exportDeviceExitFactoryInfo" resultMap="deviceExitFactoryInfoResultMap">
        SELECT * FROM device_exit_factory_info ${ew.customSqlSegment}
    </select>
    <resultMap id="voResultMap" type="org.skyworth.ess.exitfactoryquality.vo.DeviceExitFactoryInfoVO">
    </resultMap>

    <select id="queryDeviceActivationsInfo" resultType="com.alibaba.fastjson.JSONObject">
        select defi.quality_guarantee_begin_date as activationDate,
        count(1) as quantity
        from device_exit_factory_info defi
        left join device_info di on
        defi.device_serial_number = di.power_supply_sn
        and di.is_deleted = 0
        where defi.is_deleted = 0
        and defi.quality_guarantee_begin_date is not null
        <if test="ew.deviceType != null and ew.deviceType!=''">
            and defi.device_type = #{ew.deviceType}
        </if>
        <if test="ew.countryCode != null and ew.countryCode!=''">
            and di.country_code = #{ew.countryCode}
        </if>
        <if test="ew.provinceCode != null and ew.provinceCode!=''">
            and di.province_code = #{ew.provinceCode}
        </if>
        <if test="ew.cityCode != null and ew.cityCode!=''">
            and di.city_code = #{ew.cityCode}
        </if>
        <if test="ew.beginDate != null and ew.beginDate!=''">
            and defi.quality_guarantee_begin_date <![CDATA[ >= ]]> #{ew.beginDate}
        </if>
        <if test="ew.endDate != null and ew.endDate!=''">
            and defi.quality_guarantee_begin_date <![CDATA[ <= ]]> #{ew.endDate}
        </if>
        group by defi.quality_guarantee_begin_date
    </select>

    <select id="queryByDeviceSerialNumbers" resultMap="deviceExitFactoryInfoResultMap">
        SELECT id, device_serial_number, device_type, company, quality_guarantee_year, exit_factory_date
        FROM device_exit_factory_info where is_deleted = 0
        and device_serial_number in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateBatchBySn" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update device_exit_factory_info set
            <if test="item.deviceType!=null and item.deviceType!=''">
                device_type=#{item.deviceType},
            </if>
            <if test="item.company!=null and item.company!=''">
                company=#{item.company},
            </if>
            <if test="item.qualityGuaranteeYear!=null ">
                quality_guarantee_year=#{item.qualityGuaranteeYear},
            </if>
            <if test="item.exitFactoryDate!=null ">
                exit_factory_date=#{item.exitFactoryDate},
            </if>
            <if test="item.batteryRatedCapacity!=null ">
                battery_rated_capacity=#{item.batteryRatedCapacity},
            </if>
            <if test="item.batteryRatedAcDischargeCapacity!=null ">
                battery_rated_ac_discharge_capacity=#{item.batteryRatedAcDischargeCapacity},
            </if>
            <if test="item.batteryRatedDischargeCapacity!=null ">
                battery_rated_discharge_capacity=#{item.batteryRatedDischargeCapacity},
            </if>
            <if test="item.batteryRatedDcDischargeCapacity!=null ">
                battery_rated_dc_discharge_capacity=#{item.batteryRatedDcDischargeCapacity},
            </if>
            <if test="item.batteryRatedChargeCapacity!=null ">
                battery_rated_charge_capacity=#{item.batteryRatedChargeCapacity},
            </if>
            <if test="item.batteryRatedAndersonChargeCapacity!=null ">
                battery_rated_anderson_charge_capacity=#{item.batteryRatedAndersonChargeCapacity},
            </if>
            <if test="item.bluetoothProtocolVersion!=null and item.bluetoothProtocolVersion!=''">
                bluetooth_protocol_version=#{item.bluetoothProtocolVersion},
            </if>
            <if test="item.motherboardHardwareVersion!=null and item.motherboardHardwareVersion!=''">
                motherboard_hardware_version=#{item.motherboardHardwareVersion},
            </if>

            <if test="item.updateUserAccount!=null and item.updateUserAccount!=''">
                update_user_account=#{item.updateUserAccount},
            </if>
            <if test="item.updateUser!=null and item.updateUser!=''">
                update_user=#{item.updateUser},
            </if>
            <if test="item.status!=null ">
                status=#{item.status},
            </if>
            update_time=now()
            where device_serial_number = #{item.deviceSerialNumber}
        </foreach>
    </update>

    <select id="queryDeviceActivationsExcelInfo" resultType="org.skyworth.ess.report.vo.DeviceActivationAndAlarmExcel">
        select concat(defi.device_type,
        '@',ifnull(di.country_code,''),'@',ifnull(di.province_code,''),'@',ifnull(di.city_code,''),'@',defi.quality_guarantee_begin_date)
        as groupUniqueKey,
        count(1) as numberOfActivations
        from device_exit_factory_info defi
        left join device_info di on
        defi.device_serial_number = di.power_supply_sn
        and di.is_deleted = 0
        where defi.is_deleted = 0
        and defi.quality_guarantee_begin_date is not null
        <if test="ew.deviceType != null and ew.deviceType!=''">
            and defi.device_type = #{ew.deviceType}
        </if>
        <if test="ew.countryCode != null and ew.countryCode!=''">
            and di.country_code = #{ew.countryCode}
        </if>
        <if test="ew.provinceCode != null and ew.provinceCode!=''">
            and di.province_code = #{ew.provinceCode}
        </if>
        <if test="ew.cityCode != null and ew.cityCode!=''">
            and di.city_code = #{ew.cityCode}
        </if>
        <if test="ew.beginDate != null and ew.beginDate!=''">
            and defi.quality_guarantee_begin_date <![CDATA[ >= ]]> #{ew.beginDate}
        </if>
        <if test="ew.endDate != null and ew.endDate!=''">
            and defi.quality_guarantee_begin_date <![CDATA[ <= ]]> #{ew.endDate}
        </if>
        group by groupUniqueKey
    </select>
    <select id="queryQualityGuaranteeListBySelf" resultType="org.skyworth.ess.exitfactoryquality.vo.DeviceExitFactoryInfoVO">
        select f.device_serial_number ,f.buy_date ,f.device_type  from device_info d
        inner join device_exit_factory_info f on d.power_supply_sn =f.device_serial_number and f.is_deleted = 0
        where d.is_deleted =0 and d.create_user =  #{vo.createUser}
    </select>
</mapper>
