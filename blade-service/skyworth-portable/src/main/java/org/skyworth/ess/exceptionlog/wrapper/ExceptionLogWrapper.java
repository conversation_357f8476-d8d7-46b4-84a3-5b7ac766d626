package org.skyworth.ess.exceptionlog.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.skyworth.ess.exceptionlog.entity.ExceptionLogEntity;
import org.skyworth.ess.exceptionlog.vo.ExceptionLogVO;
import java.util.Objects;

/**
 * 异常日志表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
public class ExceptionLogWrapper extends BaseEntityWrapper<ExceptionLogEntity, ExceptionLogVO>  {

	public static ExceptionLogWrapper build() {
		return new ExceptionLogWrapper();
 	}

	@Override
	public ExceptionLogVO entityVO(ExceptionLogEntity exceptionLog) {
		ExceptionLogVO exceptionLogVO = Objects.requireNonNull(BeanUtil.copy(exceptionLog, ExceptionLogVO.class));

		//User createUser = UserCache.getUser(exceptionLog.getCreateUser());
		//User updateUser = UserCache.getUser(exceptionLog.getUpdateUser());
		//exceptionLogVO.setCreateUserName(createUser.getName());
		//exceptionLogVO.setUpdateUserName(updateUser.getName());

		return exceptionLogVO;
	}


}
