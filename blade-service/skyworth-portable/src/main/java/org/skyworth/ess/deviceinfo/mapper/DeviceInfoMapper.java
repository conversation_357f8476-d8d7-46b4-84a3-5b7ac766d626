package org.skyworth.ess.deviceinfo.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.skyworth.ess.deviceinfo.entity.DeviceInfoEntity;
import org.skyworth.ess.deviceinfo.vo.DeviceInfoVO;

import java.util.List;
import java.util.Map;

/**
 * 设备信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
public interface DeviceInfoMapper extends BaseMapper<DeviceInfoEntity> {
	/**
	 * 分页查询
	 *
	 * @param deviceMap 查询条件
	 * @param page      入参
	 * @return List<DeviceInfoEntity>
	 * <AUTHOR>
	 * @since 2024/1/24 9:56
	 **/
	List<DeviceInfoVO> queryPage(IPage<DeviceInfoVO> page, Map<String, Object> deviceMap);

	/**
	 * 分页查询-数据统计
	 *
	 * @param deviceMap 入参
	 * @return JSONObject
	 * <AUTHOR>
	 * @since 2024/1/24 16:43
	 **/
	JSONObject queryPageStatistics(@Param("deviceMap") Map<String, Object> deviceMap);

	/**
	 * 根据sn查询设备信息
	 *
	 * @param userId        用户id
	 * @param powerSupplySn 入参
	 * @return List<DeviceInfoEntity>
	 * <AUTHOR>
	 * @since 2024/1/24 17:33
	 **/
	List<DeviceInfoEntity> selectDeviceByUserAndSn(@Param("userId") Long userId, @Param("powerSupplySn") String powerSupplySn);

	/**
	 * app端查询设备信息
	 *
	 * @param page      分页参数
	 * @param deviceMap 入参
	 * @return List<DeviceInfoVO>
	 * <AUTHOR>
	 * @since 2024/1/25 14:03
	 **/
	List<DeviceInfoVO> appQueryPage(IPage<DeviceInfoVO> page, Map<String, Object> deviceMap);

	/**
	 * 获取最终升级结果
	 *
	 * @param jsonObject 入参
	 * <AUTHOR>
	 * @since 2024/1/26 10:27
	 **/
	void updateDeviceDistrict(@Param("json") JSONObject jsonObject);

	/**
	 * 批量还原状态
	 *
	 * @param ids 入参
	 * <AUTHOR>
	 * @since 2024/4/15 16:08
	 **/
	void stateRestoration(@Param("ids") List<Long> ids);

}
