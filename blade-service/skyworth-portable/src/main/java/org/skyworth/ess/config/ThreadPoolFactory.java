/*
 * Copyright (c) 2023. Skyworth All rights reserved
 */

package org.skyworth.ess.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
@EnableAsync
//@Service
public class ThreadPoolFactory {

   @Bean("commonThreadPool")
   public ThreadPoolExecutor getCommonThreadPool() {
       int core = Runtime.getRuntime().availableProcessors();
       ThreadFactory  threadFactory  =  new CustomizableThreadFactory("CommonThreadPool") ;
       return new ThreadPoolExecutor(core * 2, core * 4, 5L,
               TimeUnit.SECONDS, new ArrayBlockingQueue<>(core * 10),
               threadFactory, new ThreadPoolExecutor.DiscardPolicy());
   }
}
