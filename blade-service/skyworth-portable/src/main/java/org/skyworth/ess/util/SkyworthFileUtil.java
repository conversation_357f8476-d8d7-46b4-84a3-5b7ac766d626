package org.skyworth.ess.util;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.log.exception.BusinessException;

import java.io.*;
import java.net.URL;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
@Slf4j
public class SkyworthFileUtil {

    public static void createDir(String... dirPaths) {
        if (dirPaths == null) {
            return;
        }
        for (String dirPath : dirPaths) {
            File baseDirectory = new File(dirPath);
            if (!baseDirectory.exists()) {
                boolean flag = baseDirectory.mkdir();
                if (!flag) {
                    throw new BusinessException("portable.file.created.directory.is.failed");
                }
            }
        }

    }

    public static void mkdirOrDeleteFile(String dirPath) {
        if (dirPath == null) {
            return;
        }
        File directory = new File(dirPath);
        if (!directory.exists()) {
            boolean flag = directory.mkdirs();
            if (!flag) {
                throw new BusinessException("portable.file.created.directory.is.failed");
            }
        } else {
            File[] files = directory.listFiles();
            if (files == null) {
                return;
            }
            for (File file : files) {
                boolean delete = file.delete();
                if (!delete) {
                    throw new BusinessException("portable.file.delete.file.is.failed");
                }
            }
        }
    }

    /**
     *
     * @param urlPath 网络文件路径 http://www.xxx.com/image.jpg
     * @param localFilePath  生成本地文件路径和名称
     */
    public static void createFileByUrl(String urlPath, String localFilePath) {
        InputStream inputStream = null;
        FileOutputStream outputStream = null;
        try {
            URL url = new URL(urlPath);
            inputStream = url.openStream();
            outputStream = new FileOutputStream(localFilePath);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            log.error("createFileByUrl error :  ", e);
        } finally {
            closeOutputStream(outputStream);
            closeInputStream(inputStream);
        }
    }

    /**
     *
     * @param createZipPathName 创建压缩包文件名路径
     * @param needZipDirectory 待压缩的目录
     */
    public static void createZipFile4Directory(String createZipPathName, String needZipDirectory) {
        ZipOutputStream zipOut = null;
        try {
            zipOut = new ZipOutputStream(new FileOutputStream(createZipPathName + ".zip"));
            File fileDir = new File(needZipDirectory);
            File[] files = fileDir.listFiles();
            if(files == null) {
                return;
            }
            for (File file : files) {
                FileInputStream fis = null;
                try {
                    fis = new FileInputStream(file);
                    zipOut.putNextEntry(new ZipEntry(file.getName()));
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) > 0) {
                        zipOut.write(buffer, 0, length);
                    }
                } catch (IOException e) {
                    log.error("createFileByUrl error :  ", e);
                } finally {
                    if( zipOut != null) {
                        zipOut.closeEntry();
                    }
                    closeInputStream(fis);
                }
            }
        } catch (IOException e) {
            log.error("createFileByUrl error :  ", e);
        } finally {
            closeOutputStream(zipOut);
        }
    }

    public static void createZipFile4Response(String zipName, String localZipPathName,ZipOutputStream zipOut) {
        FileInputStream fis = null;
        try {
            byte[] allBuffer = new byte[1024];
            int length;
            ZipEntry zipEntry = new ZipEntry(zipName);
            zipOut.putNextEntry(zipEntry);
            fis = new FileInputStream(localZipPathName);
            while ((length = fis.read(allBuffer)) != -1) {
                zipOut.write(allBuffer, 0, length);
            }
        }catch (IOException e) {
            log.error("createZipFile4Response error :  ", e);
        }finally {
            closeInputStream(fis);
        }
    }
    private static void closeInputStream(InputStream inputStream) {
        try {
            if (inputStream != null) {
                inputStream.close();
            }
        } catch (IOException ex) {
            log.error("closeInputStream error :  ", ex);
        }
    }
    private static void closeOutputStream(OutputStream outputStream) {
        try {
            if (outputStream != null) {
                outputStream.close();
            }
        } catch (IOException ex) {
            log.error("outputStream error :  ", ex);
        }
    }
}
