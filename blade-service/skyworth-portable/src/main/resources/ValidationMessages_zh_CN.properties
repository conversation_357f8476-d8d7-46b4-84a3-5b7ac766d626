system.error=\u7CFB\u7EDF\u9519\u8BEF
portable.device.exit.factory.deviceSerialNumber.notNull=\u8BBE\u5907SN\u4E0D\u80FD\u4E3A\u7A7A
parameter.notEmpty=\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exist.multiple.records=\u5F53\u524D\u7528\u6237\u4E0B\u8BE5\u8BBE\u5907SN(%s)\u8BB0\u5F55\u91CD\u590D
portable.device.not.duplicate.association=\u8BBE\u5907\u5DF2\u88AB\u8BE5\u7528\u6237\u5173\u8054\u8FC7\uFF0C\u4E0D\u80FD\u91CD\u590D\u5173\u8054
portable.device.exit.factory.deviceType.notNull=\u8BBE\u5907\u578B\u53F7\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.company.notNull=\u5382\u5BB6\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.qualityGuaranteeYear.notNull=\u8D28\u4FDD\u5E74\u9650\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.exitFactoryDate.notNull=\u51FA\u5382\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.batteryRatedCapacity.notNull=\u7535\u6C60\u989D\u5B9A\u5BB9\u91CF\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.batteryRatedAcDischargeCapacity.notNull=\u7535\u6C60\u989D\u5B9A\u4EA4\u6D41\u653E\u7535\u603B\u529F\u7387\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.batteryRatedDischargeCapacity.notNull=\u7535\u6C60\u989D\u5B9A\u76F4\u6D41\u653E\u7535\u603B\u529F\u7387\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.batteryRatedDcDischargeCapacity.notNull=\u7535\u6C60\u989D\u5B9A\u5145\u7535\u603B\u529F\u7387\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.batteryRatedChargeCapacity.notNull=\u7535\u6C60\u989D\u5B9A\u5145\u7535\u603B\u529F\u7387\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.batteryRatedAndersonChargeCapacity.notNull=\u7535\u6C60\u989D\u5B9A\u5B89\u5FB7\u68EE\u63A5\u53E3\u5145\u7535\u603B\u529F\u7387\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.bluetoothProtocolVersion.notNull=\u84DD\u7259\u534F\u8BAE\u7248\u672C\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.motherboardHardwareVersion.notNull=\u4E3B\u677F\u786C\u4EF6\u7248\u672C\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.deviceSerialNumber.is.exist=\u8BBE\u5907SN\u5DF2\u5B58\u5728
portable.device.exit.factory.deviceSerialNumber.is.used=\u8BBE\u5907SN\uFF1A %s \u5DF2\u88AB\u4F7F\u7528\uFF0C\u4E0D\u80FD\u5220\u9664
portable.device.maintain.exit.factoryInfo=\u8BF7\u7EF4\u62A4\u8BE5\u8BBE\u5907\u51FA\u5382\u4FE1\u606F
portable.device.exit.factory.had.save.quality.guarantee=\u6B64SN\u4EA7\u54C1\u4FDD\u4FEE\u5DF2\u5B58\u5728\uFF0C\u4E0D\u80FD\u91CD\u590D\u63D0\u4EA4
portable.device.exit.factory.buyChannel.notNull=\u9500\u552E\u6E20\u9053\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.buyDate.notNull=\u8D2D\u4E70\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.buyOrderNumber.notNull=\u8BA2\u5355\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A
portable.device.exit.factory.buyCertificateImgBizKey.notNull=\u8D2D\u4E70\u51ED\u8BC1\u4E0D\u80FD\u4E3A\u7A7A
portable.file.created.directory.is.failed=\u521B\u5EFA\u76EE\u5F55\u5931\u8D25
portable.file.delete.is.failed=\u5220\u9664\u6587\u4EF6\u5931\u8D25
portable.device.exit.factory.import.deviceSerialNumber.is.exist=\u8BBE\u5907SN\uFF1A%s \u5DF2\u5B58\u5728
portable.device.exit.factory.modify.import.deviceSerialNumber.is.not.exist=\u8BBE\u5907SN\uFF1A%s \u4E0D\u5B58\u5728\uFF0C\u8BF7\u4F7F\u7528\u65B0\u589E\u5BFC\u5165
portable.device.exit.factory.import.dict.is.not.exist=\u8BBE\u5907\u7C7B\u578B\u3001\u516C\u53F8\u4E1A\u52A1\u5B57\u5178\u4E0D\u5B58\u5728\uFF1Aportable_device_type,portable_company
portable.device.exit.factory.import.dict.name.is.error=\u8BBE\u5907\u7C7B\u578B\u3001\u516C\u53F8\u4E1A\u52A1\u4E0D\u6B63\u786E\uFF1A %s
portable.device.exit.factory.not.exists=\u8BBE\u5907\u5E8F\u5217\u53F7\u672A\u7EF4\u62A4
