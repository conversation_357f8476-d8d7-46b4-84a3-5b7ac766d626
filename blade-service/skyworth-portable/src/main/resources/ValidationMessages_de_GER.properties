system.error=Systemfehler
portable.device.exit.factory.deviceSerialNumber.notNull=Die Seriennummer des Ger\u00E4ts kann nicht leer sein
parameter.notEmpty=Der Parameter kann nicht leer
portable.device.exist.multiple.records=Der SN (%s)-Datensatz des Ger\u00E4ts unter dem aktuellen Benutzer ist ein Duplikat
portable.device.not.duplicate.association=Das Ger\u00E4t wurde bereits mit dem Benutzer assoziiert und kann nicht erneut assoziiert werden
portable.device.exit.factory.deviceType.notNull=Ger\u00E4tetyp kann nicht leer
portable.device.exit.factory.company.notNull=Firma kann nicht leer sein
portable.device.exit.factory.qualityGuaranteeYear.notNull=Qualit\u00E4tsgarantie Jahr kann nicht leer sein
portable.device.exit.factory.exitFactoryDate.notNull=Exit factory date kann nicht leer
portable.device.exit.factory.batteryRatedCapacity.notNull=Die Nennkapazit\u00E4t der Batterie kann nicht leer sein
portable.device.exit.factory.batteryRatedAcDischargeCapacity.notNull=Die Nenn-Wechselstrom-Entladekapazit\u00E4t der Batterie kann nicht entladen werden
portable.device.exit.factory.batteryRatedDischargeCapacity.notNull=Die Nennentladekapazit\u00E4t der Batterie kann nicht entladen werden
portable.device.exit.factory.batteryRatedDcDischargeCapacity.notNull=Die DC-Entladekapazit\u00E4t der Batterie kann sich nicht entleeren
portable.device.exit.factory.batteryRatedChargeCapacity.notNull=Die Nennladekapazit\u00E4t des Akkus kann nicht leer sein
portable.device.exit.factory.batteryRatedAndersonChargeCapacity.notNull=Die Anderson-Ladekapazit\u00E4t der Batterie kann nicht entleert werden
portable.device.exit.factory.bluetoothProtocolVersion.notNull=Bluetooth-Protokollversion kann nicht leer
portable.device.exit.factory.motherboardHardwareVersion.notNull=Motherboard-Hardware-Version kann nicht leer
portable.device.exit.factory.deviceSerialNumber.is.exist=Die Seriennummer des Ger\u00E4ts ist vorhanden
portable.device.exit.factory.deviceSerialNumber.is.used=Ger\u00E4teseriennummer: %s wird verwendet, kann nicht gel\u00F6scht werden
portable.device.maintain.exit.factoryInfo=Bitte bewahren Sie die Werksinformationen dieses Ger\u00E4ts auf
portable.device.exit.factory.had.save.quality.guarantee=Diese SN-Produktgarantie existiert bereits und kann nicht erneut eingereicht werden
portable.device.exit.factory.buyChannel.notNull=Kaufkanal kann nicht leer werden
portable.device.exit.factory.buyDate.notNull=Kaufdatum darf nicht leer sein
portable.device.exit.factory.buyOrderNumber.notNull=Kaufauftragsnummer kann nicht leer sein
portable.device.exit.factory.buyCertificateImgBizKey.notNull=Zertifikat kaufen kann nicht leer sein
portable.file.created.directory.is.failed=Erstelltes Verzeichnis fehlgeschlagen
portable.file.delete.is.failed=Das L\u00F6schen der Datei ist fehlgeschlagen
portable.device.exit.factory.import.deviceSerialNumber.is.exist=Ger\u00E4te-SN: %s ist vorhanden
portable.device.exit.factory.modify.import.deviceSerialNumber.is.not.exist=Ger\u00E4te-SN: %s ist nicht vorhanden, bitte verwenden Sie \u201EAdd Import\u201C.
portable.device.exit.factory.import.dict.is.not.exist=Ger\u00E4tetyp und Firma sind nicht vorhanden: tragbarer Ger\u00E4tetyp, tragbare_Firma
portable.device.exit.factory.import.dict.name.is.error=Fehler bei Ger\u00E4tetyp und Unternehmen: %s
portable.device.exit.factory.not.exists=Seriennummer des Ger\u00E4ts nicht gepflegt
