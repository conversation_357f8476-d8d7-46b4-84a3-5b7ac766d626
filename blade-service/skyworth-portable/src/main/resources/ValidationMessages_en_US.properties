system.error=System Error
portable.device.exit.factory.deviceSerialNumber.notNull=Device serial number can not empty
parameter.notEmpty=The parameter cannot be empty
portable.device.exist.multiple.records=The SN (%s) record of the device under the current user is duplicate
portable.device.not.duplicate.association=The device has already been associated with the user and cannot be associated again
portable.device.exit.factory.deviceType.notNull=device Type can not empty
portable.device.exit.factory.company.notNull=company can not empty
portable.device.exit.factory.qualityGuaranteeYear.notNull=Quality guarantee Year can not empty
portable.device.exit.factory.exitFactoryDate.notNull=Exit factory date can not empty
portable.device.exit.factory.batteryRatedCapacity.notNull=Battery rated capacity can not empty
portable.device.exit.factory.batteryRatedAcDischargeCapacity.notNull=Battery rated Ac-Discharge capacity can not empty
portable.device.exit.factory.batteryRatedDischargeCapacity.notNull=Battery rated Discharge capacity can not empty
portable.device.exit.factory.batteryRatedDcDischargeCapacity.notNull=Battery rated Dc-Discharge capacity can not empty
portable.device.exit.factory.batteryRatedChargeCapacity.notNull=Battery rated charge capacity can not empty
portable.device.exit.factory.batteryRatedAndersonChargeCapacity.notNull=Battery rated Anderson charge capacity Can Not Empty
portable.device.exit.factory.bluetoothProtocolVersion.notNull=Bluetooth protocol version can not empty
portable.device.exit.factory.motherboardHardwareVersion.notNull=Motherboard hardware version can not empty
portable.device.exit.factory.deviceSerialNumber.is.exist=Device Serial Number is exist
portable.device.exit.factory.deviceSerialNumber.is.used=Device Serial Number: %s is used,can not delete
portable.device.maintain.exit.factoryInfo=Please maintain the factory information of this device
portable.device.exit.factory.had.save.quality.guarantee=This SN product warranty already exists and cannot be resubmitted
portable.device.exit.factory.buyChannel.notNull=Buy channel  can not empty
portable.device.exit.factory.buyDate.notNull=Buy date can not empty
portable.device.exit.factory.buyOrderNumber.notNull=Buy order number can not empty
portable.device.exit.factory.buyCertificateImgBizKey.notNull=Buy certificate can not empty
portable.file.created.directory.is.failed=created directory failed
portable.file.delete.is.failed=delete file failed
portable.device.exit.factory.import.deviceSerialNumber.is.exist=Device SN: %s is exist
portable.device.exit.factory.modify.import.deviceSerialNumber.is.not.exist=Device SN: %s is not exist, please use add import.
portable.device.exit.factory.import.dict.is.not.exist=Device type and company is not exits:portable_device_type,portable_company
portable.device.exit.factory.import.dict.name.is.error=Device type and company is error: %s
portable.device.exit.factory.not.exists=Equipment serial number not maintained
