package org.springblade.common.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR> - x<PERSON><PERSON><PERSON>
 * @version 1.0
 * @project
 * @description 业务常量类
 * @create-time 2023/9/20 10:54:09
 */
public interface BizConstant {
	String CHAR_ZERO = "0";
	String CHAR_ONE = "1";
	String CHAR_TWO = "2";
	String CHAR_THREE = "3";
	String CHAR_FOUR = "4";
	String CHAR_FIVE = "5";
	String CHAR_SIX = "6";
	String CHAR_SEVEN = "7";
	String CHAR_EIGHT = "8";
	String CHAR_NINE = "9";
	String CHAR_TEN = "10";

	Integer NUMBER_ZERO = 0;
	Integer NUMBER_ONE = 1;
	Integer NUMBER_TWO = 2;
	Integer NUMBER_THREE = 3;
	Integer NUMBER_FOUR = 4;
	Integer NUMBER_FIVE = 5;
	Integer NUMBER_SIX = 6;
	Integer NUMBER_SEVEN = 7;
	Integer NUMBER_EIGHT = 8;
	Integer NUMBER_NINE = 9;
	Integer NUMBER_TEN = 10;

	String BIGDECIMAL_DEFAULT_VALUE_ONE = "0.0";
	String BIGDECIMAL_DEFAULT_VALUE = "0";
	String BIGDECIMAL_DEFAULT_VALUE_THREE = "0.000";

	String PATTERN_HOUR_MINUS = "HH:mm";

	String IMPORT_SUCCESS = "import success!";
	BigDecimal THOUSAND = new BigDecimal(1000);
	String UNIT_THOUSAND_WH = " kWh";
	String UNIT_WH = " Wh";
	String UNIT_KW = " kW";
	String UNIT_W = " W";

	String INVERT_KIND_ESS_INVERTER1_PHASE = "ESSInverter1Phase";
	String INVERT_KIND_ESS_INVERTER3_PHASE = "ESSInverter3Phase";
	String INVERT_KIND_PHOTOVOLTAIC_INVERTER = "PhotovoltaicInverter";
	String INVERT_KIND_DICT_CODE = "client_invert_deviceModel_map_kind";

	Integer CLIENT_INVERTER_CONFIGURE_NETWORK_YES = 0;
	Integer CLIENT_INVERTER_CONFIGURE_NETWORK_NO = 1;

	String CLIENT_WIFI_STICK_STATUS_ONLINE = "1";
	String CLIENT_WIFI_STICK_STATUS_OFFLINE = "0";

	String CLIENT_IMPORTANT_EVENT_TYPE_PLANT = "plant";
	String CLIENT_IMPORTANT_EVENT_TYPE_INVERTER = "device";
	String CLIENT_IMPORTANT_EVENT_TYPE_BATTERY = "battery";

	String CLIENT_INVERTER_MODEL_SUPPORT_PARALLEL_DICT_CODE = "client_inverter_model_support_parallel";
	Integer CLIENT_INVERTER_MAX_PARALLEL = 3;
	String CLIENT_PLANT_PARALLEL_MODE_NO = "0";
	String CLIENT_PLANT_PARALLEL_MODE_YES = "1";
}
