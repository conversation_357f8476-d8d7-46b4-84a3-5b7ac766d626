package org.springblade.common.constant;

import lombok.Getter;

/**
 * 节点类型字典code
 *
 * <AUTHOR>
 */

@Getter
public enum DictNodeBizCodeEnum {
	SURVEY_WO_ASSIGN("surveyWoAssign", "指派踏勘任务"), INSTALL_WO_ASSIGNMENT("InstallWoAssignment", "指派施工任务"), MATERIAL_COLLECTION("materialCollection", "物料签收");
	private final String dictCode;
	private final String remark;

	DictNodeBizCodeEnum(String dictCode, String remark) {
		this.dictCode = dictCode;
		this.remark = remark;
	}
}
