package org.springblade.common.utils;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import lombok.Data;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 行合并
 *
 * @description:
 * @author: SDT50545
 * @since: 2024-01-27 17:28
 **/
@Data
public class CustomMergeStrategy implements CellWriteHandler {
	private static final Logger logger = LoggerFactory.getLogger(CustomMergeStrategy.class);
	private int[] mergeColumnIndex;
	/**
	 * 从第几行开始合并，表头下标为0
	 */
	private int mergeRowIndex;

	public CustomMergeStrategy(int mergeRowIndex, int[] mergeColumnIndex) {
		this.mergeRowIndex = mergeRowIndex;
		this.mergeColumnIndex = mergeColumnIndex;
	}

	@Override
	public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
		//当前行
		int curRowIndex = cell.getRowIndex();
		//当前列
		int curColIndex = cell.getColumnIndex();
		if (curRowIndex > mergeRowIndex) {
			for (int columnIndex : mergeColumnIndex) {
				if (curColIndex == columnIndex) {
					mergeWithPrevRow(cell, curRowIndex, isHead);
					break;
				}
			}
		}
	}

	private void mergeWithPrevRow(Cell cell, Integer relativeRowIndex, Boolean isHead) {
		//如果是表头不做处理
		if (isHead) {
			return;
		}
		//如果当前是第一行不做处理
		if (relativeRowIndex == 0) {
			return;
		}
		//获取当前行下标，上一行下标，上一行对象，上一列对象
		Sheet sheet = cell.getSheet();
		int rowIndex = cell.getRowIndex();
		int rowIndexPrev = rowIndex - 1;
		Row row = sheet.getRow(rowIndexPrev);
		Cell cellPrev = row.getCell(cell.getColumnIndex());

		//得到当前单元格值和上一行单元格
		Object cellValue = cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : cell.getNumericCellValue();
		Object cellValuePrev = cellPrev.getCellType() == CellType.STRING ? cellPrev.getStringCellValue() : cellPrev.getNumericCellValue();
		//如果当前单元格和上一行单元格值相等就合并
		if (!cellValue.equals(cellValuePrev)) {
			return;
		}
		//获取已有策略
		List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
		boolean mergen = false;
		for (int i = 0; i < mergedRegions.size(); i++) {
			CellRangeAddress cellAddresses = mergedRegions.get(i);
			if (cellAddresses.isInRange(rowIndexPrev, cell.getColumnIndex())) {
				sheet.removeMergedRegion(i);
				cellAddresses.setLastRow(rowIndex);
				sheet.addMergedRegion(cellAddresses);
				mergen = true;
				break;
			}
		}
		if (!mergen) {
			CellRangeAddress cellAddresses = new CellRangeAddress(rowIndexPrev, rowIndex, cell.getColumnIndex(), cell.getColumnIndex());
			sheet.addMergedRegion(cellAddresses);
		}
	}
}
