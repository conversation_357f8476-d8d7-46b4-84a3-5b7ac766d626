package org.springblade.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springblade.common.constant.BizConstant;
import org.springblade.common.vo.CommonVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据单位转换，用于处理类似w和kw之间的数据转换
 *
 * @description:
 * @author: SDT50545
 * @since: 2023-12-13 13:48
 **/
@Slf4j
public class DataUnitConversionUtil {

	/**
	 * @param decimal       原数据
	 * @param coefficient   倍数
	 * @param decimalPlaces 保留小数位数
	 * @return 转换后数据
	 */
	public static BigDecimal largeToSmall(BigDecimal decimal, BigDecimal coefficient, Integer decimalPlaces) {
		if (decimal == null || decimal.compareTo(BigDecimal.ZERO) == BizConstant.NUMBER_ZERO) {
			return BigDecimal.ZERO;
		}
		// 设置默认倍数
		coefficient = null == coefficient ? BizConstant.THOUSAND : coefficient;
		// 设置默认返回小数位
		decimalPlaces = null == decimalPlaces ? BizConstant.NUMBER_TWO : decimalPlaces;
		return decimal.multiply(coefficient).setScale(decimalPlaces, RoundingMode.HALF_UP);
	}

	/**
	 * @param decimal       原数据
	 * @param coefficient   倍数
	 * @param decimalPlaces 保留小数位数
	 * @return 转换后
	 */
	public static BigDecimal smallToLarge(BigDecimal decimal, BigDecimal coefficient, Integer decimalPlaces) {
		if (decimal == null || decimal.compareTo(BigDecimal.ZERO) == BizConstant.NUMBER_ZERO) {
			return BigDecimal.ZERO;
		}
		// 设置默认返回小数位
		decimalPlaces = null == decimalPlaces ? BizConstant.NUMBER_TWO : decimalPlaces;
		// 设置默认倍数
		coefficient = null == coefficient ? BizConstant.THOUSAND : coefficient;
		return decimal.divide(coefficient, decimalPlaces, RoundingMode.HALF_UP);
	}

	/**
	 * @param type：1，wh和kwh；2：w和kw
	 * @param bigDecimal           入参
	 * @return String
	 * <AUTHOR>
	 * @since 2023/12/13 16:27
	 **/
	public static String getChangeEnergyResult(BigDecimal bigDecimal, int type) {
		BigDecimal temp = bigDecimal == null ? new BigDecimal(BizConstant.NUMBER_ZERO) : bigDecimal;
		String result = "";
		String divisionThousandResult = temp.divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP) +
			(type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_THOUSAND_WH : BizConstant.UNIT_KW);
		String divisionResult = temp.setScale(BizConstant.NUMBER_ONE, RoundingMode.HALF_UP) +
			(type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_WH : BizConstant.UNIT_W);
		if (temp.compareTo(BizConstant.THOUSAND) > BizConstant.NUMBER_ZERO) {
			result = divisionThousandResult;
		} else {
			if (temp.compareTo(BigDecimal.ZERO) >= BizConstant.NUMBER_ZERO) {
				result = divisionResult;
			} else {
				// 负数情况
				if (temp.compareTo(new BigDecimal("-1000")) >= BizConstant.NUMBER_ZERO) {
					result = divisionResult;
				} else {
					result = divisionThousandResult;
				}
			}
		}
		return result;
	}

	/**
	 * @param type：1，wh和kwh；2：w和kw
	 * @param bigDecimal           入参
	 * @param moreThanThousandScale  小数位
	 * @param lessThousandScale  小数位
	 * @return String
	 * <AUTHOR>
	 * @since 2023/12/13 16:27
	 **/
	public static String getChangeEnergyResult4Scale(BigDecimal bigDecimal, int type,Integer moreThanThousandScale,Integer lessThousandScale) {
		BigDecimal temp = bigDecimal == null ? new BigDecimal(BizConstant.NUMBER_ZERO) : bigDecimal;
		String result = "";
		String divisionThousandResult = temp.divide(BizConstant.THOUSAND, moreThanThousandScale, RoundingMode.HALF_UP) +
			(type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_THOUSAND_WH : BizConstant.UNIT_KW);
		String divisionResult = temp.setScale(lessThousandScale, RoundingMode.HALF_UP) +
			(type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_THOUSAND_WH : BizConstant.UNIT_KW);
		if (temp.compareTo(BizConstant.THOUSAND) > BizConstant.NUMBER_ZERO) {
			result = divisionThousandResult;
		} else {
			if (temp.compareTo(BigDecimal.ZERO) >= BizConstant.NUMBER_ZERO) {
				result = divisionResult;
			} else {
				// 负数情况
				if (temp.compareTo(new BigDecimal("-1000")) >= BizConstant.NUMBER_ZERO) {
					result = divisionResult;
				} else {
					result = divisionThousandResult;
				}
			}
		}
		return result;
	}

	public static String getChangeEnergyAddResult(int type, BigDecimal... bigDecimalArray) {
		String result = "";
		List<CommonVO> valueList = getCommonVoS(bigDecimalArray);
		log.info("getChangeEnergyAddResult ：{}", valueList);
		List<Boolean> collect = valueList.stream().map(CommonVO::getFlag).distinct().collect(Collectors.toList());
		// 2者都小于 1000 或者都大于 1000，没有做除以1000的转换
		if (collect.size() == 1) {
			Boolean flag = collect.get(0);
			// 2者都进行了除以1000的转换
			if (flag) {
				result = valueList.stream().map(CommonVO::getBigDecimalValue).reduce(BigDecimal.ZERO, BigDecimal::add)
					+ (type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_THOUSAND_WH : BizConstant.UNIT_KW);
			} else {
				// 2者都没有进行除以1000的转换
				BigDecimal addResult = valueList.stream().map(CommonVO::getBigDecimalValue).reduce(BigDecimal.ZERO, BigDecimal::add);
				// 判断相加后是否大于1000
				result = getChangeEnergyResult(addResult, type);
			}
		} else {
			// 2者有其中一个进行了 转换，需要将没有转换的 也除以1000，再累加
			BigDecimal noDivideAddResult = valueList.stream().filter(p -> !p.getFlag()).map(p -> p.getBigDecimalValue().divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP))
				.reduce(BigDecimal.ZERO, BigDecimal::add);
			BigDecimal divideAddResult = valueList.stream().filter(CommonVO::getFlag).map(CommonVO::getBigDecimalValue).reduce(BigDecimal.ZERO, BigDecimal::add);
			result = noDivideAddResult.add(divideAddResult) + (type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_THOUSAND_WH : BizConstant.UNIT_KW);

		}

		return result;
	}

	@NotNull
	private static List<CommonVO> getCommonVoS(BigDecimal[] bigDecimalArray) {
		List<CommonVO> valueList = new ArrayList<>();
		for (BigDecimal bigDecimal : bigDecimalArray) {
			CommonVO commonVO = new CommonVO();
			BigDecimal temp = bigDecimal == null ? new BigDecimal(BizConstant.NUMBER_ZERO) : bigDecimal;
			if (temp.compareTo(BizConstant.THOUSAND) > BizConstant.NUMBER_ZERO ||
				temp.compareTo(new BigDecimal("-1000")) < BizConstant.NUMBER_ZERO) {
				commonVO.setBigDecimalValue(temp.divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP));
				commonVO.setFlag(true);
			} else {
				commonVO.setBigDecimalValue(temp);
				commonVO.setFlag(false);
			}
			valueList.add(commonVO);
		}
		return valueList;
	}

	/**
	 * @param type：1，kWh；2：kW
	 * @param bigDecimal      入参
	 * @ClassName DataUnitConversionUtil
	 * @Description
	 * <AUTHOR>
	 * @Date 2024/3/20 09:38
	 * @version V1.0
	 */
	public static String getChangeEnergyResultK(BigDecimal bigDecimal, int type) {
		if (bigDecimal == null || bigDecimal.compareTo(BigDecimal.ZERO) == BizConstant.NUMBER_ZERO) {
			return "";
		}
		String result = bigDecimal.divide(BizConstant.THOUSAND, BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
		if (new BigDecimal(result).scale() > 0) {
			result = new BigDecimal(result).setScale(BizConstant.NUMBER_TWO, RoundingMode.HALF_UP).toPlainString();
		}
		return result + (type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_THOUSAND_WH : BizConstant.UNIT_KW);
	}

	/**
	 * 截取数据小数位
	 *
	 * @param bigDecimal    数据
	 * @param decimalPlaces 小数位数
	 * @return 截取小数位后的数据
	 */
	public static BigDecimal extractDecimalPlaces(BigDecimal bigDecimal, Integer decimalPlaces) {
		bigDecimal = bigDecimal == null ? new BigDecimal(BizConstant.NUMBER_ZERO) : bigDecimal;
		decimalPlaces = null == decimalPlaces ? BizConstant.NUMBER_TWO : decimalPlaces;
		return bigDecimal.setScale(decimalPlaces, RoundingMode.HALF_UP);
	}

	/**
	 * 	原始值是 kw 时，
	 * @param bigDecimal
	 * @param type
	 * @return
	 */
	public static String getKwChangeEnergyResult(BigDecimal bigDecimal, int type) {
		BigDecimal temp = bigDecimal == null ? new BigDecimal(BizConstant.NUMBER_ZERO) : bigDecimal;
		String result = "";
		String kwResult = temp.setScale(BizConstant.NUMBER_ONE, RoundingMode.HALF_UP) +
			(type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_THOUSAND_WH : BizConstant.UNIT_KW);
		String wResult = temp.multiply(BizConstant.THOUSAND).setScale(BizConstant.NUMBER_ONE, RoundingMode.HALF_UP) +
			(type == BizConstant.NUMBER_ONE ? BizConstant.UNIT_WH : BizConstant.UNIT_W);
		if (temp.compareTo(BigDecimal.ONE) > BizConstant.NUMBER_ZERO) {
			result = kwResult;
		} else {
			if (temp.compareTo(BigDecimal.ZERO) >= BizConstant.NUMBER_ZERO) {
				result = wResult;
			} else {
				// 负数情况
				if (temp.compareTo(new BigDecimal("-1")) >= BizConstant.NUMBER_ZERO) {
					result = wResult;
				} else {
					result = kwResult;
				}
			}
		}
		return result;
	}
}
