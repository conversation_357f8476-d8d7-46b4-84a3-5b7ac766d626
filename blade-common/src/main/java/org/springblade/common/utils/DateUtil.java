package org.springblade.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.tool.ValidationUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日期工具类
 *
 * @author: SDT50545
 * @since: 2023-09-16 08:53
 **/
@Slf4j
public class DateUtil {
	public static final String PATTERN_DATE = "yyyy-MM-dd";
	public static final String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";
	public static final String PATTERN_YEAR_MONTH = "yyyy-MM";
	public static final String PATTERN_HOURS_MINUTE = "HH:mm";
	public static final String PATTERN_DAY = "dd";
	public static final String PATTERN_YEAR = "yyyy";
	public static final String PATTERN_MONTH_DAY = "MM-dd";

	/**
	 * 计算日期之间相差的天数
	 *
	 * @param startDate 开始时间
	 * @param endDate   结束时间
	 * @return long
	 * <AUTHOR>
	 * @since 2023/9/16 9:12
	 **/
	public static long getDayBetweenTwoDate(Date startDate, Date endDate) {
		LocalDate localStartDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		LocalDate localEndDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		return ChronoUnit.DAYS.between(localStartDate, localEndDate);
	}

	/**
	 * 判断日期是否是当天
	 *
	 * @param date 结束时间
	 * @return boolean
	 * <AUTHOR>
	 * @since 2023/9/16 9:12
	 **/
	public static boolean judgeTimeIsToday(Date date) {
		LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
		LocalDateTime startTime = LocalDate.now().atTime(0, 0, 0);
		LocalDateTime endTime = LocalDate.now().atTime(23, 59, 59);
		//如果大于今天的开始日期，小于今天的结束日期
		if (localDateTime.isAfter(startTime) && localDateTime.isBefore(endTime)) {
			return true;
		}
		return false;
	}

	/**
	 * 获取当前日期
	 *
	 * @return String
	 * <AUTHOR>
	 * @since 2023/9/18 17:55
	 **/
	public static Date getCurrentDate() {
		SimpleDateFormat sdf = new SimpleDateFormat(PATTERN_DATE);
		String currentDateStr = sdf.format(new Date());
		Date date;
		try {
			date = sdf.parse(currentDateStr);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
		return date;
	}

	/**
	 * 日期累加
	 *
	 * @param date      当前日期
	 * @param daysToAdd 入参
	 * @return Date
	 * <AUTHOR>
	 * @since 2023/10/10 18:54
	 **/
	public static Date plusDays(Date date, long daysToAdd) {
		Instant instant = date.toInstant();
		return Date.from(instant.plus(Duration.ofDays(daysToAdd)));
	}

	public static LocalDateTime stringDateTime2LocalDateTime(String dateTime) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return LocalDateTime.parse(dateTime, formatter);
	}

	/**
	 * 获取当前服务器时区，格式为  UTC+8
	 *
	 * @return
	 */
	public static String getCurrentUtcTimeZone() {
		ZoneId currentTimeZone = ZoneId.systemDefault();
		ZoneOffset offset = currentTimeZone.getRules().getOffset(java.time.Instant.now());
		int totalSeconds = offset.getTotalSeconds();
		int hours = totalSeconds / 3600;
		return "UTC" + (hours >= 0 ? "+" : "-") + hours;
	}

	public static int compareUtc(String utcOffset1, String utcOffset2) {
		utcOffset1 = getTimeZoneWithoutUtc(utcOffset1);
		utcOffset2 = getTimeZoneWithoutUtc(utcOffset2);
		ZoneOffset offset1 = ZoneOffset.of(utcOffset1);
		ZoneOffset offset2 = ZoneOffset.of(utcOffset2);

		return offset2.getTotalSeconds() / 3600 - offset1.getTotalSeconds() / 3600;
	}

	@NotNull
	private static String getTimeZoneWithoutUtc(String utcOffset) {
		if (utcOffset.contains("UTC")) {
			utcOffset = utcOffset.replace("UTC", "");
		}
		return utcOffset;
	}

	/**
	 * 将指定时区的时间转换为另一个时区的时间(设备上报专用)
	 *
	 * @param timestamp   输入的日期时间字符串
	 * @param currentZone 原始时区
	 * @param targetZone  目标时区
	 * @return 转换后的日期时间字符串
	 */
	public static Date convertLongTimeZoneForDeviceUpload(long timestamp, String currentZone, String targetZone)  {
		try {
			log.info("转换时区前的时间戳:=============>" + timestamp);
			// 将时间戳转换为毫秒
			long timestampInMillis = timestamp * 1000;
			// 创建 Instant 对象
			Instant instant = Instant.ofEpochMilli(timestampInMillis);
			// 创建 ZonedDateTime 对象，按照 +0时区 去转换正常时间格式；因为设备上报的时间戳已经是经过时区偏移的时间戳了
			ZonedDateTime zonedDateTimeCurrent = instant.atZone(ZoneId.of(targetZone));
			log.info("转换时区前的时间:=============>" + zonedDateTimeCurrent+",原时区（设备的）:"+currentZone);
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME);
			String dateStringFromTarget = zonedDateTimeCurrent.format(formatter);
			// 将 ZonedDateTime 转换为 Date
			// 将时间转换为目标时区
			LocalDateTime localDateTime = convertStringTimeZone(dateStringFromTarget, currentZone, targetZone);
			log.info("最终返回的时间（转成服务器+0时区的时间）:=============>" + localDateTime);
			return convertToDate(localDateTime);
		} catch (Exception e) {
			throw new IllegalArgumentException("无效的时区或时间戳", e);
		}
	}
	/**
	 * 将指定时区的时间转换为另一个时区的时间
	 *
	 * @param timestamp   输入的日期时间字符串
	 * @param currentZone 原始时区
	 * @param targetZone  目标时区
	 * @return 转换后的日期时间字符串
	 */
	public static Date convertLongTimeZone(long timestamp, String currentZone, String targetZone)  {
		try {
			// 将时间戳转换为毫秒
			long timestampInMillis = timestamp * 1000;
			// 创建 Instant 对象
			Instant instant = Instant.ofEpochMilli(timestampInMillis);
			// 创建 ZonedDateTime 对象，指定原始时区
			ZonedDateTime zonedDateTimeCurrent = instant.atZone(ZoneId.of(currentZone));
			// 将时间转换为目标时区
			ZonedDateTime zonedDateTimeTarget = zonedDateTimeCurrent.withZoneSameInstant(ZoneId.of(targetZone));
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.PATTERN_DATETIME);
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME);
			String dateStringFromTarget = zonedDateTimeTarget.format(formatter);
			// 将 ZonedDateTime 转换为 Date
			return simpleDateFormat.parse(dateStringFromTarget);
		} catch (Exception e) {
			throw new IllegalArgumentException("无效的时区或时间戳", e);
		}
	}


	/**
	 * 将指定时区的时间转换为另一个时区的时间
	 *
	 * @param timestamp   输入的日期时间字符串
	 * @param currentZone 原始时区
	 * @param targetZone  目标时区
	 * @return 转换后的日期时间字符串
	 */
	public static long convertTimeZoneReturnTimeMillis(long timestamp, String currentZone, String targetZone,
													   boolean isMillis) {
		// 将时间戳转换为毫秒
		long timestampInMillis = isMillis ? timestamp : timestamp * 1000;
		// 创建 Instant 对象
		Instant instant = Instant.ofEpochMilli(timestampInMillis);
		// 创建 ZonedDateTime 对象，指定原始时区
		ZonedDateTime zonedDateTimeCurrent = instant.atZone(ZoneId.of(currentZone));
		// 将时间转换为目标时区
		ZonedDateTime zonedDateTimeTarget = zonedDateTimeCurrent.withZoneSameInstant(ZoneId.of(targetZone));
		// 返回目标时区的毫秒级时间戳
		return zonedDateTimeTarget.toInstant().toEpochMilli();
	}

	/**
	 * 将指定时区的时间转换为另一个时区的时间
	 *
	 * @param timestamp   输入的日期时间字符串
	 * @param currentZone 原始时区
	 * @param targetZone  目标时区
	 * @param isMillis    isMillis为true，timestamp为毫秒;isMillis为false，timestamp为秒
	 * @return 转换后的日期时间字符串
	 */
	public static long convertTimeZoneReturnTimeSecond(long timestamp, String currentZone, String targetZone,
													   boolean isMillis) {
		// 将时间戳转换为毫秒
		long timestampInMillis = isMillis ? timestamp : timestamp * 1000;
		// 创建 Instant 对象
		Instant instant = Instant.ofEpochMilli(timestampInMillis);
		// 创建 ZonedDateTime 对象，指定原始时区
		ZonedDateTime zonedDateTimeCurrent = instant.atZone(ZoneId.of(currentZone));
		// 将时间转换为目标时区
		ZonedDateTime zonedDateTimeTarget = zonedDateTimeCurrent.withZoneSameInstant(ZoneId.of(targetZone));
		// 返回目标时区的秒级时间戳
		return zonedDateTimeTarget.toInstant().getEpochSecond();
	}

	/**
	 * 将指定时区的时间转换为另一个时区的时间
	 *
	 * @param dateStr     输入的日期时间字符串
	 * @param currentZone 原始时区
	 * @param targetZone  目标时区
	 * @return 转换后的日期时间字符串
	 */
	public static LocalDateTime convertStringTimeZone(String dateStr, String currentZone, String targetZone) {
		// 定义输入格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME);
		// 解析输入字符串为 ZonedDateTime 对象，设置时区为 UTC+2
		ZonedDateTime zonedDateTime = ZonedDateTime.of(LocalDateTime.parse(dateStr, formatter),
			ZoneId.of(currentZone));
		// 转换到 UTC 时区
		ZonedDateTime utcZonedDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of(targetZone));
		// 提取 LocalDateTime
		return utcZonedDateTime.toLocalDateTime();
	}

	/**
	 * 将指定时区的时间转换为另一个时区的时间
	 *
	 * @param date            输入的日期时间字符串
	 * @param currentTimeZone 原始时区
	 * @param targetTimeZone  目标时区
	 * @return 转换后的日期时间字符串
	 */
	public static Date convertDateTimeZone(Date date, String currentTimeZone, String targetTimeZone) {
		try {
			// 将 Date 转换为 Instant
			Instant instant = date.toInstant();
			// 获取当前时区的 ZoneId
			ZoneId currentZoneId = ZoneId.of(currentTimeZone);
			// 获取目标时区的 ZoneId
			ZoneId targetZoneId = ZoneId.of(targetTimeZone);
			// 创建当前时区的 ZonedDateTime
			ZonedDateTime currentZonedDateTime = instant.atZone(currentZoneId);
			// 转换到目标时区的 ZonedDateTime
			ZonedDateTime targetZonedDateTime = currentZonedDateTime.withZoneSameInstant(targetZoneId);
			// 转换回 Date 类型
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME);
			String dateStringFromTarget = targetZonedDateTime.format(formatter);
			// 将 ZonedDateTime 转换为 Date
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.PATTERN_DATETIME);
			return simpleDateFormat.parse(dateStringFromTarget);
		} catch (ParseException e) {
			throw new IllegalArgumentException("解析时间错误", e);
		}
	}

	/**
	 * 将指定时区的时间转换为另一个时区的时间
	 *
	 * @param localDateTime 输入的日期时间localDateTime
	 * @param targetZone    目标时区
	 * @return 转换后的日期时间字符串
	 */
	public static LocalDateTime convertLocalDateTimeZone(LocalDateTime localDateTime, String currentZone,
														 String targetZone) {
		// 解析输入字符串为 ZonedDateTime 对象，设置时区为 UTC+2
		ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, ZoneId.of(currentZone));
		// 转换到 UTC 时区
		ZonedDateTime utcZonedDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of(targetZone));
		// 提取 LocalDateTime
		return utcZonedDateTime.toLocalDateTime();
	}


	public static String getFirstDayOfYear(String yearString) {
		// 将年份字符串转换为整数
		int year = Integer.parseInt(yearString);
		// 创建指定年份1月1日00:00:00的LocalDateTime对象
		LocalDateTime firstDayOfYear = LocalDateTime.of(year, 1, 1, 0, 0, 0);
		// 定义日期时间格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		// 格式化日期时间
		return firstDayOfYear.format(formatter);
	}

	public static String getLastDayOfYear(String yearString) {
		// 将年份字符串转换为整数
		int year = Integer.parseInt(yearString);
		// 创建指定年份12月31日23:59:59的LocalDateTime对象
		LocalDateTime lastDayOfYear = LocalDateTime.of(year, 12, 31, 23, 59, 59);
		// 定义日期时间格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		// 格式化日期时间
		return lastDayOfYear.format(formatter);
	}

	public static String completeStartDayTime(String yearMonthStr) {
		// 解析字符串为YearMonth对象
		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(PATTERN_YEAR_MONTH);
		YearMonth yearMonth = YearMonth.parse(yearMonthStr, inputFormatter);
		// 获取该月份的第一天并设置时间为00:00:00
		LocalDateTime firstDayOfMonth = yearMonth.atDay(1).atTime(0, 0, 0);
		// 格式化输出
		DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return firstDayOfMonth.format(outputFormatter);
	}

	public static String completeEndDayTime(String yearMonthStr) {
		// 解析字符串为YearMonth对象
		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(PATTERN_YEAR_MONTH);
		YearMonth yearMonth = YearMonth.parse(yearMonthStr, inputFormatter);
		// 获取该月份的最后一天并设置时间为23:59:59
		LocalDateTime lastDayOfMonth = yearMonth.atEndOfMonth().atTime(23, 59, 59);
		// 格式化输出
		DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return lastDayOfMonth.format(outputFormatter);
	}

	public static String formatOffset(String offset) {
		if (offset.equals("Z")) {
			return "UTC";
		}
		if (offset.startsWith("+")) {
			return "UTC+" + offset.charAt(2);
		}
		return "UTC" + offset;
	}

	public static Date convertToDate(LocalDateTime localDateTime) {
		ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());
		return Date.from(zonedDateTime.toInstant());
	}

	public static Date convertLocalDateTime2Date(LocalDateTime localDateTime) {
		return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * 转换date为特定格式的String字符串
	 *
	 * @param date    日期
	 * @param pattern 格式
	 * @return String
	 * <AUTHOR>
	 * @date 2025/3/12 10:27
	 */
	public static String parseDateToString(Date date, String pattern) {
		// 创建 SimpleDateFormat 对象，指定格式为 "HH:mm"
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		// 格式化 Date 对象并返回结果
		return sdf.format(date);
	}

	/**
	 * 转换String为特定格式的Date对象
	 *
	 * @param dateStr 日期字符串
	 * @param pattern 格式
	 * @return Date
	 * <AUTHOR>
	 * @date 2025/3/12 10:27
	 */
	public static Date parseStringToDate(String dateStr, String pattern) throws ParseException {
		// 创建 SimpleDateFormat 对象，指定格式为 "HH:mm"
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		// 格式化 Date 对象并返回结果
		return sdf.parse(dateStr);
	}

	/**
	 * 将字符串时间转换为指定时区的字符串时间
	 *
	 * @param inputTime   输入的日期时间字符串
	 * @param currentZone 原始时区
	 * @param targetZone  目标时区
	 * @param pattern     输出的日期时间格式
	 * @return 转换后的日期时间字符串
	 */
	public static String convertStringTimeZoneReturnString(String inputTime, String currentZone, String targetZone,
														   String pattern) {
		// 解析输入的日期时间字符串为LocalDateTime对象
		LocalDateTime localDateTime = LocalDateTime.parse(inputTime, DateTimeFormatter.ofPattern(PATTERN_DATETIME));
		// 将LocalDateTime对象与源时区结合，形成ZonedDateTime对象
		ZonedDateTime sourceZonedDateTime = localDateTime.atZone(ZoneId.of(currentZone));
		// 将ZonedDateTime对象转换到目标时区
		ZonedDateTime targetZonedDateTime = sourceZonedDateTime.withZoneSameInstant(ZoneId.of(targetZone));
		// 根据输出格式格式化目标时区的ZonedDateTime对象
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
		return targetZonedDateTime.format(formatter);
	}

	/**
	 * 将LocalDateTime转换为字符串Date
	 *
	 * @param localDateTime LocalDateTime
	 * @param pattern       格式
	 * @return Date
	 * <AUTHOR>
	 * @date 2025/3/12 10:27
	 */
	public static String parseLocalDateTimeToStringDate(LocalDateTime localDateTime, String pattern) {
		// 定义日期时间格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
		// 将 LocalDateTime 转换为 String
		return localDateTime.format(formatter);
	}

	/**
	 * 将UTC时间转换为指定时区的时间
	 *
	 * @param timestamp   UTC时间戳
	 * @param currentZone 设备所在时区
	 * @param targetZone  目标时区
	 * @return 转换后的本地时间
	 */
	public static LocalDateTime convertLongTimeToLocalDateTime(long timestamp, String currentZone, String targetZone) {
		Instant instant = Instant.ofEpochMilli(timestamp);
		ZonedDateTime utcDateTime = instant.atZone(ZoneId.of(currentZone));
		ZonedDateTime deviceDateTime = utcDateTime.withZoneSameInstant(ZoneId.of(targetZone));
		return deviceDateTime.toLocalDateTime();
	}

	/**
	 * 根据传入的long类型时间戳，时间戳所属的时区，转换后的时区，返回后的String类型时间戳格式返回String类型
	 *
	 * @param timestamp   long类型时间戳
	 * @param currentZone 当前时区
	 * @param targetZone  目标时区
	 * @param format      转换后的String类型的时间格式
	 * @return String
	 * <AUTHOR>
	 * @date 2025/3/13 14:28
	 */
	public static String convertLongTimeZoneReturnFormatString(long timestamp, String currentZone, String targetZone,
															   String format) {
		// 将时间戳转换为毫秒
		long timestampInMillis = timestamp * 1000;
		// 创建 Instant 对象
		Instant instant = Instant.ofEpochMilli(timestampInMillis);
		// 创建 ZonedDateTime 对象，指定原始时区
		ZonedDateTime zonedDateTimeCurrent = instant.atZone(ZoneId.of(currentZone));
		// 将时间转换为目标时区
		ZonedDateTime zonedDateTimeTarget = zonedDateTimeCurrent.withZoneSameInstant(ZoneId.of(targetZone));
		// 将 ZonedDateTime 转换为指定格式的字符串
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
		return zonedDateTimeTarget.format(formatter);
	}

	// 获取一年前的时间
	public static String getBeforeYearTime() {
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime beforeYear = now.minusYears(1).with(LocalTime.MIN);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return beforeYear.format(formatter);
	}

	// 获取一个月前的时间
	public static String getBeforeMonthTime() {
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime beforeMonth = now.minusDays(29).with(LocalTime.MIN);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return beforeMonth.format(formatter);
	}

	// 获取一周前的时间
	public static String getBeforeWeekTime() {
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime beforeWeek = now.minusDays(6).with(LocalTime.MIN);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return beforeWeek.format(formatter);
	}

	// 获取一天前的时间
	public static String getBeforeDayTime() {
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime beforeDay = now.with(LocalTime.MIN);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return beforeDay.format(formatter);
	}

	// 获取当天23:59:59
	public static String getNowEndTime() {
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime endOfDay = now.with(LocalTime.MAX);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return endOfDay.format(formatter);
	}

	public static String convertLocalDateTimeToStringDateTime(LocalDateTime localDateTime) {
		if (localDateTime == null) {
			throw new IllegalArgumentException("localDateTime cannot be null");
		}
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
		return localDateTime.format(formatter);
	}


	/**
	 * 标准化时区字符串
	 *
	 * @param timeZone 输入的时区字符串
	 * @return 标准化后的时区字符串
	 */
	public static String normalizeTimeZone(String timeZone) {
		if (ValidationUtil.isEmpty(timeZone)) {
			return timeZone;
		}

		// 使用正则表达式匹配时区字符串
		Pattern pattern = Pattern.compile("^(UTC)\\s*([+-]?)(\\d{1,2})(?::?(\\d{2})?)$");
		Matcher matcher = pattern.matcher(timeZone);

		if (matcher.find()) {
			String utc = matcher.group(1);
			String sign = matcher.group(2);
			String hour = matcher.group(3);
			String minute = matcher.group(4);

			// 如果分钟部分为空，则补全为 "00"
			if (minute == null || minute.isEmpty()) {
				minute = "00";
			}

			// 确保小时部分是两位数字
			if (hour.length() == 1) {
				hour = "0" + hour;
			}

			// 确保 + 或 - 号存在
			if (sign.isEmpty()) {
				sign = "+";
			}

			// 返回标准化后的时区字符串
			return utc + sign + hour + ":" + minute;
		} else {
			log.warn("Invalid time zone format: {}", timeZone);
			return timeZone; // 或者抛出异常
		}
	}

	public static Date dateAddTimeZone(Date utcDate, String targetTimeZone) {
		// 将Date转换为Instant（UTC时间点）
		Instant instant = utcDate.toInstant();

		// 将UTC时间点转换为目标时区的时间
		ZonedDateTime targetZonedTime = instant.atZone(ZoneId.of(targetTimeZone));
//		ZonedDateTime zonedDateTime = targetZonedTime.toLocalDateTime().atZone(ZoneId.of(targetTimeZone));
//		ZonedDateTime targetZonedDateTime = targetZonedTime.toLocalDateTime().atZone(ZoneId.of(targetTimeZone));
//		log.info("originalZonedTime , originalInstant , date : {} , {} , {}",targetZonedTime,targetZonedTime.toInstant(),Date.from(targetZonedTime.toInstant()));
//		log.info("systemZonedDateTime , systemInstant , date : {} , {} , {}",zonedDateTime,zonedDateTime.toInstant(),Date.from(zonedDateTime.toInstant()));
//		log.info("targetZonedDateTime , targetInstant , date : {} , {} , {}",targetZonedDateTime,targetZonedDateTime.toInstant(),Date.from(targetZonedDateTime.toInstant()));
//		return Date.from(zonedDateTime.toInstant());
		// 将目标时区的时间转换为字符串
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String format = targetZonedTime.format(formatter);
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Date targetDate = sdf.parse(format);
			log.info("原始时间 和 转换后的时间：{}， {}", utcDate, targetDate);
			return targetDate;
		} catch (ParseException e) {
			log.error("转换时间失败{}", e.getMessage());
		}
		return utcDate;
	}

//	public static void main(String[] args) throws ParseException {
//		LocalDateTime localDateTime = LocalDateTime.of(2025, 8, 26, 10, 30, 0);
//		ZonedDateTime utcDateTime = localDateTime.atZone(ZoneId.of("UTC"));
//		Date utcDate = Date.from(utcDateTime.toInstant());
//		Date date1 = dateAddTimeZone(utcDate, "UTC+02:00");
//	}
}
