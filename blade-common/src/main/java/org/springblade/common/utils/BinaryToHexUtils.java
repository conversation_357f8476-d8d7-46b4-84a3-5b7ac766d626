package org.springblade.common.utils;

import java.math.BigInteger;

/**
 * 进制转换
 * */
public class BinaryToHexUtils {

	/**
	 * 二进制转16进制
	 * */
	public static String binaryToHex(String binaryString) {
		int decimal = Integer.parseInt(binaryString, 2);
		String hexString = Integer.toHexString(decimal);
		return hexString;
	}

	/**
	 * 16进制转二进制
	 * */
	public static String hexToBinary(String hexString) {
		int decimal = Integer.parseInt(hexString, 16);
		return Integer.toBinaryString(decimal);
	}

	/**
	 * 16进制转10进制
	 * */
	public static int hexToDecimal(String hexString) {
		return Integer.parseInt(hexString, 16);
	}

	/**
	 * 10进制转16进制
	 * */
	public static String decimalToHex(int decimal) {
		return Integer.toHexString(decimal);
	}

	/**
	 * 二进制转10进制
	 * */
	public static int binaryToDecimal(String binaryString) {
		return Integer.parseInt(binaryString, 2);
	}

	/**
	 * 10进制转二进制
	 * */
	public static String decimalToBinary(int decimal) {
		return Integer.toBinaryString(decimal);
	}

	/**
	 *  16进制转Ascii
	 * */
	public static char hexToAscii(String hex){
		int decimal = Integer.parseInt(hex, 16);
		return (char) decimal;
	}

	/**
	 * 16进制转Ascii
	 * */
	public static String hex16ToAscii(String hexString){
		StringBuilder sb = new StringBuilder();

		for (int i = 0; i < hexString.length(); i += 2) {
			String hex = hexString.substring(i, i + 2);
			int decimal = Integer.parseInt(hex, 16);
			sb.append((char) decimal);
		}
		return sb.toString();

	}

	/**
	 * 俩个16进制相加
	 * */
	public static String HexadecimalAddition(String hex1,String hex2){
		// 将两个16进制数转换为十进制
		int dec1 = Integer.parseInt(hex1, 16);
		int dec2 = Integer.parseInt(hex2, 16);
		// 相加得到十进制结果
		int sum = dec1 + dec2-1;
		return Integer.toHexString(sum).toUpperCase();
	}

	/**
	 * 俩个16进制相加 获取长度
	 * */
	public static String HexadecimalAdditionLen(String hex1,String hex2){
		// 将两个16进制数转换为十进制
		int dec1 = Integer.parseInt(hex1, 16);
		int dec2 = Integer.parseInt(hex2, 16);
		// 相加得到十进制结果
		int sum = dec1 + dec2;
		return Integer.toHexString(sum).toUpperCase();
	}

	/**
	 * 俩个16进制相减
	 * */
	public static String HexadecimalSubtract(String hex2,String hex1){

		// 将十六进制数转换为整数
		int num1 = Integer.parseInt(hex1, 16);
		int num2 = Integer.parseInt(hex2, 16);

		// 执行减法运算
		int result = num2 - num1;

		// 将结果转换回十六进制表示
		return Integer.toHexString(result).toUpperCase();

	}

	/**
	 * 俩个16进制相减
	 * */
	public static int HexadecimalSubtractLen(String hex2,String hex1){
		// 将两个16进制数转换为十进制
		int num1 = Integer.parseInt(hex1, 16);
		int num2 = Integer.parseInt(hex2, 16);
		// 相加得到十进制结果
		return  num2 - num1;
	}

	/**
	 * 16进制数据转10进制数据有符号位
	 * */
	public static int hexToDecimalForSymbol(String hexString) {
		BigInteger number = new BigInteger(hexString, 16);
	    return number.intValue();
	}

	public static short hexToShortForSymbol(String hexString) {
		return (short) Integer.parseInt(hexString, 16);
	}


	/**
	 * 二进制转字符串
	 * */
	public static String binaryToText(String binary) {
		StringBuilder stringBuilder = new StringBuilder();
		int length = binary.length();

		for (int i = 0; i < length; i += 8) {
			String binaryByte = binary.substring(i, Math.min(i + 8, length));
			int decimal = Integer.parseInt(binaryByte, 2);
			char character = (char) decimal;
			stringBuilder.append(character);
		}

		return stringBuilder.toString();
	}

	public static String hexToBinaryByRepairZero(String hexNumber) {
		String binaryNumber = hexToBinary2(hexNumber);
		return padWithZeros(binaryNumber, 16);
	}

	private static String hexToBinary2(String hexNumber) {
		StringBuilder binaryNumber = new StringBuilder();
		for (int i = 0; i < hexNumber.length(); i++) {
			char hexDigit = hexNumber.charAt(i);
			int decimalValue = Character.digit(hexDigit, 16);
			String binaryDigit = Integer.toBinaryString(decimalValue);
			binaryNumber.append(padWithZeros(binaryDigit, 4));
		}
		return binaryNumber.toString();
	}

	private static String padWithZeros(String binaryNumber, int length) {
		int numZerosToAdd = length - binaryNumber.length();
		StringBuilder paddedBinaryNumber = new StringBuilder();
		for (int i = 0; i < numZerosToAdd; i++) {
			paddedBinaryNumber.append('0');
		}
		paddedBinaryNumber.append(binaryNumber);
		return paddedBinaryNumber.toString();
	}
}
