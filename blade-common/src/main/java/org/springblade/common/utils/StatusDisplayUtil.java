package org.springblade.common.utils;

import org.springblade.common.constant.BizConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.vo.UserTypeEnum;
import org.springblade.core.tool.constant.RoleConstant;
import org.springblade.core.tool.utils.CollectionUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.StringUtil;

import java.math.BigDecimal;

/**
 * 用户信息工具类
 *
 * @ClassName UserUtil
 * @Description
 * <AUTHOR>
 * @Date 2025/4/19 9:27
 */
public class StatusDisplayUtil {
	private static final String USER_ROLE_CLIENT = "client_user";

	public static String getRoleType(String userRoleNames, String deptIds) {
		// 空值检查
		if (StringUtil.isBlank(userRoleNames) || StringUtil.isBlank(deptIds)) {
			// 默认返回 USER_TYPE_USER
			return UserTypeEnum.USER.getType();
		}
		// 转换 userRoleNames 为数组并缓存结果
		String[] roleArray = Func.toStrArray(userRoleNames);
		try {
			// 判断是否为管理员角色
			if (isAdministrator(roleArray)) {
				return UserTypeEnum.ADMIN.getType();
			}
			// 判断是否为代理角色
			if (!deptIds.isEmpty() && !CommonConstant.DEFAULT_VALUE_MINUS_ONE.equals(deptIds)) {
				return UserTypeEnum.AGENT.getType();
			}
			// 判断是否为客户端用户
			if (USER_ROLE_CLIENT.equalsIgnoreCase(userRoleNames.trim())) {
				return UserTypeEnum.USER.getType();
			}
		} catch (Exception e) {
			// 捕获异常并记录日志（实际项目中应使用日志框架）,默认返回 USER_TYPE_USER
			return UserTypeEnum.USER.getType();
		}
		// 默认返回 USER_TYPE_USER
		return UserTypeEnum.USER.getType();
	}

	/**
	 * 判断是否是管理员
	 *
	 * @param roleArray 入参
	 * @return boolean
	 * <AUTHOR>
	 * @date 2025/4/19 10:05
	 */
	private static boolean isAdministrator(String[] roleArray) {
		return CollectionUtil.contains(roleArray, RoleConstant.ADMIN) ||
			CollectionUtil.contains(roleArray, RoleConstant.ADMINISTRATOR);
	}

	/**
	 * 转换站点&逆变器状态
	 * 根据用户类型和报警类型来转换站点状态值
	 *
	 * @param status         站点&逆变器状态，需要转换的状态值
	 * @param userType       用户类型，决定转换逻辑的一个关键参数
	 * @param userAlarType   用户报警类型，用于判断是否需要转换状态
	 * @param agentAlarmType 代理报警类型，用于判断代理类型的用户是否需要转换状态
	 * @return 转换后的站点&逆变器状态如果输入参数为空或不符合转换条件，则返回原始的plantStatus
	 */
	public static String plantAndInverterStatusConvert(String status, String userType, Integer userAlarType,
													   Integer agentAlarmType) {
		// 参数空值检查
		if (status == null || userType == null || userAlarType == null || agentAlarmType == null) {
			// 如果任意参数为空，直接返回原始 plantStatus
			return status;
		}
		// 提取常量以减少重复代码
		final String charTwo = BizConstant.CHAR_TWO;
		final String charOne = BizConstant.CHAR_ONE;
		// 判断 plantStatus 是否为 BizConstant.CHAR_TWO
		if (!charTwo.equalsIgnoreCase(status)) {
			// 如果不是 CHAR_TWO，直接返回原始值
			return status;
		}
		// 根据 userType 判断逻辑
		if (UserTypeEnum.USER.getType().equalsIgnoreCase(userType)) {
			return BizConstant.NUMBER_ONE.equals(userAlarType) ? charTwo : charOne;
		} else if (UserTypeEnum.AGENT.getType().equalsIgnoreCase(userType)) {
			return BizConstant.NUMBER_ONE.equals(agentAlarmType) ? charTwo : charOne;
		}
		// 默认返回 status
		return status;
	}

	/**
	 * 转换电池状态
	 *
	 * @param status         站点状态，需要转换的状态值
	 * @param userType       用户类型，决定转换逻辑的一个关键参数
	 * @param userAlarType   用户报警类型，用于判断是否需要转换状态
	 * @param agentAlarmType 代理报警类型，用于判断代理类型的用户是否需要转换状态
	 * @return 转换后的站点状态如果输入参数为空或不符合转换条件，则返回原始的plantStatus
	 */
	public static String batteryStatusConvert(String status, String userType, Integer userAlarType,
											  Integer agentAlarmType, BigDecimal batteryPower) {
		// 参数空值检查
		if (status == null || userType == null || userAlarType == null || agentAlarmType == null || batteryPower == null) {
			// 如果任意参数为空，直接返回原始 plantStatus
			return status;
		}
		// 判断 plantStatus 是否为告警状态，告警状态需要根据登录角色区分
		if (!BizConstant.CHAR_FOUR.equalsIgnoreCase(status)) {
			// 如果不是 CHAR_TWO，直接返回原始值
			return status;
		}
		// 根据 userType 判断逻辑
		if (UserTypeEnum.USER.getType().equalsIgnoreCase(userType)) {
			return batteryStatusConvertByRole(batteryPower, userAlarType);
		} else if (UserTypeEnum.AGENT.getType().equalsIgnoreCase(userType)) {
			return batteryStatusConvertByRole(batteryPower, agentAlarmType);
		} else {
			return status;
		}
	}

	/**
	 * 用户告警状态为告警，则返回告警，如果用户告警状态不为告警，则根据batteryPower判断当前是充电还是放电
	 *
	 * @param batteryPower 电池容量
	 * @param alarType     报警类型
	 * @return 转换后的电池状态
	 */
	private static String batteryStatusConvertByRole(BigDecimal batteryPower, Integer alarType) {
		if (BizConstant.NUMBER_ONE.equals(alarType)) {
			return BizConstant.CHAR_FOUR;
		}
		// 根据batteryPower判断当前是充电还是放电,大于0是放电，小于0是充电，0是在线
		if (batteryPower.compareTo(BigDecimal.ZERO) > 0) {
			return BizConstant.CHAR_TWO;
		} else if (batteryPower.compareTo(BigDecimal.ZERO) < 0) {
			return BizConstant.CHAR_ONE;
		} else {
			return BizConstant.CHAR_THREE;
		}
	}
}
