#使用基础镜像eclipse-temurin:11-jdk-alpine作为最终镜像的基础镜像
FROM eclipse-temurin:11-jdk-alpine

#创建目录
RUN mkdir -p /blade/xxljob-admin

#设置工作目录
WORKDIR /blade/xxljob-admin

#将构建上下文中匹配target/*.jar的文件复制到容器内的app.jar。
ADD ./target/blade-xxljob-admin.jar ./app.jar

#设置镜像的作者。
MAINTAINER skyworth_energy

#设置环境变量TZ为UTC，用于指定时区
ENV TZ=UTC

#替换Alpine Linux的软件源为中国科技大学的镜像源，然后升级系统并安装tzdata、curl和sudo。
#RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories \
#    && apk -U upgrade && apk add tzdata curl sudo \

#将容器内的时区设置为${TZ}所指定的时区。
RUN cp /usr/share/zoneinfo/${TZ} /etc/localtime && echo ${TZ} > /etc/timezone

EXPOSE 7009


#RUN addgroup -S spring && adduser -S spring -G spring
#RUN mkdir -p /etc/sudoers.d && echo "spring ALL=(ALL) NOPASSWD: ALL" > /etc/sudoers.d/spring && chmod 0440 /etc/sudoers.d/spring
#USER spring:spring

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar", "--blade-xxljob-admin"]

CMD ["--spring.profiles.active=dev", "--spring.cloud.nacos.config.namespace=public", "--spring.cloud.nacos.discovery.namespace=public"]
