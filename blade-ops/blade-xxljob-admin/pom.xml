<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>blade-ops</artifactId>
        <groupId>org.springblade</groupId>
        <version>3.1.1.RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>blade-xxljob-admin</artifactId>
    <name>${project.artifactId}</name>
    <version>${bladex.project.version}</version>
    <packaging>jar</packaging>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>

        <xxl-rpc.version>1.5.0</xxl-rpc.version>

        <spring.version>4.3.25.RELEASE</spring.version>
        <spring-boot.version>1.5.22.RELEASE</spring-boot.version>
        <mybatis-spring-boot-starter.version>1.3.5</mybatis-spring-boot-starter.version>
        <mysql-connector-java.version>8.0.13</mysql-connector-java.version>

        <slf4j-api.version>1.7.29</slf4j-api.version>
        <junit.version>4.12</junit.version>

        <groovy.version>2.5.8</groovy.version>

        <maven-source-plugin.version>3.2.0</maven-source-plugin.version>
        <maven-javadoc-plugin.version>3.1.1</maven-javadoc-plugin.version>
        <maven-gpg-plugin.version>1.6</maven-gpg-plugin.version>
        <maven-war-plugin.version>3.2.3</maven-war-plugin.version>
    </properties>


    <dependencies>
        <!--Blade-->
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-cloud</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-launch</artifactId>
        </dependency>
        <!-- freemarker-starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- mail-starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <!-- starter-actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- mybatis-starter：mybatis + mybatis-spring + tomcat-jdbc（default） -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot-starter.version}</version>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
<!--            <version>2.2.0</version>-->
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <skip>${docker.fabric.skip}</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>1.4.13</version>
                <configuration>
                    <username>${docker.repositories.configuration.username}</username>
                    <password>${docker.repositories.configuration.password}</password>
                    <repository>${docker.repositories.configuration.repository}blade-xxljob-admin</repository>
                    <tag>${docker.repositories.configuration.tag}</tag>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <buildArgs>
                        <JAR_FILE>target/app.jar</JAR_FILE>
                    </buildArgs>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
