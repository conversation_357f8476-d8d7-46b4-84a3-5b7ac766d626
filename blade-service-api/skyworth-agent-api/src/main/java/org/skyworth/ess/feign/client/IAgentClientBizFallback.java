package org.skyworth.ess.feign.client;

import cn.hutool.json.JSONObject;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
public class IAgentClientBizFallback implements IAgentClientBiz {
	@Override
	public R<List<AgentCompanyVO>> agentCompany(String companyName) {
		return null;
	}

	@Override
	public R<String> agentDeptId(String agentNumber) {
		return null;
	}

	@Override
	public R<AgentCompanyVO> agentNumber(String agentNumber) {
		return null;
	}

	@Override
	public R<List<JSONObject>> findAgentUserList(String roleCode, Long agentId, String realName) {
		return null;
	}


	@Override
	public R<JSONObject> getQcSubNodeAuditInfo(String taskId) {
		return null;
	}

	@Override
	public R cleanUpAgentUser(List<JSONObject> jsonObjectList) {
		return null;
	}

	@Override
	public R<List<AgentCompanyVO>> agentCompanyInfoByIds(List<Long> ids) {
		return null;
	}


	@Override
	public R<List<AgentCompanyVO>> agentCompanyByDeptId(String deptId, Long userId) {
		return null;
	}

	@Override
	public R<List<AgentUserVo>> agentUserInfo(Long agentId, Long deptId) {
		return null;
	}

	@Override
	public R<Boolean> saveAgentUserInfo(AgentUserInfoEntity agentUserInfoEntity) {
		return null;
	}


	@Override
	public R<Boolean> deleteAgentUserInfo(@RequestParam("userIdList") List<Long> userIdList) {
		throw new RuntimeException();
	}
}
