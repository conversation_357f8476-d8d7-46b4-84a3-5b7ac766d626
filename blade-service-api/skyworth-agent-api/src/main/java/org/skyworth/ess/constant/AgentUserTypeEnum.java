package org.skyworth.ess.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: SDT50545
 * @since: 2023-11-27 15:53
 **/
@Getter
@AllArgsConstructor
public enum AgentUserTypeEnum {
	/**
	 * 施工人员
	 */
	CONSTRUCTOR("constructor", "008"),
	/**
	 * 电工
	 */
	ELECTRICIAN("electrician", "009"),
	/**
	 * 代理商交付经理
	 */
	ROLLOUT_MANAGER_DISTRIBUTOR("rolloutManager", "018");
	final String code;
	final String roleCode;

	public static AgentUserTypeEnum of(String code) {
		if (code == null) {
			return null;
		}
		AgentUserTypeEnum[] values = AgentUserTypeEnum.values();
		for (AgentUserTypeEnum agentUserTypeEnum : values) {
			if (agentUserTypeEnum.code.equals(code) || agentUserTypeEnum.roleCode.equalsIgnoreCase(code)) {
				return agentUserTypeEnum;
			}
		}
		return null;
	}
}
