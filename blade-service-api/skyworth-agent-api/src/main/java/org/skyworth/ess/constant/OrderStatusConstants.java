package org.skyworth.ess.constant;

/**
 * OrderStatusConstants
 * org.skyworth.ess.entity
 *
 * <AUTHOR>
 * @since 2023/11/16 - 11 - 16
 */
public class OrderStatusConstants {
	public static final String ORDER_STATUS_CLOSED = "cancel";

	public static final String MAINTENANCE = "maintenance";

	public static final String FINAL_NODE = "officialCoc";

	public static final String ORDER_STATUS_CLOSED_NULL = "";

	public static final String CURRENT_ROLE = "role";

	public static final String CURRENT_USER = "user";

	public static final String CURRENT_USER_ROLE = "user/role";

	//上一步审批状态（1:通过；2：不通过；3：待审批）
	public static final Integer AUDIT_STATUS_PASS = 1;
	public static final Integer AUDIT_STATUS_NO_PASS = 2;
	public static final Integer AUDIT_STATUS_WAIT_PASS = 2;


	//审批状态（1:通过；2：不通过；3：取消）
	public static final String EXAMINE_APPROVE_PASS = "1";
	public static final String EXAMINE_APPROVE_NOT_PASS = "2";
	public static final String EXAMINE_APPROVE_CANCEL = "3";
	public static final String WF_CURRENT_STATUS_QUOTES = "quotes";
}
