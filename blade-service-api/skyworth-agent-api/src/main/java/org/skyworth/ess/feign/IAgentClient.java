package org.skyworth.ess.feign;


import cn.hutool.json.JSONObject;
import org.skyworth.ess.dto.OrderWorkFlowDTO;
import org.skyworth.ess.entity.AgentUserInfoEntity;
import org.skyworth.ess.vo.AgentCompanyVO;
import org.skyworth.ess.vo.AgentUserVo;
import org.skyworth.ess.vo.OrderWorkFlowVO;
import org.springblade.core.tool.api.R;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 代理商公司信息表 Feign接口类
 * org.skyworth.ess.companyInfo.feign
 *
 * <AUTHOR>
 * @since 2023/11/6
 */

@FeignClient(
	value = "skyworth-agent",
	fallback = IAgentClientFallback.class
)
public interface IAgentClient {
	String API_PREFIX = "/companyInfo";

	String API_WORK_FLOW_PREFIX = "/blade-orderWorkFlow/orderWorkFlow";

	String AGENT_COMPANY_INFO = API_PREFIX + "/getListCompanyName";

	String AGENT_DEPT_ID = API_PREFIX + "/getAgentDeptId";

	String AGENT_NUMBER = API_PREFIX + "/getAgentNumber";

	String AGENT_USER_LIST = API_PREFIX + "/findAgentUserList";

	String ORDER_WORK_FLOW_INFO = API_WORK_FLOW_PREFIX + "/getWorkFlowDataByOrderId";

	String QC_SUB_NODE_AUDIT_INFO = API_WORK_FLOW_PREFIX + "/getQcSubNodeAuditInfo";

	String AGENT_CLEAN_USER = API_PREFIX + "/cleanUser";

	String AGENT_COMPANY_INFO_BY_ID = API_PREFIX + "/agentCompanyInfo";

	String AGENT_USER_INFO_BY_ID = API_PREFIX + "/agentUserInfo";

	String AGENT_COMPANY_INFO_BY_DEPT_ID = API_PREFIX + "/agentCompanyByDeptId";
	String SAVE_AGENT_USER_INFO = API_PREFIX + "/saveAgentUserInfo";
	String DELETE_AGENT_USER_INFO = API_PREFIX + "/deleteAgentUserInfo";

	/**
	 * 获取代理商公司信息
	 *
	 * @param companyName 公司名称
	 * @return R
	 */
	@GetMapping(AGENT_COMPANY_INFO)
	R<List<AgentCompanyVO>> agentCompany(@RequestParam("companyName") String companyName);

	/**
	 * 获取代理商部门id
	 *
	 * @param agentNumber 公司名称
	 * @return R
	 */
	@GetMapping(AGENT_DEPT_ID)
	R<String> agentDeptId(@RequestParam("agentNumber") String agentNumber);

	/**
	 * 精确匹配获取代理商编码
	 *
	 * @param agentNumber 公司名称
	 * @return R
	 */
	@GetMapping(AGENT_NUMBER)
	R<AgentCompanyVO> agentNumber(@RequestParam("agentNumber") String agentNumber);

	/**
	 * 通过角色+代理商id查询代理商下面的人员
	 *
	 * @param roleCode 角色编码
	 * @param agentId  入参
	 * @param realName 名字
	 * @return R<List < JSONObject>>
	 * <AUTHOR>
	 * @since 2023/11/29 10:56
	 **/
	@GetMapping(AGENT_USER_LIST)
	R<List<JSONObject>> findAgentUserList(@RequestParam("roleCode") String roleCode, @RequestParam("agentId") Long agentId, @RequestParam("realName") String realName);


	/**
	 * 查询订单流程信息
	 *
	 * @param orderWorkFlow 订单流程信息条件
	 * @return R<OrderWorkFlowDTO> 查询订单流程详情
	 */
	@PostMapping(ORDER_WORK_FLOW_INFO)
	R<OrderWorkFlowVO> getWorkFlowDataByOrderId(@RequestBody OrderWorkFlowDTO orderWorkFlow);


	/**
	 * 查询QC子节点信息
	 */
	@GetMapping(QC_SUB_NODE_AUDIT_INFO)
	R<JSONObject> getQcSubNodeAuditInfo(@RequestParam("taskId") String taskId);

	/**
	 * 角色注销，切换，删除，清理代理商下面的安装人员和电气工程师
	 *
	 * @param jsonObjectList json:userId/userType, userType:electrician/constructor/all,电气工程师/施工人员/所有
	 * @return R
	 * <AUTHOR>
	 * @since 2024/1/12 15:47
	 **/
	@PostMapping(AGENT_CLEAN_USER)
	R cleanUpAgentUser(@RequestBody List<JSONObject> jsonObjectList);


	/**
	 * 获取代理商公司信息
	 *
	 * @param ids 主键id
	 * @return R
	 */
	@PostMapping(AGENT_COMPANY_INFO_BY_ID)
	R<List<AgentCompanyVO>> agentCompanyInfoByIds(@RequestBody List<Long> ids);

	/**
	 * 获取运维人员信息
	 *
	 * @param agentId
	 * @param deptId
	 * @return R
	 */
	@GetMapping(AGENT_USER_INFO_BY_ID)
	R<List<AgentUserVo>> agentUserInfo(@RequestParam("agentId") Long agentId, @RequestParam("deptId") Long deptId);

	/**
	 * 根据部门id以及用户id获取运维团队信息
	 *
	 * @param deptId
	 * @param userId
	 * @return R
	 */
	@GetMapping(AGENT_COMPANY_INFO_BY_DEPT_ID)
	R<List<AgentCompanyVO>> agentCompanyByDeptId(@RequestParam("deptId") String deptId, @RequestParam("userId") Long userId);

	@PostMapping(SAVE_AGENT_USER_INFO)
	R<Boolean> saveAgentUserInfo(@RequestBody AgentUserInfoEntity agentUserInfoEntity);

	@PostMapping(DELETE_AGENT_USER_INFO)
	R<Boolean> deleteAgentUserInfo(@RequestParam("userIdList") List<Long> userIdList);
}
