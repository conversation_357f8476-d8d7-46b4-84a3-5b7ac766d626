package org.skyworth.ess.device.entity;

public enum DictErrorCode2Enum {

	GridVoltage(0,"A0 - Grid over voltage","A0 - Netzüber Spannung"),
	GridUnderVoltage(1,"A1 - Grid under voltage","A1 - Netzunterspannung"),
	absent(2,"A2 - Grid absent","A2 - Netz abwesend"),
	overFrequency(3,"A3 - Grid over frequency","A3 - Netzüberfrequenz"),
	underFrequency(4,"A4 - Grid under frequency","A4 - Netzunterfrequenz"),
	overVoltage(5,"B0 - PV over voltage","B0 - PV über Spannung"),
	insulationAbnormal(6,"B1 - PV insulation abnormal","B1 - PV-Isolierung abnormal"),
	currentAbnormal(7,"B2 - Leakage current abnormal ","B2 - <PERSON><PERSON><PERSON> abnormal"),
	limitState(8,"CL - Inverter in power limit state","CL - Wechselrichter im Leistungsbeschränkungsstatus"),
	supplyAbnormal(9,"C0 - Internal power supply abnormal","C0 - Interner Stromversorgungsfehler"),
	stringAbnormal(10,"B3 - PV string abnormal","B3 - PV-String abnormal"),
	underVoltage(11,"B4 - PV under voltage","B4 - PV-Unterspannung"),
	irradiationWeak(12,"B5 - PV irradiation weak","B5 - PV-Bestrahlung schwach"),
	GridAbnormal(13,"A6 - Grid abnormal","A6 - Netz abnormal"),
	faultDetection (14,"C1 - Arc fault detection","C1 - ARC-Fehlererkennung"),
	voltageHigh(15,"A7 - AC moving average voltage high","A7 - AC gleitender Durchschnittsspannung hoch");

	private int code;
	private String message;

	private String deMessage;

	private DictErrorCode2Enum(int code, String message,String deMessage) {
		this.code = code;
		this.message = message;
		this.deMessage = deMessage;
	}

	public static String switchLanguage(String language,int key) {
		String result = "";

		for (DictErrorCode2Enum s : values()) {
			if (s.getCode()==key) {
				if("de".equals(language)){
					result = s.getDeMessage();
				}else {
					result = s.getMessage();
				}
				break;
			}
		}

		return result;
	}

	public String getDeMessage() {
		return deMessage;
	}

	public void setDeMessage(String deMessage) {
		this.deMessage = deMessage;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictErrorCode2Enum match(int key) {

		DictErrorCode2Enum result = null;

		for (DictErrorCode2Enum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictErrorCode2Enum catchMessage(String msg) {

		DictErrorCode2Enum result = null;

		for (DictErrorCode2Enum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
