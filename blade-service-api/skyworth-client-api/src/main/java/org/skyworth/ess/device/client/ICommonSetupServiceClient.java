package org.skyworth.ess.device.client;

import com.alibaba.fastjson.JSONObject;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 用于其他模块调用client模块的下发设置feign接口
 */
@FeignClient(
	value = "skyworth-client",
	fallback = CommonSetupServiceClientCallBack.class
)
public interface ICommonSetupServiceClient {

	String API_PREFIX = "/commonSetup";
	String issue = API_PREFIX + "/invokeIssue";

	@PostMapping(issue)
	R invokeIssueInterface(@RequestBody JSONObject jsonObject);
}
