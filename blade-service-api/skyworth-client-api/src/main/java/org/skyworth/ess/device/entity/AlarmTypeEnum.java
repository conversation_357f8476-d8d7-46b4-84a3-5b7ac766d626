package org.skyworth.ess.device.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 告警类型枚举
 *
 * <AUTHOR>
 * @date 2025/4/18 9:26
 */
@Getter
@AllArgsConstructor
public enum AlarmTypeEnum {
	DEVICE("device", "智能能量变换器"),
	BATTERY("battery", "储能包"),
	GUARDIAN("guardian", "安全卫士"),
	OTHER("other", "其他");
	final String type;
	final String desc;
	public static AlarmTypeEnum of(String type) {
		if (type == null) {
			return null;
		}
		AlarmTypeEnum[] values = AlarmTypeEnum.values();
		for (AlarmTypeEnum alarmTypeEnum : values) {
			if (alarmTypeEnum.type.equals(type)) {
				return alarmTypeEnum;
			}
		}
		return null;
	}
}
