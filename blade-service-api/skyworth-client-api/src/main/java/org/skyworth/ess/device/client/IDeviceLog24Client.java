/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.client;

import org.skyworth.ess.device.entity.DeviceLog24Entity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * DeviceLog24 Feign接口类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@FeignClient(
    value = "skyworth-client",
	fallback = IDeviceLog24Fallback.class
)
public interface IDeviceLog24Client {

	String API_PREFIX = "/deviceLog";
	String device24 = API_PREFIX + "/24/insert";

	String NETWORK = API_PREFIX + "/24/insert/network";
    /**
     * 获取DeviceLog24列表
     *
     * @return BladePage
     */
	@PostMapping(device24)
	void insertBatchDeviceLog24Column(@RequestBody List<DeviceLog24Entity> deviceLog24Entities);

	@PostMapping(NETWORK)
	void insertBatchDeviceLog24ForNetWork(@RequestBody List<DeviceLog24Entity> deviceLog24Entities);

}
