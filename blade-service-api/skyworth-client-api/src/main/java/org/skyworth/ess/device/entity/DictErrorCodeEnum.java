package org.skyworth.ess.device.entity;

public enum DictErrorCodeEnum {

	DcBias(0,"C2 - Inverter over dc-bias current","C2 - Wechselrichter über Gleichstrom-Bias-Strom"),
	abnormal(1,"C3 - Inverter relay abnormal","C3 - Wechselrichter-Relais abnormal"),
	Remote(2,"Cn - Remote off","Cn - Fernabschaltung"),
	temperature(3,"C5 - Inverter over temperature","C5 - Wechselrichter über Temperatur"),
	GFCI_abnormal(4,"C6 - GFCI abnormal","C6 - GFCI abnormal"),
	PV_reverse(5,"B7 - PV string reverse ","B7 - PV-String umgekehrt"),
	Type_error(6,"C7 - System type error","C7 - Systemtypfehler"),
	Fan_abnormal(7,"C8 - Fan abnormal","C8 - Lüfter abnormal"),
	under_voltage(8,"C9 - Dc-link unbalance or under voltage","C9 - DC-Link-Unwucht oder Unterspannung"),
	over_voltage(9,"CA - Dc-link over voltage","CA - DC-Link Über Spannung"),
	communication_error(10,"Cb - Internal communication error","Cb - Interner Kommunikationsfehler"),
	Software_incompatibility(11,"CC - Software incompatibility","CC - Softwareinkompatibilität"),
	storage_error(12,"Cd - Internal storage error","Cd - Interner Speicherfehler"),
	Data_inconsistency(13,"CE - Data inconsistency","CE - Dateninkonsistenz"),
	Inverter_abnormal (14,"CF - Inverter abnormal","CF - Wechselrichter abnormal"),
	Boost_abnormal(15,"CG - Boost abnormal","CG - Boost abnormal");

	private int code;
	private String message;

	private String deMessage;



	private DictErrorCodeEnum(int code, String message,String deMessage) {
		this.code = code;
		this.message = message;
		this.deMessage=deMessage;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getDeMessage() {
		return deMessage;
	}

	public void setDeMessage(String deMessage) {
		this.deMessage = deMessage;
	}


	public static String switchLanguage(String language,int key) {
		String result = "";

		for (DictErrorCodeEnum s : values()) {
			if (s.getCode()==key) {
				if("de".equals(language)){
					result = s.getDeMessage();
				}else {
					result = s.getMessage();
				}
				break;
			}
		}

		return result;
	}

	public static DictErrorCodeEnum match(int key) {

		DictErrorCodeEnum result = null;

		for (DictErrorCodeEnum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictErrorCodeEnum catchMessage(String msg) {

		DictErrorCodeEnum result = null;

		for (DictErrorCodeEnum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
