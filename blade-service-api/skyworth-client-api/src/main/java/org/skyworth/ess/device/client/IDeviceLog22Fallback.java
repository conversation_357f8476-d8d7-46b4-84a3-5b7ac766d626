/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.client;

import org.skyworth.ess.device.entity.Device21Entity;
import org.skyworth.ess.device.entity.DeviceLog21;
import org.skyworth.ess.device.entity.DeviceLog22;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;

/**
 * Feign失败配置
 *
 * <AUTHOR>
 */
@Component
public class IDeviceLog22Fallback implements IDeviceLog22Client {

	@Override
	public List<DeviceLog22> queryDeviceLog22list(List<DeviceLog22> deviceLog22List) {
		return new ArrayList<>();
	}

}
