package org.skyworth.ess.device.entity;

public enum DictErrorMessageEnum {

	absent(0,"D1 - Battery absent","D1 - Batterie abwesend"),
	over_voltage(1,"D2 - Battery over voltage","D2 - Batterieüber Spannung"),
	under_voltage(2,"D3 - Battery under voltage","D3 - Batterieunter Spannung"),
	over_current(3,"D4 - Battery discharge over current","D4 - Batterie-Entladungsüberstrom"),
	over_temperature(4,"D5 - Battery over temperature","D5 - Batterie über Temperatur"),
	under_temperature(5,"D6 - Battery under temperature","D6 - Batterie unter Temperatur"),
	wire_reversed(6,"A9 - Neutral live wire reversed ","A9 - Neutralleiter vertauscht"),
	voltage_abnormal(7,"D7 - Back up output voltage abnormal","D7 - Backup-Ausgangsspannung abnormal"),
	Inverter_BMS(8,"D8 - Communication error (Inverter-BMS)","D8 - Kommunikationsfehler (Wechselrichter-BMS)"),
	communication_loss(9,"D9 - Internal communication loss(E-M) ","D9 - Interne Kommunikationsverluste (E-M)"),
	M_D(10,"DA - Internal communication loss(M-D) ","DA - Interne Kommunikationsverluste (M-D)"),
	DCDC(11,"CU - Dcdc abnormal","CU - DCDC abnormal"),
	bias_voltage(12,"CP - Back up over dc-bias voltage","CP - Backup über Gleichstrom-Bias-Spannung"),
	circuit(13,"Db - Back up short circuit", "Db - Backup-Kurzschluss"),
	load (14,"DC - Back up over load","DC - Backup-Überlast"),
	Reserved(15,"Dn - Battery reserved","Dn - Batterie umgekehrt");

	private int code;
	private String message;

	private String deMessage;

	private DictErrorMessageEnum(int code, String message,String deMessage) {
		this.code = code;
		this.message = message;
		this.deMessage = deMessage;
	}

	public String getDeMessage() {
		return deMessage;
	}

	public void setDeMessage(String deMessage) {
		this.deMessage = deMessage;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static String switchLanguage(String language,int key) {
		String result = "";

		for (DictErrorMessageEnum s : values()) {
			if (s.getCode()==key) {
				if("de".equals(language)){
					result = s.getDeMessage();
				}else {
					result = s.getMessage();
				}
				break;
			}
		}

		return result;
	}
	public static DictErrorMessageEnum match(int key) {

		DictErrorMessageEnum result = null;

		for (DictErrorMessageEnum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictErrorMessageEnum catchMessage(String msg) {

		DictErrorMessageEnum result = null;

		for (DictErrorMessageEnum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
