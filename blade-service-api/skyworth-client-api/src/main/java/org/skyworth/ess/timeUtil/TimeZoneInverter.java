package org.skyworth.ess.timeUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class TimeZoneInverter {

	private static final String PATTERN="yyyy-MM-dd HH:mm:ss";
	public static String timeInverterToString (String timeZoneId,long timeStamp){
		ZoneId zoneId = ZoneId.of(timeZoneId, ZoneId.SHORT_IDS);
		// 将时间戳转换为指定时区的日期时间对象
		Instant instant = Instant.ofEpochMilli(timeStamp);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN);
        return formatter.format(instant.atZone(zoneId).toLocalDateTime());
	}

	public static Date timeInverterToDate (String timeZoneId, long timeStamp){
		ZoneId zoneId = ZoneId.of(timeZoneId, ZoneId.SHORT_IDS);
		// 将时间戳转换为指定时区的日期时间对象
		Instant instant = Instant.ofEpochMilli(timeStamp);
		DateTimeFormatter formatter =DateTimeFormatter.ofPattern(PATTERN);
		String time=formatter.format(instant.atZone(zoneId).toLocalDateTime());
		SimpleDateFormat sb=new SimpleDateFormat(PATTERN);
		try {
			return sb.parse(time);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

}
