package org.skyworth.ess.threadpool;

import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ThreadPoolCustom {

	private static volatile ThreadPoolExecutor threadPoolExecutor;

	public static ThreadPoolExecutor getCustomThreadPool() {
		if (threadPoolExecutor == null) {
			synchronized (ThreadPoolCustom.class) {
				if (threadPoolExecutor == null) {
					int core = Runtime.getRuntime().availableProcessors();
					ThreadFactory threadFactory = new CustomizableThreadFactory("ThreadPoolCustom");
					threadPoolExecutor = new ThreadPoolExecutor(core * 2, core * 4, 5L,
						TimeUnit.SECONDS, new ArrayBlockingQueue<>(core * 10),
						threadFactory, new ThreadPoolExecutor.DiscardPolicy());
				}
			}
		}
		return threadPoolExecutor;
	}

}
