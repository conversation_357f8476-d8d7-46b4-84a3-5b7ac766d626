package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/逆变器日志表，记录22数据;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-9-14
 */
@ApiModel(value = "设备/逆变器日志表，记录22数据",description = "")
@TableName("device_log22")
@Data
public class DeviceLog22 extends BaseEntity implements Serializable,Cloneable{

	@ApiModelProperty(name = "同步状态（N未同步；Y已同步）",notes = "")
	private String synchStatus ;

	/** 电压 phase a */
	@ApiModelProperty(name = "电压 phase a",notes = "")
	private BigDecimal phaseAVoltage ;
	/** 电流 phase a */
	@ApiModelProperty(name = "电流 phase a",notes = "")
	private BigDecimal phaseACurrent ;
	/** 功率 phase a */
	@ApiModelProperty(name = "功率 phase a",notes = "")
	private BigDecimal phaseAPower ;
	/** 频率 phase  a */
	@ApiModelProperty(name = "频率 phase  a",notes = "")
	private BigDecimal phaseAFrequency ;
	/** 电压 phase b */
	@ApiModelProperty(name = "电压 phase b",notes = "")
	private BigDecimal phaseBVoltage ;
	/** 电流 phase b */
	@ApiModelProperty(name = "电流 phase b",notes = "")
	private BigDecimal phaseBCurrent ;
	/** 功率 phase b */
	@ApiModelProperty(name = "功率 phase b",notes = "")
	private BigDecimal phaseBPower ;
	/** 频率 phase  b */
	@ApiModelProperty(name = "频率 phase  b",notes = "")
	private BigDecimal phaseBFrequency ;
	/** 电压 phase c */
	@ApiModelProperty(name = "电压 phase c",notes = "")
	private BigDecimal phaseCVoltage ;
	/** 电流 phase c */
	@ApiModelProperty(name = "电流 phase c",notes = "")
	private BigDecimal phaseCCurrent ;
	/** 功率 phase  c */
	@ApiModelProperty(name = "功率 phase  c",notes = "")
	private BigDecimal phaseCPower ;
	/** 频率 phase  c */
	@ApiModelProperty(name = "频率 phase  c",notes = "")
	private BigDecimal phaseCFrequency ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv1Voltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv1Current ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal mppt1Power ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv2Voltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv2Current ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal mppt2Power ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv3Voltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv3Current ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal mppt3Power ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal innerTemperature ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private String inverterMode ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private String errorCode ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal totalEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal totalGenerationTime ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal activePower ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal reactivePower ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayPeakPower ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal powerFactor ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv4Voltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pv4Current ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal mppt4Power ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRWattOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSWattOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTWattOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyOfPositive ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyOfNegative ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRWattOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSWattOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTWattOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1NPhaseVoltageOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2NPhaseVoltageOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3NPhaseVoltageOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1CurrentOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2CurrentOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3CurrentOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1NPhaseVoltageOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2NPhaseVoltageOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3NPhaseVoltageOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1CurrentOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2CurrentOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3CurrentOfLoad ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayImportEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayExportEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal todayLoadEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal frequencyOfGrid ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRVoltageOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRCurrentOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseRWattOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal frequencyOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSVoltageOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSCurrentOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseSWattOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTVoltageOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTCurrentOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseTWattOfEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal dailyEnergyToEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyToEps ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batterySoc ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryTemperature ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryVoltage ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryCurrent ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryPower ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryDailyChargeEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedChargeEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryDailyDischargeEnergy ;
	/**  */
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedDischargeEnergy ;
	/** 错误信息 */
	@ApiModelProperty(name = "错误信息",notes = "")
	private String errorMessage4 ;


	@ApiModelProperty(name = "",notes = "")
	private BigDecimal numberOfBattery ;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal soh ;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pvTotalInputPower ;



	public DeviceLog22(){

	}

	/** 站点ID */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(name = "站点ID",notes = "")
	private long plantId ;

	/** 设备时间，设备上报时时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(name = "设备时间，设备上报时时间",notes = "")
	private Date deviceDateTime ;

	/** modbus协议版本 */
	@ApiModelProperty(name = "modbus协议版本",notes = "")
	private String modbusProtocolVersion ;


	/** 逆变器/设备SN */
	@ApiModelProperty(name = "逆变器/设备SN",notes = "")
	private String deviceSerialNumber ;

	/** 创建人账号 */
	@ApiModelProperty(name = "创建人账号",notes = "")
	private String createUserAccount ;
	/** 更新人账号 */
	@ApiModelProperty(name = "更新人账号",notes = "")
	private String updateUserAccount ;


	@ApiModelProperty(name = "单体电池最高电压",notes = "")
	private BigDecimal batteryMaximumCellVoltage;

	@ApiModelProperty(name = "单体电池最低电压",notes = "")
	private BigDecimal batteryMinimumCellVoltage;

	@ApiModelProperty(name = "单体电池最高温度",notes = "")
	private BigDecimal batteryMaximumCellTemperature;

	@ApiModelProperty(name = "单体电池最低温度",notes = "")
	private BigDecimal batteryMinimumCellTemperature;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhaseCurrentOfGenerator ;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l1PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhaseCurrentOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l2PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhaseVoltageOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhaseCurrentOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal l3PhasePowerOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorFrequency;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTodayEnergy;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTotalEnergy;

	// L1 相交流耦合电压
	private BigDecimal l1PhaseVoltageOfAcCouple;

	// L1 相交流耦合电流
	private BigDecimal l1PhaseCurrentOfAcCouple;

	// L1 相交流耦合功率
	private BigDecimal l1PhasePowerOfAcCouple;

	// L2 相交流耦合电压
	private BigDecimal l2PhaseVoltageOfAcCouple;

	// L2 相交流耦合电流
	private BigDecimal l2PhaseCurrentOfAcCouple;

	// L2 相交流耦合功率
	private BigDecimal l2PhasePowerOfAcCouple;

	// L3 相交流耦合电压
	private BigDecimal l3PhaseVoltageOfAcCouple;

	// L3 相交流耦合电流
	private BigDecimal l3PhaseCurrentOfAcCouple;

	// L3 相交流耦合功率
	private BigDecimal l3PhasePowerOfAcCouple;

	// 交流耦合频率
	private BigDecimal frequencyOfAcCouple;

	// 今日交流耦合能量（单位：kWh）
	private BigDecimal energyTodayOfAcCoupleKwh;

	// 总交流耦合能量
	private BigDecimal energyTotalOfAcCouple;

	// 今日交流耦合能量（单位：Wh）
	private BigDecimal energyTodayOfAcCoupleWh;

	public String getBatteryStatus() {
		return batteryStatus;
	}

	public void setBatteryStatus(String batteryStatus) {
		this.batteryStatus = batteryStatus;
	}

	// 104A 中的 4-5位
	private String batteryStatus;

	// 并机 begin
	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattOfGridSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattOfGridSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattOfGridSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	// 13AC
	private BigDecimal dailyEnergyOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal monthlyEnergyOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedEnergyOfLoadSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1ApparentPowerSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2ApparentPowerSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3ApparentPowerSumOfBackup;

	@ApiModelProperty(name = "",notes = "")
	// 13C0
	private BigDecimal dailySupportEnergySumToBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal accumulatedSupportEnergySumToBackup;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1WattSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2WattSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3WattSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL1ApparentPowerSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL2ApparentPowerSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal phaseL3ApparentPowerSumOfGenerator;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTodayEnergySum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal generatorTotalEnergySum;

	@ApiModelProperty(name = "",notes = "")
	// 13D8
	private BigDecimal pvlDailyGeneratingEnergySum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal pvlAccumulatedEnergySum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal totallyInputDcWattSum;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryPowerSum;

	@ApiModelProperty(name = "",notes = "")
	// 13E0
	private BigDecimal batteryDailyChargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedChargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryDailyDischargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	private BigDecimal batteryAccumulatedDischargeEnergyParallel;

	@ApiModelProperty(name = "",notes = "")
	private String isParallelMode;
	// 并机 end
	/**
	 * 获取 l1PhaseVoltageOfAcCouple 的值
	 *
	 * @return l1PhaseVoltageOfAcCouple 的值
	 */
	public BigDecimal getL1PhaseVoltageOfAcCouple() {
		return l1PhaseVoltageOfAcCouple;
	}


	/**
	 * 设置 l1PhaseVoltageOfAcCouple 的值
	 *
	 * @param l1PhaseVoltageOfAcCouple 要设置的值
	 */
	public void setL1PhaseVoltageOfAcCouple(BigDecimal l1PhaseVoltageOfAcCouple) {
		this.l1PhaseVoltageOfAcCouple = l1PhaseVoltageOfAcCouple;
	}


	/**
	 * 获取 l1PhaseCurrentOfAcCouple 的值
	 *
	 * @return l1PhaseCurrentOfAcCouple 的值
	 */
	public BigDecimal getL1PhaseCurrentOfAcCouple() {
		return l1PhaseCurrentOfAcCouple;
	}


	/**
	 * 设置 l1PhaseCurrentOfAcCouple 的值
	 *
	 * @param l1PhaseCurrentOfAcCouple 要设置的值
	 */
	public void setL1PhaseCurrentOfAcCouple(BigDecimal l1PhaseCurrentOfAcCouple) {
		this.l1PhaseCurrentOfAcCouple = l1PhaseCurrentOfAcCouple;
	}


	/**
	 * 获取 l1PhasePowerOfAcCouple 的值
	 *
	 * @return l1PhasePowerOfAcCouple 的值
	 */
	public BigDecimal getL1PhasePowerOfAcCouple() {
		return l1PhasePowerOfAcCouple;
	}


	/**
	 * 设置 l1PhasePowerOfAcCouple 的值
	 *
	 * @param l1PhasePowerOfAcCouple 要设置的值
	 */
	public void setL1PhasePowerOfAcCouple(BigDecimal l1PhasePowerOfAcCouple) {
		this.l1PhasePowerOfAcCouple = l1PhasePowerOfAcCouple;
	}


	/**
	 * 获取 l2PhaseVoltageOfAcCouple 的值
	 *
	 * @return l2PhaseVoltageOfAcCouple 的值
	 */
	public BigDecimal getL2PhaseVoltageOfAcCouple() {
		return l2PhaseVoltageOfAcCouple;
	}


	/**
	 * 设置 l2PhaseVoltageOfAcCouple 的值
	 *
	 * @param l2PhaseVoltageOfAcCouple 要设置的值
	 */
	public void setL2PhaseVoltageOfAcCouple(BigDecimal l2PhaseVoltageOfAcCouple) {
		this.l2PhaseVoltageOfAcCouple = l2PhaseVoltageOfAcCouple;
	}


	/**
	 * 获取 l2PhaseCurrentOfAcCouple 的值
	 *
	 * @return l2PhaseCurrentOfAcCouple 的值
	 */
	public BigDecimal getL2PhaseCurrentOfAcCouple() {
		return l2PhaseCurrentOfAcCouple;
	}


	/**
	 * 设置 l2PhaseCurrentOfAcCouple 的值
	 *
	 * @param l2PhaseCurrentOfAcCouple 要设置的值
	 */
	public void setL2PhaseCurrentOfAcCouple(BigDecimal l2PhaseCurrentOfAcCouple) {
		this.l2PhaseCurrentOfAcCouple = l2PhaseCurrentOfAcCouple;
	}


	/**
	 * 获取 l2PhasePowerOfAcCouple 的值
	 *
	 * @return l2PhasePowerOfAcCouple 的值
	 */
	public BigDecimal getL2PhasePowerOfAcCouple() {
		return l2PhasePowerOfAcCouple;
	}


	/**
	 * 设置 l2PhasePowerOfAcCouple 的值
	 *
	 * @param l2PhasePowerOfAcCouple 要设置的值
	 */
	public void setL2PhasePowerOfAcCouple(BigDecimal l2PhasePowerOfAcCouple) {
		this.l2PhasePowerOfAcCouple = l2PhasePowerOfAcCouple;
	}


	/**
	 * 获取 l3PhaseVoltageOfAcCouple 的值
	 *
	 * @return l3PhaseVoltageOfAcCouple 的值
	 */
	public BigDecimal getL3PhaseVoltageOfAcCouple() {
		return l3PhaseVoltageOfAcCouple;
	}


	/**
	 * 设置 l3PhaseVoltageOfAcCouple 的值
	 *
	 * @param l3PhaseVoltageOfAcCouple 要设置的值
	 */
	public void setL3PhaseVoltageOfAcCouple(BigDecimal l3PhaseVoltageOfAcCouple) {
		this.l3PhaseVoltageOfAcCouple = l3PhaseVoltageOfAcCouple;
	}


	/**
	 * 获取 l3PhaseCurrentOfAcCouple 的值
	 *
	 * @return l3PhaseCurrentOfAcCouple 的值
	 */
	public BigDecimal getL3PhaseCurrentOfAcCouple() {
		return l3PhaseCurrentOfAcCouple;
	}


	/**
	 * 设置 l3PhaseCurrentOfAcCouple 的值
	 *
	 * @param l3PhaseCurrentOfAcCouple 要设置的值
	 */
	public void setL3PhaseCurrentOfAcCouple(BigDecimal l3PhaseCurrentOfAcCouple) {
		this.l3PhaseCurrentOfAcCouple = l3PhaseCurrentOfAcCouple;
	}


	/**
	 * 获取 l3PhasePowerOfAcCouple 的值
	 *
	 * @return l3PhasePowerOfAcCouple 的值
	 */
	public BigDecimal getL3PhasePowerOfAcCouple() {
		return l3PhasePowerOfAcCouple;
	}


	/**
	 * 设置 l3PhasePowerOfAcCouple 的值
	 *
	 * @param l3PhasePowerOfAcCouple 要设置的值
	 */
	public void setL3PhasePowerOfAcCouple(BigDecimal l3PhasePowerOfAcCouple) {
		this.l3PhasePowerOfAcCouple = l3PhasePowerOfAcCouple;
	}


	/**
	 * 获取 frequencyOfAcCouple 的值
	 *
	 * @return frequencyOfAcCouple 的值
	 */
	public BigDecimal getFrequencyOfAcCouple() {
		return frequencyOfAcCouple;
	}


	/**
	 * 设置 frequencyOfAcCouple 的值
	 *
	 * @param frequencyOfAcCouple 要设置的值
	 */
	public void setFrequencyOfAcCouple(BigDecimal frequencyOfAcCouple) {
		this.frequencyOfAcCouple = frequencyOfAcCouple;
	}


	/**
	 * 获取 energyTodayOfAcCoupleKwh 的值
	 *
	 * @return energyTodayOfAcCoupleKwh 的值
	 */
	public BigDecimal getEnergyTodayOfAcCoupleKwh() {
		return energyTodayOfAcCoupleKwh;
	}


	/**
	 * 设置 energyTodayOfAcCoupleKwh 的值
	 *
	 * @param energyTodayOfAcCoupleKwh 要设置的值
	 */
	public void setEnergyTodayOfAcCoupleKwh(BigDecimal energyTodayOfAcCoupleKwh) {
		this.energyTodayOfAcCoupleKwh = energyTodayOfAcCoupleKwh;
	}


	/**
	 * 获取 energyTotalOfAcCouple 的值
	 *
	 * @return energyTotalOfAcCouple 的值
	 */
	public BigDecimal getEnergyTotalOfAcCouple() {
		return energyTotalOfAcCouple;
	}


	/**
	 * 设置 energyTotalOfAcCouple 的值
	 *
	 * @param energyTotalOfAcCouple 要设置的值
	 */
	public void setEnergyTotalOfAcCouple(BigDecimal energyTotalOfAcCouple) {
		this.energyTotalOfAcCouple = energyTotalOfAcCouple;
	}


	/**
	 * 获取 energyTodayOfAcCoupleWh 的值
	 *
	 * @return energyTodayOfAcCoupleWh 的值
	 */
	public BigDecimal getEnergyTodayOfAcCoupleWh() {
		return energyTodayOfAcCoupleWh;
	}


	/**
	 * 设置 energyTodayOfAcCoupleWh 的值
	 *
	 * @param energyTodayOfAcCoupleWh 要设置的值
	 */
	public void setEnergyTodayOfAcCoupleWh(BigDecimal energyTodayOfAcCoupleWh) {
		this.energyTodayOfAcCoupleWh = energyTodayOfAcCoupleWh;
	}

	public BigDecimal getPvTotalInputPower() {
		return pvTotalInputPower;
	}

	public void setPvTotalInputPower(BigDecimal pvTotalInputPower) {
		this.pvTotalInputPower = pvTotalInputPower;
	}

	public BigDecimal getL1PhaseVoltageOfGenerator() {
		return l1PhaseVoltageOfGenerator;
	}

	public void setL1PhaseVoltageOfGenerator(BigDecimal l1PhaseVoltageOfGenerator) {
		this.l1PhaseVoltageOfGenerator = l1PhaseVoltageOfGenerator;
	}

	public BigDecimal getL1PhaseCurrentOfGenerator() {
		return l1PhaseCurrentOfGenerator;
	}

	public void setL1PhaseCurrentOfGenerator(BigDecimal l1PhaseCurrentOfGenerator) {
		this.l1PhaseCurrentOfGenerator = l1PhaseCurrentOfGenerator;
	}

	public BigDecimal getL1PhasePowerOfGenerator() {
		return l1PhasePowerOfGenerator;
	}

	public void setL1PhasePowerOfGenerator(BigDecimal l1PhasePowerOfGenerator) {
		this.l1PhasePowerOfGenerator = l1PhasePowerOfGenerator;
	}

	public BigDecimal getL2PhaseVoltageOfGenerator() {
		return l2PhaseVoltageOfGenerator;
	}

	public void setL2PhaseVoltageOfGenerator(BigDecimal l2PhaseVoltageOfGenerator) {
		this.l2PhaseVoltageOfGenerator = l2PhaseVoltageOfGenerator;
	}

	public BigDecimal getL2PhaseCurrentOfGenerator() {
		return l2PhaseCurrentOfGenerator;
	}

	public void setL2PhaseCurrentOfGenerator(BigDecimal l2PhaseCurrentOfGenerator) {
		this.l2PhaseCurrentOfGenerator = l2PhaseCurrentOfGenerator;
	}

	public BigDecimal getL2PhasePowerOfGenerator() {
		return l2PhasePowerOfGenerator;
	}

	public void setL2PhasePowerOfGenerator(BigDecimal l2PhasePowerOfGenerator) {
		this.l2PhasePowerOfGenerator = l2PhasePowerOfGenerator;
	}

	public BigDecimal getL3PhaseVoltageOfGenerator() {
		return l3PhaseVoltageOfGenerator;
	}

	public void setL3PhaseVoltageOfGenerator(BigDecimal l3PhaseVoltageOfGenerator) {
		this.l3PhaseVoltageOfGenerator = l3PhaseVoltageOfGenerator;
	}

	public BigDecimal getL3PhaseCurrentOfGenerator() {
		return l3PhaseCurrentOfGenerator;
	}

	public void setL3PhaseCurrentOfGenerator(BigDecimal l3PhaseCurrentOfGenerator) {
		this.l3PhaseCurrentOfGenerator = l3PhaseCurrentOfGenerator;
	}

	public BigDecimal getL3PhasePowerOfGenerator() {
		return l3PhasePowerOfGenerator;
	}

	public void setL3PhasePowerOfGenerator(BigDecimal l3PhasePowerOfGenerator) {
		this.l3PhasePowerOfGenerator = l3PhasePowerOfGenerator;
	}

	public BigDecimal getGeneratorFrequency() {
		return generatorFrequency;
	}

	public void setGeneratorFrequency(BigDecimal generatorFrequency) {
		this.generatorFrequency = generatorFrequency;
	}

	public BigDecimal getGeneratorTodayEnergy() {
		return generatorTodayEnergy;
	}

	public void setGeneratorTodayEnergy(BigDecimal generatorTodayEnergy) {
		this.generatorTodayEnergy = generatorTodayEnergy;
	}

	public BigDecimal getGeneratorTotalEnergy() {
		return generatorTotalEnergy;
	}

	public void setGeneratorTotalEnergy(BigDecimal generatorTotalEnergy) {
		this.generatorTotalEnergy = generatorTotalEnergy;
	}

	public long getPlantId() {
		return plantId;
	}

	public void setPlantId(long plantId) {
		this.plantId = plantId;
	}

	public Date getDeviceDateTime() {
		return deviceDateTime;
	}

	public void setDeviceDateTime(Date deviceDateTime) {
		this.deviceDateTime = deviceDateTime;
	}

	public String getModbusProtocolVersion() {
		return modbusProtocolVersion;
	}

	public void setModbusProtocolVersion(String modbusProtocolVersion) {
		this.modbusProtocolVersion = modbusProtocolVersion;
	}

	public String getDeviceSerialNumber() {
		return deviceSerialNumber;
	}

	public void setDeviceSerialNumber(String deviceSerialNumber) {
		this.deviceSerialNumber = deviceSerialNumber;
	}

	public String getCreateUserAccount() {
		return createUserAccount;
	}

	public void setCreateUserAccount(String createUserAccount) {
		this.createUserAccount = createUserAccount;
	}

	public String getUpdateUserAccount() {
		return updateUserAccount;
	}

	public void setUpdateUserAccount(String updateUserAccount) {
		this.updateUserAccount = updateUserAccount;
	}

	public BigDecimal getBatteryMaximumCellVoltage() {
		return batteryMaximumCellVoltage;
	}

	public void setBatteryMaximumCellVoltage(BigDecimal batteryMaximumCellVoltage) {
		this.batteryMaximumCellVoltage = batteryMaximumCellVoltage;
	}

	public BigDecimal getBatteryMinimumCellVoltage() {
		return batteryMinimumCellVoltage;
	}

	public void setBatteryMinimumCellVoltage(BigDecimal batteryMinimumCellVoltage) {
		this.batteryMinimumCellVoltage = batteryMinimumCellVoltage;
	}

	public BigDecimal getBatteryMaximumCellTemperature() {
		return batteryMaximumCellTemperature;
	}

	public void setBatteryMaximumCellTemperature(BigDecimal batteryMaximumCellTemperature) {
		this.batteryMaximumCellTemperature = batteryMaximumCellTemperature;
	}

	public BigDecimal getbatteryMinimumCellTemperature() {
		return batteryMinimumCellTemperature;
	}

	public void setbatteryMinimumCellTemperature(BigDecimal batteryMinimumCellTemperature) {
		this.batteryMinimumCellTemperature = batteryMinimumCellTemperature;
	}

	/** 同步状态（N未同步；Y已同步） */
	public String getSynchStatus(){
		return this.synchStatus;
	}
	/** 同步状态（N未同步；Y已同步） */
	public void setSynchStatus(String synchStatus){
		this.synchStatus=synchStatus;
	}

	/** 电压 phase a */
	public BigDecimal getPhaseAVoltage(){
		return this.phaseAVoltage;
	}
	/** 电压 phase a */
	public void setPhaseAVoltage(BigDecimal phaseAVoltage){
		this.phaseAVoltage=phaseAVoltage;
	}
	/** 电流 phase a */
	public BigDecimal getPhaseACurrent(){
		return this.phaseACurrent;
	}
	/** 电流 phase a */
	public void setPhaseACurrent(BigDecimal phaseACurrent){
		this.phaseACurrent=phaseACurrent;
	}
	/** 功率 phase a */
	public BigDecimal getPhaseAPower(){
		return this.phaseAPower;
	}
	/** 功率 phase a */
	public void setPhaseAPower(BigDecimal phaseAPower){
		this.phaseAPower=phaseAPower;
	}
	/** 频率 phase  a */
	public BigDecimal getPhaseAFrequency(){
		return this.phaseAFrequency;
	}
	/** 频率 phase  a */
	public void setPhaseAFrequency(BigDecimal phaseAFrequency){
		this.phaseAFrequency=phaseAFrequency;
	}
	/** 电压 phase b */
	public BigDecimal getPhaseBVoltage(){
		return this.phaseBVoltage;
	}
	/** 电压 phase b */
	public void setPhaseBVoltage(BigDecimal phaseBVoltage){
		this.phaseBVoltage=phaseBVoltage;
	}
	/** 电流 phase b */
	public BigDecimal getPhaseBCurrent(){
		return this.phaseBCurrent;
	}
	/** 电流 phase b */
	public void setPhaseBCurrent(BigDecimal phaseBCurrent){
		this.phaseBCurrent=phaseBCurrent;
	}
	/** 功率 phase b */
	public BigDecimal getPhaseBPower(){
		return this.phaseBPower;
	}
	/** 功率 phase b */
	public void setPhaseBPower(BigDecimal phaseBPower){
		this.phaseBPower=phaseBPower;
	}
	/** 频率 phase  b */
	public BigDecimal getPhaseBFrequency(){
		return this.phaseBFrequency;
	}
	/** 频率 phase  b */
	public void setPhaseBFrequency(BigDecimal phaseBFrequency){
		this.phaseBFrequency=phaseBFrequency;
	}
	/** 电压 phase c */
	public BigDecimal getPhaseCVoltage(){
		return this.phaseCVoltage;
	}
	/** 电压 phase c */
	public void setPhaseCVoltage(BigDecimal phaseCVoltage){
		this.phaseCVoltage=phaseCVoltage;
	}
	/** 电流 phase c */
	public BigDecimal getPhaseCCurrent(){
		return this.phaseCCurrent;
	}
	/** 电流 phase c */
	public void setPhaseCCurrent(BigDecimal phaseCCurrent){
		this.phaseCCurrent=phaseCCurrent;
	}
	/** 功率 phase  c */
	public BigDecimal getPhaseCPower(){
		return this.phaseCPower;
	}
	/** 功率 phase  c */
	public void setPhaseCPower(BigDecimal phaseCPower){
		this.phaseCPower=phaseCPower;
	}
	/** 频率 phase  c */
	public BigDecimal getPhaseCFrequency(){
		return this.phaseCFrequency;
	}
	/** 频率 phase  c */
	public void setPhaseCFrequency(BigDecimal phaseCFrequency){
		this.phaseCFrequency=phaseCFrequency;
	}
	/**  */
	public BigDecimal getPv1Voltage(){
		return this.pv1Voltage;
	}
	/**  */
	public void setPv1Voltage(BigDecimal pv1Voltage){
		this.pv1Voltage=pv1Voltage;
	}
	/**  */
	public BigDecimal getPv1Current(){
		return this.pv1Current;
	}
	/**  */
	public void setPv1Current(BigDecimal pv1Current){
		this.pv1Current=pv1Current;
	}
	/**  */
	public BigDecimal getMppt1Power(){
		return this.mppt1Power;
	}
	/**  */
	public void setMppt1Power(BigDecimal mppt1Power){
		this.mppt1Power=mppt1Power;
	}
	/**  */
	public BigDecimal getPv2Voltage(){
		return this.pv2Voltage;
	}
	/**  */
	public void setPv2Voltage(BigDecimal pv2Voltage){
		this.pv2Voltage=pv2Voltage;
	}
	/**  */
	public BigDecimal getPv2Current(){
		return this.pv2Current;
	}
	/**  */
	public void setPv2Current(BigDecimal pv2Current){
		this.pv2Current=pv2Current;
	}
	/**  */
	public BigDecimal getMppt2Power(){
		return this.mppt2Power;
	}
	/**  */
	public void setMppt2Power(BigDecimal mppt2Power){
		this.mppt2Power=mppt2Power;
	}
	/**  */
	public BigDecimal getPv3Voltage(){
		return this.pv3Voltage;
	}
	/**  */
	public void setPv3Voltage(BigDecimal pv3Voltage){
		this.pv3Voltage=pv3Voltage;
	}
	/**  */
	public BigDecimal getPv3Current(){
		return this.pv3Current;
	}
	/**  */
	public void setPv3Current(BigDecimal pv3Current){
		this.pv3Current=pv3Current;
	}
	/**  */
	public BigDecimal getMppt3Power(){
		return this.mppt3Power;
	}
	/**  */
	public void setMppt3Power(BigDecimal mppt3Power){
		this.mppt3Power=mppt3Power;
	}
	/**  */
	public BigDecimal getInnerTemperature(){
		return this.innerTemperature;
	}
	/**  */
	public void setInnerTemperature(BigDecimal innerTemperature){
		this.innerTemperature=innerTemperature;
	}
	/**  */
	public String getInverterMode(){
		return this.inverterMode;
	}
	/**  */
	public void setInverterMode(String inverterMode){
		this.inverterMode=inverterMode;
	}
	/**  */
	public String getErrorCode(){
		return this.errorCode;
	}
	/**  */
	public void setErrorCode(String errorCode){
		this.errorCode=errorCode;
	}
	/**  */
	public BigDecimal getTotalEnergy(){
		return this.totalEnergy;
	}
	/**  */
	public void setTotalEnergy(BigDecimal totalEnergy){
		this.totalEnergy=totalEnergy;
	}
	/**  */
	public BigDecimal getTotalGenerationTime(){
		return this.totalGenerationTime;
	}
	/**  */
	public void setTotalGenerationTime(BigDecimal totalGenerationTime){
		this.totalGenerationTime=totalGenerationTime;
	}
	/**  */
	public BigDecimal getTodayEnergy(){
		return this.todayEnergy;
	}
	/**  */
	public void setTodayEnergy(BigDecimal todayEnergy){
		this.todayEnergy=todayEnergy;
	}
	/**  */
	public BigDecimal getActivePower(){
		return this.activePower;
	}
	/**  */
	public void setActivePower(BigDecimal activePower){
		this.activePower=activePower;
	}
	/**  */
	public BigDecimal getReactivePower(){
		return this.reactivePower;
	}
	/**  */
	public void setReactivePower(BigDecimal reactivePower){
		this.reactivePower=reactivePower;
	}
	/**  */
	public BigDecimal getTodayPeakPower(){
		return this.todayPeakPower;
	}
	/**  */
	public void setTodayPeakPower(BigDecimal todayPeakPower){
		this.todayPeakPower=todayPeakPower;
	}
	/**  */
	public BigDecimal getPowerFactor(){
		return this.powerFactor;
	}
	/**  */
	public void setPowerFactor(BigDecimal powerFactor){
		this.powerFactor=powerFactor;
	}
	/**  */
	public BigDecimal getPv4Voltage(){
		return this.pv4Voltage;
	}
	/**  */
	public void setPv4Voltage(BigDecimal pv4Voltage){
		this.pv4Voltage=pv4Voltage;
	}
	/**  */
	public BigDecimal getPv4Current(){
		return this.pv4Current;
	}
	/**  */
	public void setPv4Current(BigDecimal pv4Current){
		this.pv4Current=pv4Current;
	}
	/**  */
	public BigDecimal getMppt4Power(){
		return this.mppt4Power;
	}
	/**  */
	public void setMppt4Power(BigDecimal mppt4Power){
		this.mppt4Power=mppt4Power;
	}
	/**  */
	public BigDecimal getPhaseRWattOfGrid(){
		return this.phaseRWattOfGrid;
	}
	/**  */
	public void setPhaseRWattOfGrid(BigDecimal phaseRWattOfGrid){
		this.phaseRWattOfGrid=phaseRWattOfGrid;
	}
	/**  */
	public BigDecimal getPhaseSWattOfGrid(){
		return this.phaseSWattOfGrid;
	}
	/**  */
	public void setPhaseSWattOfGrid(BigDecimal phaseSWattOfGrid){
		this.phaseSWattOfGrid=phaseSWattOfGrid;
	}
	/**  */
	public BigDecimal getPhaseTWattOfGrid(){
		return this.phaseTWattOfGrid;
	}
	/**  */
	public void setPhaseTWattOfGrid(BigDecimal phaseTWattOfGrid){
		this.phaseTWattOfGrid=phaseTWattOfGrid;
	}
	/**  */
	public BigDecimal getAccumulatedEnergyOfPositive(){
		return this.accumulatedEnergyOfPositive;
	}
	/**  */
	public void setAccumulatedEnergyOfPositive(BigDecimal accumulatedEnergyOfPositive){
		this.accumulatedEnergyOfPositive=accumulatedEnergyOfPositive;
	}
	/**  */
	public BigDecimal getAccumulatedEnergyOfNegative(){
		return this.accumulatedEnergyOfNegative;
	}
	/**  */
	public void setAccumulatedEnergyOfNegative(BigDecimal accumulatedEnergyOfNegative){
		this.accumulatedEnergyOfNegative=accumulatedEnergyOfNegative;
	}
	/**  */
	public BigDecimal getPhaseRWattOfLoad(){
		return this.phaseRWattOfLoad;
	}
	/**  */
	public void setPhaseRWattOfLoad(BigDecimal phaseRWattOfLoad){
		this.phaseRWattOfLoad=phaseRWattOfLoad;
	}
	/**  */
	public BigDecimal getPhaseSWattOfLoad(){
		return this.phaseSWattOfLoad;
	}
	/**  */
	public void setPhaseSWattOfLoad(BigDecimal phaseSWattOfLoad){
		this.phaseSWattOfLoad=phaseSWattOfLoad;
	}
	/**  */
	public BigDecimal getPhaseTWattOfLoad(){
		return this.phaseTWattOfLoad;
	}
	/**  */
	public void setPhaseTWattOfLoad(BigDecimal phaseTWattOfLoad){
		this.phaseTWattOfLoad=phaseTWattOfLoad;
	}
	/**  */
	public BigDecimal getAccumulatedEnergyOfLoad(){
		return this.accumulatedEnergyOfLoad;
	}
	/**  */
	public void setAccumulatedEnergyOfLoad(BigDecimal accumulatedEnergyOfLoad){
		this.accumulatedEnergyOfLoad=accumulatedEnergyOfLoad;
	}
	/**  */
	public BigDecimal getL1NPhaseVoltageOfGrid(){
		return this.l1NPhaseVoltageOfGrid;
	}
	/**  */
	public void setL1NPhaseVoltageOfGrid(BigDecimal l1NPhaseVoltageOfGrid){
		this.l1NPhaseVoltageOfGrid=l1NPhaseVoltageOfGrid;
	}
	/**  */
	public BigDecimal getL2NPhaseVoltageOfGrid(){
		return this.l2NPhaseVoltageOfGrid;
	}
	/**  */
	public void setL2NPhaseVoltageOfGrid(BigDecimal l2NPhaseVoltageOfGrid){
		this.l2NPhaseVoltageOfGrid=l2NPhaseVoltageOfGrid;
	}
	/**  */
	public BigDecimal getL3NPhaseVoltageOfGrid(){
		return this.l3NPhaseVoltageOfGrid;
	}
	/**  */
	public void setL3NPhaseVoltageOfGrid(BigDecimal l3NPhaseVoltageOfGrid){
		this.l3NPhaseVoltageOfGrid=l3NPhaseVoltageOfGrid;
	}
	/**  */
	public BigDecimal getL1CurrentOfGrid(){
		return this.l1CurrentOfGrid;
	}
	/**  */
	public void setL1CurrentOfGrid(BigDecimal l1CurrentOfGrid){
		this.l1CurrentOfGrid=l1CurrentOfGrid;
	}
	/**  */
	public BigDecimal getL2CurrentOfGrid(){
		return this.l2CurrentOfGrid;
	}
	/**  */
	public void setL2CurrentOfGrid(BigDecimal l2CurrentOfGrid){
		this.l2CurrentOfGrid=l2CurrentOfGrid;
	}
	/**  */
	public BigDecimal getL3CurrentOfGrid(){
		return this.l3CurrentOfGrid;
	}
	/**  */
	public void setL3CurrentOfGrid(BigDecimal l3CurrentOfGrid){
		this.l3CurrentOfGrid=l3CurrentOfGrid;
	}
	/**  */
	public BigDecimal getL1NPhaseVoltageOfLoad(){
		return this.l1NPhaseVoltageOfLoad;
	}
	/**  */
	public void setL1NPhaseVoltageOfLoad(BigDecimal l1NPhaseVoltageOfLoad){
		this.l1NPhaseVoltageOfLoad=l1NPhaseVoltageOfLoad;
	}
	/**  */
	public BigDecimal getL2NPhaseVoltageOfLoad(){
		return this.l2NPhaseVoltageOfLoad;
	}
	/**  */
	public void setL2NPhaseVoltageOfLoad(BigDecimal l2NPhaseVoltageOfLoad){
		this.l2NPhaseVoltageOfLoad=l2NPhaseVoltageOfLoad;
	}
	/**  */
	public BigDecimal getL3NPhaseVoltageOfLoad(){
		return this.l3NPhaseVoltageOfLoad;
	}
	/**  */
	public void setL3NPhaseVoltageOfLoad(BigDecimal l3NPhaseVoltageOfLoad){
		this.l3NPhaseVoltageOfLoad=l3NPhaseVoltageOfLoad;
	}
	/**  */
	public BigDecimal getL1CurrentOfLoad(){
		return this.l1CurrentOfLoad;
	}
	/**  */
	public void setL1CurrentOfLoad(BigDecimal l1CurrentOfLoad){
		this.l1CurrentOfLoad=l1CurrentOfLoad;
	}
	/**  */
	public BigDecimal getL2CurrentOfLoad(){
		return this.l2CurrentOfLoad;
	}
	/**  */
	public void setL2CurrentOfLoad(BigDecimal l2CurrentOfLoad){
		this.l2CurrentOfLoad=l2CurrentOfLoad;
	}
	/**  */
	public BigDecimal getL3CurrentOfLoad(){
		return this.l3CurrentOfLoad;
	}
	/**  */
	public void setL3CurrentOfLoad(BigDecimal l3CurrentOfLoad){
		this.l3CurrentOfLoad=l3CurrentOfLoad;
	}
	/**  */
	public BigDecimal getTodayImportEnergy(){
		return this.todayImportEnergy;
	}
	/**  */
	public void setTodayImportEnergy(BigDecimal todayImportEnergy){
		this.todayImportEnergy=todayImportEnergy;
	}
	/**  */
	public BigDecimal getTodayExportEnergy(){
		return this.todayExportEnergy;
	}
	/**  */
	public void setTodayExportEnergy(BigDecimal todayExportEnergy){
		this.todayExportEnergy=todayExportEnergy;
	}
	/**  */
	public BigDecimal getTodayLoadEnergy(){
		return this.todayLoadEnergy;
	}
	/**  */
	public void setTodayLoadEnergy(BigDecimal todayLoadEnergy){
		this.todayLoadEnergy=todayLoadEnergy;
	}
	/**  */
	public BigDecimal getFrequencyOfGrid(){
		return this.frequencyOfGrid;
	}
	/**  */
	public void setFrequencyOfGrid(BigDecimal frequencyOfGrid){
		this.frequencyOfGrid=frequencyOfGrid;
	}
	/**  */
	public BigDecimal getPhaseRVoltageOfEps(){
		return this.phaseRVoltageOfEps;
	}
	/**  */
	public void setPhaseRVoltageOfEps(BigDecimal phaseRVoltageOfEps){
		this.phaseRVoltageOfEps=phaseRVoltageOfEps;
	}
	/**  */
	public BigDecimal getPhaseRCurrentOfEps(){
		return this.phaseRCurrentOfEps;
	}
	/**  */
	public void setPhaseRCurrentOfEps(BigDecimal phaseRCurrentOfEps){
		this.phaseRCurrentOfEps=phaseRCurrentOfEps;
	}
	/**  */
	public BigDecimal getPhaseRWattOfEps(){
		return this.phaseRWattOfEps;
	}
	/**  */
	public void setPhaseRWattOfEps(BigDecimal phaseRWattOfEps){
		this.phaseRWattOfEps=phaseRWattOfEps;
	}
	/**  */
	public BigDecimal getFrequencyOfEps(){
		return this.frequencyOfEps;
	}
	/**  */
	public void setFrequencyOfEps(BigDecimal frequencyOfEps){
		this.frequencyOfEps=frequencyOfEps;
	}
	/**  */
	public BigDecimal getPhaseSVoltageOfEps(){
		return this.phaseSVoltageOfEps;
	}
	/**  */
	public void setPhaseSVoltageOfEps(BigDecimal phaseSVoltageOfEps){
		this.phaseSVoltageOfEps=phaseSVoltageOfEps;
	}
	/**  */
	public BigDecimal getPhaseSCurrentOfEps(){
		return this.phaseSCurrentOfEps;
	}
	/**  */
	public void setPhaseSCurrentOfEps(BigDecimal phaseSCurrentOfEps){
		this.phaseSCurrentOfEps=phaseSCurrentOfEps;
	}
	/**  */
	public BigDecimal getPhaseSWattOfEps(){
		return this.phaseSWattOfEps;
	}
	/**  */
	public void setPhaseSWattOfEps(BigDecimal phaseSWattOfEps){
		this.phaseSWattOfEps=phaseSWattOfEps;
	}
	/**  */
	public BigDecimal getPhaseTVoltageOfEps(){
		return this.phaseTVoltageOfEps;
	}
	/**  */
	public void setPhaseTVoltageOfEps(BigDecimal phaseTVoltageOfEps){
		this.phaseTVoltageOfEps=phaseTVoltageOfEps;
	}
	/**  */
	public BigDecimal getPhaseTCurrentOfEps(){
		return this.phaseTCurrentOfEps;
	}
	/**  */
	public void setPhaseTCurrentOfEps(BigDecimal phaseTCurrentOfEps){
		this.phaseTCurrentOfEps=phaseTCurrentOfEps;
	}
	/**  */
	public BigDecimal getPhaseTWattOfEps(){
		return this.phaseTWattOfEps;
	}
	/**  */
	public void setPhaseTWattOfEps(BigDecimal phaseTWattOfEps){
		this.phaseTWattOfEps=phaseTWattOfEps;
	}
	/**  */
	public BigDecimal getDailyEnergyToEps(){
		return this.dailyEnergyToEps;
	}
	/**  */
	public void setDailyEnergyToEps(BigDecimal dailyEnergyToEps){
		this.dailyEnergyToEps=dailyEnergyToEps;
	}
	/**  */
	public BigDecimal getAccumulatedEnergyToEps(){
		return this.accumulatedEnergyToEps;
	}
	/**  */
	public void setAccumulatedEnergyToEps(BigDecimal accumulatedEnergyToEps){
		this.accumulatedEnergyToEps=accumulatedEnergyToEps;
	}
	/**  */
	public BigDecimal getBatterySoc(){
		return this.batterySoc;
	}
	/**  */
	public void setBatterySoc(BigDecimal batterySoc){
		this.batterySoc=batterySoc;
	}
	/**  */
	public BigDecimal getBatteryTemperature(){
		return this.batteryTemperature;
	}
	/**  */
	public void setBatteryTemperature(BigDecimal batteryTemperature){
		this.batteryTemperature=batteryTemperature;
	}
	/**  */
	public BigDecimal getBatteryVoltage(){
		return this.batteryVoltage;
	}
	/**  */
	public void setBatteryVoltage(BigDecimal batteryVoltage){
		this.batteryVoltage=batteryVoltage;
	}
	/**  */
	public BigDecimal getBatteryCurrent(){
		return this.batteryCurrent;
	}
	/**  */
	public void setBatteryCurrent(BigDecimal batteryCurrent){
		this.batteryCurrent=batteryCurrent;
	}
	/**  */
	public BigDecimal getBatteryPower(){
		return this.batteryPower;
	}
	/**  */
	public void setBatteryPower(BigDecimal batteryPower){
		this.batteryPower=batteryPower;
	}
	/**  */
	public BigDecimal getBatteryDailyChargeEnergy(){
		return this.batteryDailyChargeEnergy;
	}
	/**  */
	public void setBatteryDailyChargeEnergy(BigDecimal batteryDailyChargeEnergy){
		this.batteryDailyChargeEnergy=batteryDailyChargeEnergy;
	}
	/**  */
	public BigDecimal getBatteryAccumulatedChargeEnergy(){
		return this.batteryAccumulatedChargeEnergy;
	}
	/**  */
	public void setBatteryAccumulatedChargeEnergy(BigDecimal batteryAccumulatedChargeEnergy){
		this.batteryAccumulatedChargeEnergy=batteryAccumulatedChargeEnergy;
	}
	/**  */
	public BigDecimal getBatteryDailyDischargeEnergy(){
		return this.batteryDailyDischargeEnergy;
	}
	/**  */
	public void setBatteryDailyDischargeEnergy(BigDecimal batteryDailyDischargeEnergy){
		this.batteryDailyDischargeEnergy=batteryDailyDischargeEnergy;
	}
	/**  */
	public BigDecimal getBatteryAccumulatedDischargeEnergy(){
		return this.batteryAccumulatedDischargeEnergy;
	}
	/**  */
	public void setBatteryAccumulatedDischargeEnergy(BigDecimal batteryAccumulatedDischargeEnergy){
		this.batteryAccumulatedDischargeEnergy=batteryAccumulatedDischargeEnergy;
	}
	/** 错误信息 */
	public String getErrorMessage4(){
		return this.errorMessage4;
	}
	/** 错误信息 */
	public void setErrorMessage4(String errorMessage4){
		this.errorMessage4=errorMessage4;
	}

	public BigDecimal getNumberOfBattery() {
		return numberOfBattery;
	}

	public void setNumberOfBattery(BigDecimal numberOfBattery) {
		this.numberOfBattery = numberOfBattery;
	}

	public BigDecimal getSoh() {
		return soh;
	}

	public void setSoh(BigDecimal soh) {
		this.soh = soh;
	}
}
