/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.core.tenant.mp.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Device24 实体类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@TableName("device_24")
@ApiModel(value = "Device24对象", description = "Device24")
@EqualsAndHashCode(callSuper = true)
public class Device24Entity extends TenantEntity {

	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String year;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String monthDay;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String hoursMin;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String seconds;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String powerDeratingPercentByModbus;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String modbusAddress;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String rs485BaudRate;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String wifiStaSsid;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String wifiStaPassword;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal digitalMeterModbusAddress;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String digitalMeterType;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String powerFlowDirection;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String powerLimitFunction;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String powerLimitCtRatio;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String meterLocation;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal maximumFeedInGridPower;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal firstConnectStartTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal reconnectTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLossLevel1Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLossLevel1Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLossLevel1Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLossLevel1Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLossLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLossLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLossLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLossLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLossLevel2Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLossLevel2Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLossLevel2Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLossLevel2Limit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLossLevel2TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLossLevel2TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLossLevel2TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLossLevel2TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLevel1Back;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLevel1Back;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal tenMinAverageSustainedVoltage;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal reconnectSoftOutputPowerPercent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal overFrequencyPowerReductionDroop;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal insulationResistanceActiveLimit;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridOverVoltageDeratingPoint;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyHighLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridFrequencyLowLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridOverFrequencyDeratingStartPoint;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridOverFrequencyDeratingEndPoint;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	@TableField(value = "grid_voltage_low_level1_trip_time")
	private BigDecimal gridVoltageLowLevel1TripTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageHighLevel1Back;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal gridVoltageLowLevel1Back;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal firstConnectSoftStartOutputPowerPercent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal overVoltageDeratingSettlingTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal connectionAndReconnectionPowerRampRate;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal outputReactivePowerMode;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal powerFactorSetting;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal reactiveControlResponseTime;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode1Percent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode2Percent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode3Percent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode4Percent;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode1ValueSetting;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode2ValueSetting;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private BigDecimal curveNode3ValueSetting;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String regulationCode;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String inverterControl;
	/**
	 * 创建人账号
	 */
	@ApiModelProperty(value = "创建人账号")
	private String createUserAccount;
	/**
	 * 更新人账号
	 */
	@ApiModelProperty(value = "更新人账号")
	private String updateUserAccount;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String factoryReset;
	/**
	 *
	 */
	@ApiModelProperty(value = "")
	private String clearHistoricalInfo;
	/**
	 * 站点id
	 */
	@ApiModelProperty(value = "站点id")
	private Long plantId;
	/**
	 * 设备/逆变器SN
	 */
	@ApiModelProperty(value = "设备/逆变器SN")
	private String deviceSerialNumber;
	/**
	 * modbus协议版本
	 */
	@ApiModelProperty(value = "modbus协议版本")
	private String modbusProtocolVersion;
	/**
	 * 设备时间，设备上报时时间
	 */
	@ApiModelProperty(value = "设备时间，设备上报时时间")
	private Date deviceDateTime;

	private String powerSwitching;
}
