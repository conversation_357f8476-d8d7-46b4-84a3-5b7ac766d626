package org.skyworth.ess.device.client;

import org.skyworth.ess.device.entity.DeviceLog24Entity;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> - xuehongjiang
 * @version 1.0
 * @project
 * @description
 * @create-time 2023/11/6 10:23:09
 */
@Component
public class IDeviceLog24Fallback implements IDeviceLog24Client{

	@Override
	public void insertBatchDeviceLog24Column(List<DeviceLog24Entity> deviceLog24Entities) {

	}

	@Override
	public void insertBatchDeviceLog24ForNetWork(List<DeviceLog24Entity> deviceLog24Entities) {

	}

}
