package org.skyworth.ess.device.entity;

public enum DictErrorCode3Enum {

	Reserved(0,"CY - RSD(Rapid Shutdown) abnormal","CY - RSD (Schnellabschaltung) abnormal"),
	EMSEEPROM(1,"Cv - EMS/CSB EEPROM fail", "CV - EMS/CSB EEPROM fehlgeschlagen"),
	Reserved2(2,"B9 - PV open warning","B9 - PV offen Warnung"),
	deviceAbnormal(3,"BA - PID device abnormal","BA - PID-Gerät abnormal"),
	AFCILost(4,"Bb - AFCI lost","Bb - AFCI verloren"),
	DataLogger(5,"CH - Data logger lost ","CH - Datenlogger verloren"),
	MeterLost(6,"CJ - Meter lost", "CJ - Meter verloren"),
	Reserved3(7,"Co - Inverter lost","Co - Wechselrichter verloren"),
	abnormal(8,"A8 - Grid N abnormal","A8 - Netz N abnormal"),
	defective(9,"BC - Surge Protection Devices (SPD) defective","BC - Überspannungsschutzgeräte (SPD) defekt"),
	warning(10,"P1 - Parallel ID warning","P1 - Parallel-ID-Warnung"),
	signalWarning(11,"P2 - Parallel SYN signal warning","P2 - Paralleles SYN-Signal-Warnung"),
	BATAbnormal(12,"P3 - Parallel BAT abnormal","P3 - Paralleler BAT abnormal"),
	GRIDAbnormal(13,"P4 - Parallel GRID abnormal","P4 - Paralleles GRID abnormal"),
	voltageAbnormal (14,"Dd - Generator voltage abnormal","Dd - Generatorspannung abnormal"),
	Reserved4(15,"P5 - Phase sequence abnormal","P5 - Phasensequenz abnormal");

	private int code;
	private String message;

	private String deMessage;

	private DictErrorCode3Enum(int code, String message,String deMessage) {
		this.code = code;
		this.message = message;
		this.deMessage = deMessage;
	}

	public static String switchLanguage(String language,int key) {
		String result = "";

		for (DictErrorCode3Enum s : values()) {
			if (s.getCode()==key) {
				if("de".equals(language)){
					result = s.getDeMessage();
				}else {
					result = s.getMessage();
				}
				break;
			}
		}

		return result;
	}

	public String getDeMessage() {
		return deMessage;
	}

	public void setDeMessage(String deMessage) {
		this.deMessage = deMessage;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public static DictErrorCode3Enum match(int key) {

		DictErrorCode3Enum result = null;

		for (DictErrorCode3Enum s : values()) {
			if (s.getCode()==key) {
				result = s;
				break;
			}
		}

		return result;
	}

	public static DictErrorCode3Enum catchMessage(String msg) {

		DictErrorCode3Enum result = null;

		for (DictErrorCode3Enum s : values()) {
			if (s.getMessage().equals(msg)) {
				result = s;
				break;
			}
		}

		return result;
	}
}
