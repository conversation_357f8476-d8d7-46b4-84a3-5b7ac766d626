package org.skyworth.ess.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.tenant.mp.TenantEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备/逆变器日志表，记录23数据;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-9-14
 */
@ApiModel(value = "设备/逆变器日志表，记录23数据",description = "")
@TableName("device_log23")
@Data
public class DeviceLog23 extends TenantEntity implements Serializable,Cloneable{

	/** 工作模式 */
	@ApiModelProperty(name = "工作模式",notes = "")
	private String hybridWorkMode ;
	/** 一次/每天 */
	@ApiModelProperty(name = "一次/每天",notes = "")
	private String onceEveryday ;
	/** 充电开始时间 */
	@ApiModelProperty(name = "充电开始时间",notes = "")
	private String chargeStartTime1 ;
	/** 充电结束时间 */
	@ApiModelProperty(name = "充电结束时间",notes = "")
	private String chargeEndTime1 ;
	/** 放电开始时间 */
	@ApiModelProperty(name = "放电开始时间",notes = "")
	private String dischargeStartTime1 ;
	/** 放电结束时间 */
	@ApiModelProperty(name = "放电结束时间",notes = "")
	private String dischargeEndTime1 ;
	/** 电池类型选择 */
	@ApiModelProperty(name = "电池类型选择",notes = "")
	private String batteryTypeSelection ;
	/** 通讯地址 */
	@ApiModelProperty(name = "通讯地址",notes = "")
	private String commAddress ;
	/** 电池容量 */
	@ApiModelProperty(name = "电池容量",notes = "")
	private Double batteryAh ;
	/** 铅酸电池放电截止电压 */
	@ApiModelProperty(name = "铅酸电池放电截止电压",notes = "")
	private Double stopDischargeVoltage ;
	/** 铅酸电池充电截止电压 */
	@ApiModelProperty(name = "铅酸电池充电截止电压",notes = "")
	private Double stopChargeVoltage ;
	/** 电网充电使能 */
	@ApiModelProperty(name = "电网充电使能",notes = "")
	private String gridCharge ;
	/** 最大电网充电功率 */
	@ApiModelProperty(name = "最大电网充电功率",notes = "")
	private Double maximumGridChargerPower ;
	/** 电网充电截止电量% */
	@ApiModelProperty(name = "电网充电截止电量%",notes = "")
	private Double capacityOfGridChargerEnd ;
	/** 最大充电功率 */
	@ApiModelProperty(name = "最大充电功率",notes = "")
	private Double maximumChargerPower ;
	/** 充电截止SOC */
	@ApiModelProperty(name = "充电截止SOC",notes = "")
	private Double capacityOfChargerEnd ;
	/** 最大放电功率 */
	@ApiModelProperty(name = "最大放电功率",notes = "")
	private Double maximumDischargerPower ;
	/** 放电截止EOD */
	@ApiModelProperty(name = "放电截止EOD",notes = "")
	private Double capacityOfDischargerEnd ;
	/** 离网模式使能 */
	@ApiModelProperty(name = "离网模式使能",notes = "")
	private String offGridMode ;
	/** 额定输出电压 */
	@ApiModelProperty(name = "额定输出电压",notes = "")
	private Double ratedOutputVoltage ;
	/** 额定输出频率 */
	@ApiModelProperty(name = "额定输出频率",notes = "")
	private Double ratedOutputFrequency ;
	/** 切换离网模式的电池最低SOC */
	@ApiModelProperty(name = "切换离网模式的电池最低SOC",notes = "")
	private Double offGridStartUpBatteryCapacity ;
	/** 最大放电电流 */
	@ApiModelProperty(name = "最大放电电流",notes = "")
	private Double maximumDischargeCurrent ;
	/** 最大充电电流 */
	@ApiModelProperty(name = "最大充电电流",notes = "")
	private Double maximumChargerCurrent ;


	@ApiModelProperty(name = "GEN端口",notes = "")
	private String genPort;


	@ApiModelProperty(name = "锂电池激活功能",notes = "")
	private String lithiumBatteryActivationFunction;

	@ApiModelProperty(name = "最大发电机充电功率",notes = "")
	private double maximumGenChargePower;

	@ApiModelProperty(name = "发电机的最大输入功率",notes = "")
	private double maximumInputPowerFromGenerator;


	@ApiModelProperty(name = "支持正常负载功能",notes = "")
	private String supportNormalLoadFunction;

	@ApiModelProperty(name = "并行模式功能",notes = "")
	private String parallelModeFunction;

	@ApiModelProperty(name = "馈入网格功能",notes = "")
	private String feedInGridFunction;

	@ApiModelProperty(name = "最大电网强制充电功率",notes = "")
	private double maximumGridForcedChargePower;
	/** 分时控制开关 */
	@ApiModelProperty(name = "分时控制开关",notes = "分时控制开关")
	private String timeBasedControlEnable ;

	@ApiModelProperty(value = "最大充电功率(新)")
	private String chargePowerInTime1HighWord;

	@ApiModelProperty(value = "结束充电容量比例(新)")
	private String chargeEndSocInTime1;

	@ApiModelProperty(value = "最大放电功率(新)")
	private String dischargePowerInTime1HighWord;

	@ApiModelProperty(value = "结束放电容量比例(新)")
	private String dischargeEndSocInTime1;

	private BigDecimal generatorStartSoc;

	private BigDecimal generatorEndSoc;

	private BigDecimal maximumInputPowerFromGrid;

	private BigDecimal capacityOfDischargeEndOnGrid;

	private BigDecimal forceChargeStartSoc;

	private BigDecimal forceChargeEndSoc;

	private BigDecimal backupMinimumOutputVoltage;

	private BigDecimal backupMaximumOutputVoltage;

	private String generatorDryForceOnOrOff;

	private String onceEveryday2;
	private String chargeStartTime2;
	private String chargeEndTime2;
	private String chargePowerInTime2HighWord;
	private String chargeEndSocInTime2;
	private String dischargeStartTime2;
	private String dischargeEndTime2;
	private String dischargePowerInTime2HighWord;
	private String dischargeEndSocInTime2;

	private String onceEveryday3;
	private String chargeStartTime3;
	private String chargeEndTime3;
	private String chargePowerInTime3HighWord;
	private String chargeEndSocInTime3;
	private String dischargeStartTime3;
	private String dischargeEndTime3;
	private String dischargePowerInTime3HighWord;
	private String dischargeEndSocInTime3;
	// ac_coupling开关;(0/1:关/开)
	private BigDecimal acCouplingFunction;
	// 通讯电表,业务字典 device_re_485_hybrid
	private String rs485Device;
	private String parallelSystemBatteryConnectType;

	public BigDecimal getGeneratorStartSoc() {
		return generatorStartSoc;
	}

	public void setGeneratorStartSoc(BigDecimal generatorStartSoc) {
		this.generatorStartSoc = generatorStartSoc;
	}

	public BigDecimal getGeneratorEndSoc() {
		return generatorEndSoc;
	}

	public void setGeneratorEndSoc(BigDecimal generatorEndSoc) {
		this.generatorEndSoc = generatorEndSoc;
	}

	public BigDecimal getMaximumInputPowerFromGrid() {
		return maximumInputPowerFromGrid;
	}

	public void setMaximumInputPowerFromGrid(BigDecimal maximumInputPowerFromGrid) {
		this.maximumInputPowerFromGrid = maximumInputPowerFromGrid;
	}

	public BigDecimal getCapacityOfDischargeEndOnGrid() {
		return capacityOfDischargeEndOnGrid;
	}

	public void setCapacityOfDischargeEndOnGrid(BigDecimal capacityOfDischargeEndOnGrid) {
		this.capacityOfDischargeEndOnGrid = capacityOfDischargeEndOnGrid;
	}

	public BigDecimal getForceChargeStartSoc() {
		return forceChargeStartSoc;
	}

	public void setForceChargeStartSoc(BigDecimal forceChargeStartSoc) {
		this.forceChargeStartSoc = forceChargeStartSoc;
	}

	public BigDecimal getForceChargeEndSoc() {
		return forceChargeEndSoc;
	}

	public void setForceChargeEndSoc(BigDecimal forceChargeEndSoc) {
		this.forceChargeEndSoc = forceChargeEndSoc;
	}

	public BigDecimal getBackupMinimumOutputVoltage() {
		return backupMinimumOutputVoltage;
	}

	public void setBackupMinimumOutputVoltage(BigDecimal backupMinimumOutputVoltage) {
		this.backupMinimumOutputVoltage = backupMinimumOutputVoltage;
	}

	public BigDecimal getBackupMaximumOutputVoltage() {
		return backupMaximumOutputVoltage;
	}

	public void setBackupMaximumOutputVoltage(BigDecimal backupMaximumOutputVoltage) {
		this.backupMaximumOutputVoltage = backupMaximumOutputVoltage;
	}

	public String getGeneratorDryForceOnOrOff() {
		return generatorDryForceOnOrOff;
	}

	public void setGeneratorDryForceOnOrOff(String generatorDryForceOnOrOff) {
		this.generatorDryForceOnOrOff = generatorDryForceOnOrOff;
	}

	public String getChargePowerInTime1HighWord() {
		return chargePowerInTime1HighWord;
	}

	public void setChargePowerInTime1HighWord(String chargePowerInTime1HighWord) {
		this.chargePowerInTime1HighWord = chargePowerInTime1HighWord;
	}

	public String getChargeEndSocInTime1() {
		return chargeEndSocInTime1;
	}

	public void setChargeEndSocInTime1(String chargeEndSocInTime1) {
		this.chargeEndSocInTime1 = chargeEndSocInTime1;
	}

	public String getDischargePowerInTime1HighWord() {
		return dischargePowerInTime1HighWord;
	}

	public void setDischargePowerInTime1HighWord(String dischargePowerInTime1HighWord) {
		this.dischargePowerInTime1HighWord = dischargePowerInTime1HighWord;
	}

	public String getDischargeEndSocInTime1() {
		return dischargeEndSocInTime1;
	}

	public void setDischargeEndSocInTime1(String dischargeEndSocInTime1) {
		this.dischargeEndSocInTime1 = dischargeEndSocInTime1;
	}

	public String getTimeBasedControlEnable() {
		return timeBasedControlEnable;
	}

	public void setTimeBasedControlEnable(String timeBasedControlEnable) {
		this.timeBasedControlEnable = timeBasedControlEnable;
	}

	public String getGenPort() {
		return genPort;
	}

	public void setGenPort(String genPort) {
		this.genPort = genPort;
	}

	public String getLithiumBatteryActivationFunction() {
		return lithiumBatteryActivationFunction;
	}

	public void setLithiumBatteryActivationFunction(String lithiumBatteryActivationFunction) {
		this.lithiumBatteryActivationFunction = lithiumBatteryActivationFunction;
	}

	public double getMaximumGenChargePower() {
		return maximumGenChargePower;
	}

	public void setMaximumGenChargePower(double maximumGenChargePower) {
		this.maximumGenChargePower = maximumGenChargePower;
	}

	public double getMaximumInputPowerFromGenerator() {
		return maximumInputPowerFromGenerator;
	}

	public void setMaximumInputPowerFromGenerator(double maximumInputPowerFromGenerator) {
		this.maximumInputPowerFromGenerator = maximumInputPowerFromGenerator;
	}

	public String getSupportNormalLoadFunction() {
		return supportNormalLoadFunction;
	}

	public void setSupportNormalLoadFunction(String supportNormalLoadFunction) {
		this.supportNormalLoadFunction = supportNormalLoadFunction;
	}

	public String getParallelModeFunction() {
		return parallelModeFunction;
	}

	public void setParallelModeFunction(String parallelModeFunction) {
		this.parallelModeFunction = parallelModeFunction;
	}

	public String getFeedInGridFunction() {
		return feedInGridFunction;
	}

	public void setFeedInGridFunction(String feedInGridFunction) {
		this.feedInGridFunction = feedInGridFunction;
	}

	public double getMaximumGridForcedChargePower() {
		return maximumGridForcedChargePower;
	}

	public void setMaximumGridForcedChargePower(double maximumGridForcedChargePower) {
		this.maximumGridForcedChargePower = maximumGridForcedChargePower;
	}

	public DeviceLog23(){

	}
	/** 站点ID */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(name = "站点ID",notes = "")
	private long plantId ;

	/** 设备时间，设备上报时时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(name = "设备时间，设备上报时时间",notes = "")
	private Date deviceDateTime ;

	/** modbus协议版本 */
	@ApiModelProperty(name = "modbus协议版本",notes = "")
	private String modbusProtocolVersion ;


	@ApiModelProperty(name = "同步状态（N未同步；Y已同步）",notes = "")
	private String synchStatus ;

	/** 逆变器/设备SN */
	@ApiModelProperty(name = "逆变器/设备SN",notes = "")
	private String deviceSerialNumber ;

	/** 创建人账号 */
	@ApiModelProperty(name = "创建人账号",notes = "")
	private String createUserAccount ;
	/** 更新人账号 */
	@ApiModelProperty(name = "更新人账号",notes = "")
	private String updateUserAccount ;

	public String getSynchStatus() {
		return synchStatus;
	}

	public void setSynchStatus(String synchStatus) {
		this.synchStatus = synchStatus;
	}

	public long getPlantId() {
		return plantId;
	}

	public void setPlantId(long plantId) {
		this.plantId = plantId;
	}

	public Date getDeviceDateTime() {
		return deviceDateTime;
	}

	public void setDeviceDateTime(Date deviceDateTime) {
		this.deviceDateTime = deviceDateTime;
	}

	public String getModbusProtocolVersion() {
		return modbusProtocolVersion;
	}

	public void setModbusProtocolVersion(String modbusProtocolVersion) {
		this.modbusProtocolVersion = modbusProtocolVersion;
	}

	public String getDeviceSerialNumber() {
		return deviceSerialNumber;
	}

	public void setDeviceSerialNumber(String deviceSerialNumber) {
		this.deviceSerialNumber = deviceSerialNumber;
	}

	public String getCreateUserAccount() {
		return createUserAccount;
	}

	public void setCreateUserAccount(String createUserAccount) {
		this.createUserAccount = createUserAccount;
	}

	public String getUpdateUserAccount() {
		return updateUserAccount;
	}

	public void setUpdateUserAccount(String updateUserAccount) {
		this.updateUserAccount = updateUserAccount;
	}

	/** 工作模式 */
	public String getHybridWorkMode(){
		return this.hybridWorkMode;
	}
	/** 工作模式 */
	public void setHybridWorkMode(String hybridWorkMode){
		this.hybridWorkMode=hybridWorkMode;
	}
	/** 一次/每天 */
	public String getOnceEveryday(){
		return this.onceEveryday;
	}
	/** 一次/每天 */
	public void setOnceEveryday(String onceEveryday){
		this.onceEveryday=onceEveryday;
	}
	/** 充电开始时间 */
	public String getChargeStartTime1(){
		return this.chargeStartTime1;
	}
	/** 充电开始时间 */
	public void setChargeStartTime1(String chargeStartTime1){
		this.chargeStartTime1=chargeStartTime1;
	}
	/** 充电结束时间 */
	public String getChargeEndTime1(){
		return this.chargeEndTime1;
	}
	/** 充电结束时间 */
	public void setChargeEndTime1(String chargeEndTime1){
		this.chargeEndTime1=chargeEndTime1;
	}
	/** 放电开始时间 */
	public String getDischargeStartTime1(){
		return this.dischargeStartTime1;
	}
	/** 放电开始时间 */
	public void setDischargeStartTime1(String dischargeStartTime1){
		this.dischargeStartTime1=dischargeStartTime1;
	}
	/** 放电结束时间 */
	public String getDischargeEndTime1(){
		return this.dischargeEndTime1;
	}
	/** 放电结束时间 */
	public void setDischargeEndTime1(String dischargeEndTime1){
		this.dischargeEndTime1=dischargeEndTime1;
	}
	/** 电池类型选择 */
	public String getBatteryTypeSelection(){
		return this.batteryTypeSelection;
	}
	/** 电池类型选择 */
	public void setBatteryTypeSelection(String batteryTypeSelection){
		this.batteryTypeSelection=batteryTypeSelection;
	}
	/** 通讯地址 */
	public String getCommAddress(){
		return this.commAddress;
	}
	/** 通讯地址 */
	public void setCommAddress(String commAddress){
		this.commAddress=commAddress;
	}
	/** 电池容量 */
	public Double getBatteryAh(){
		return this.batteryAh;
	}
	/** 电池容量 */
	public void setBatteryAh(Double batteryAh){
		this.batteryAh=batteryAh;
	}
	/** 铅酸电池放电截止电压 */
	public Double getStopDischargeVoltage(){
		return this.stopDischargeVoltage;
	}
	/** 铅酸电池放电截止电压 */
	public void setStopDischargeVoltage(Double stopDischargeVoltage){
		this.stopDischargeVoltage=stopDischargeVoltage;
	}
	/** 铅酸电池充电截止电压 */
	public Double getStopChargeVoltage(){
		return this.stopChargeVoltage;
	}
	/** 铅酸电池充电截止电压 */
	public void setStopChargeVoltage(Double stopChargeVoltage){
		this.stopChargeVoltage=stopChargeVoltage;
	}
	/** 电网充电使能 */
	public String getGridCharge(){
		return this.gridCharge;
	}
	/** 电网充电使能 */
	public void setGridCharge(String gridCharge){
		this.gridCharge=gridCharge;
	}
	/** 最大电网充电功率 */
	public Double getMaximumGridChargerPower(){
		return this.maximumGridChargerPower;
	}
	/** 最大电网充电功率 */
	public void setMaximumGridChargerPower(Double maximumGridChargerPower){
		this.maximumGridChargerPower=maximumGridChargerPower;
	}
	/** 电网充电截止电量% */
	public Double getCapacityOfGridChargerEnd(){
		return this.capacityOfGridChargerEnd;
	}
	/** 电网充电截止电量% */
	public void setCapacityOfGridChargerEnd(Double capacityOfGridChargerEnd){
		this.capacityOfGridChargerEnd=capacityOfGridChargerEnd;
	}
	/** 最大充电功率 */
	public Double getMaximumChargerPower(){
		return this.maximumChargerPower;
	}
	/** 最大充电功率 */
	public void setMaximumChargerPower(Double maximumChargerPower){
		this.maximumChargerPower=maximumChargerPower;
	}
	/** 充电截止SOC */
	public Double getCapacityOfChargerEnd(){
		return this.capacityOfChargerEnd;
	}
	/** 充电截止SOC */
	public void setCapacityOfChargerEnd(Double capacityOfChargerEnd){
		this.capacityOfChargerEnd=capacityOfChargerEnd;
	}
	/** 最大放电功率 */
	public Double getMaximumDischargerPower(){
		return this.maximumDischargerPower;
	}
	/** 最大放电功率 */
	public void setMaximumDischargerPower(Double maximumDischargerPower){
		this.maximumDischargerPower=maximumDischargerPower;
	}
	/** 放电截止EOD */
	public Double getCapacityOfDischargerEnd(){
		return this.capacityOfDischargerEnd;
	}
	/** 放电截止EOD */
	public void setCapacityOfDischargerEnd(Double capacityOfDischargerEnd){
		this.capacityOfDischargerEnd=capacityOfDischargerEnd;
	}
	/** 离网模式使能 */
	public String getOffGridMode(){
		return this.offGridMode;
	}
	/** 离网模式使能 */
	public void setOffGridMode(String offGridMode){
		this.offGridMode=offGridMode;
	}
	/** 额定输出电压 */
	public Double getRatedOutputVoltage(){
		return this.ratedOutputVoltage;
	}
	/** 额定输出电压 */
	public void setRatedOutputVoltage(Double ratedOutputVoltage){
		this.ratedOutputVoltage=ratedOutputVoltage;
	}
	/** 额定输出频率 */
	public Double getRatedOutputFrequency(){
		return this.ratedOutputFrequency;
	}
	/** 额定输出频率 */
	public void setRatedOutputFrequency(Double ratedOutputFrequency){
		this.ratedOutputFrequency=ratedOutputFrequency;
	}
	/** 切换离网模式的电池最低SOC */
	public Double getOffGridStartUpBatteryCapacity(){
		return this.offGridStartUpBatteryCapacity;
	}
	/** 切换离网模式的电池最低SOC */
	public void setOffGridStartUpBatteryCapacity(Double offGridStartUpBatteryCapacity){
		this.offGridStartUpBatteryCapacity=offGridStartUpBatteryCapacity;
	}
	/** 最大放电电流 */
	public Double getMaximumDischargeCurrent(){
		return this.maximumDischargeCurrent;
	}
	/** 最大放电电流 */
	public void setMaximumDischargeCurrent(Double maximumDischargeCurrent){
		this.maximumDischargeCurrent=maximumDischargeCurrent;
	}
	/** 最大充电电流 */
	public Double getMaximumChargerCurrent(){
		return this.maximumChargerCurrent;
	}
	/** 最大充电电流 */
	public void setMaximumChargerCurrent(Double maximumChargerCurrent){
		this.maximumChargerCurrent=maximumChargerCurrent;
	}

}
