package org.skyworth.ess.device.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(
        value ="skyworth-client",
        fallback = WifiStickPlantClientFallBack.class
)
public interface IWifiStickPlantClient {

    String API_PREFIX = "/wifiInfo";
    String address = API_PREFIX + "/heartBeat/updateStatus";

    @PostMapping(address)
    void updateHeartStatus(@RequestBody List<String> wifiSnList);
}
