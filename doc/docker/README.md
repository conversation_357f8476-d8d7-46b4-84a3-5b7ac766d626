#使用docker构建工程步骤
### 1. 使用harbor作为私有库,需要配置maven,找到setting.xml( `linux可以使用find / -name settings.xml`)加入以下配置

```
<servers>
  <server>
    <id>*************</id>
    <username>admin</username>
    <password>admin12345</password>
    <configuration>
      <email><EMAIL></email>
    </configuration>
  </server>
</servers>

<pluginGroups>
  <pluginGroup>io.fabric8</pluginGroup>  
</pluginGroups>
```

### 2. docker开启远程访问

如果没有远程访问,会报 `Connect to *************:2375 [/*************] failed: Connection refused: connect`

在`/usr/lib/systemd/system/docker.service`,配置远程访问。主要是在[Service]这个部分，加上下面两个参数：

```
cd /usr/lib/systemd/system

vi docker.service

ExecStart=
ExecStart=/usr/bin/dockerd -H tcp://0.0.0.0:2375 -H unix://var/run/docker.sock
```

### 3. 配置http访问
因为docker1.3.2版本开始默认docker registry使用的是https，我们设置Harbor默认http方式，所以当执行用docker login、pull、push等命令操作非https的docker regsitry的时就会报错。  
解决办法：配置`/etc/docker/daemon.json`

```
[root@localhost harbor]# vi /etc/docker/daemon.json 
{
  "registry-mirrors": ["https://3dse7md.mirror.aliyuncs.com"]
}
```

将其修改为：

```
{
  "registry-mirrors": ["https://3dse7md.mirror.aliyuncs.com"],
  "insecure-registries":["*************"]
}
```

### 4. 在每个需要构建子项目的pom.xml下加入配置,内容可参考如下

```
<build>
  <plugins>
    <plugin>
        <groupId>io.fabric8</groupId>
        <artifactId>docker-maven-plugin</artifactId>
        <configuration>
            <skip>${docker.fabric.skip}</skip>
        </configuration>
    </plugin>
  </plugins>
</build>
```

### 5. 在每个需要构建子项目的根目录下加入Dockerfile,内容可参考如下

```
FROM bladex/alpine-java:openjdk8-openj9_cn_slim

MAINTAINER <EMAIL>

RUN mkdir -p /blade/gateway

WORKDIR /blade/gateway

EXPOSE 80

ADD ./target/blade-gateway.jar ./app.jar

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "app.jar"]

CMD ["--spring.profiles.active=test"]
```

### 6. 在工程根目录的docker-compose.yml下加入配置，内容可参考如下
```
blade-gateway:
  image: "${REGISTER}/blade-gateway:${TAG}"
  ports:
  - 80:80
  networks:
    blade_net:
      ipv4_address: ***********
```

### 7. 一切配置完毕，采用fabric插件命令执行构建推送：
```
mvn clean package docker:build docker:push  
```